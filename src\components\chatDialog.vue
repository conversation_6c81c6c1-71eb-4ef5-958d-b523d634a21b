<template>
  <div 
    class="float-panel"
    :style="{ height: `${panelHeight}px` }"
  >
    <img class="float-panel-bg" src="@/assets/image/float_bg1.png" alt="" />
    <div 
      class="content"
    >
      <div class="drag-handle">
        <div class="drag-indicator" />
      </div>
      <div
        class="tips"
        @click="togglePanel"
      >
        <img
          class="tips-icon"
          :class="{ toggle: tips === '点击收起对话' }"
          src="@/assets/image/arrow.png"

        />
        <div class="tips-text">
          {{ `${tips} ` }}
        </div>
      </div>
      <div
        class="chat-list"
        scroll-y
        scroll-with-animation
        ref="chatScroll"
      >
        <div
          v-for="(item, index) in chatList"
          :key="item.chatId"
          class="chat-item"
          :class="{ 'is-admin': item.role === 'admin' }"
          :data-index="index"
        >
          <img class="avatar" round src="@/assets/image/admin_avatar.png" v-if=" item.role === 'admin'"/>
          <img class="avatar" round src="@/assets/image/user.png" v-else />
          <div class="chat-content admin">
            <div class="text">
              {{ `${item.content} ` }}
            </div>
            <div v-if="item.role === 'admin'" class="ai-tips">
              {{
                item.source === "knowledge_base"
                  ? "内容来源于博物馆知识库"
                  : "内容由 deepseek 生成，仅供参考，注意甄别"
              }}
            </div>
          </div>
          <!-- <div v-if="item.audioDuration" class="drop" @tap="handlePlayAudio(item)">
            <img class="bubble" src="@/assets/image/bubble.png" />
            <img src="@/assets/image/play.png" class="play-icon" />
            <div class="duration">
              {{ `${formatTime(item.audioDuration)} ` }}
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { eventBus } from "@/refactored/EventBus";
import { ref, nextTick, computed, watch, onMounted  } from "vue";

const chatScroll = ref(null);
const anchors = [270, 700] // 锚点高度数组

eventBus.on('enterNavigateMode',()=>{
  panelHeight.value = anchors[0]
})
const chatList = ref([
  {
      type: 'text',
      content: '你好呀，我是小娥，欢迎来到湖北省博物馆，希望你在这里玩的开心呀~',
      role: 'admin',
      chatId: Date.now(),
      timestamp: Date.now(),
    },
])
// 浮动面板相关
const panelHeight = ref(270); // 当前面板高度
const tips = computed(() => {
  if (panelHeight.value >= anchors[anchors.length - 1]) {
    return "点击收起对话";
  } else {
    return "点击展开对话";
  }
});

// 切换面板高度
function togglePanel() {
  if (panelHeight.value == 270) {
    panelHeight.value = 700
  } else {
    panelHeight.value = 270
  }
  nextTick(() => {
    scrollToBottom();
  });
}
// 浮动面板相关 end

const { curChatMessage } = defineProps({
  curChatMessage: {
    type: Object,
    default: {}
  }
})
// 滚动到底部函数
const scrollToBottom = () => {
  nextTick(() => {
    if (chatScroll.value) {
      console.log('chatScroll.value.scrollHeight', chatScroll.value.scrollHeight)

      chatScroll.value.scrollTop = chatScroll.value.scrollHeight;
    }
  });
};

watch(() => curChatMessage, (newVal) => {
  console.log('curChatMessage newVal~~~~~', newVal)
  if (newVal) {
    // chatList.value.push(newVal)
    console.log('chat newVal', newVal)
    console.log('chatList.value', chatList.value)

    let isExist = false
    chatList.value = (chatList.value || []).map(item => {
      if (item?.chatId == newVal?.chatId) {
        item.content = newVal.content
        isExist = true
      }
      return item
    })

    if (!isExist) {
      chatList.value.push(newVal)
    }
    nextTick(() => {
      scrollToBottom();
    });
  }
})

</script>


<style lang="scss" scoped>
.float-panel {
  position: fixed;
  bottom: 0px;
  left: 0;
  width: 100%;
  height: 250px;
  background-color: transparent;
  z-index: 1998;
  transition: none;
  /* 禁用默认过渡，使用 JS 动画 */
  overflow: hidden;
  padding-bottom: 120px;

  .float-panel-bg {
    width: 100%;
    position: absolute;
    height: 684px;
    top: 0;
    z-index: -1;
  }

  .content {
    display: flex;
    flex-direction: column;
    height: 100%;

    overflow-x: hidden;

    .tips {
      padding-top: 10px;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 10px;
      height: 32px;

      .tips-icon {
        width: 12px;
        height: 12px;
      }

      .toggle {
        transform: rotate(180deg);
      }

      .tips-text {
        font-family: Source Han Serif CN;
        font-weight: 700;
        font-style: Bold;
        font-size: 8px;
        leading-trim: NONE;
        letter-spacing: 0%;
        text-align: center;
        vertical-align: middle;
        color: rgba(148, 99, 45, 1);
        height: 11px;
        line-height: 11px;
      }
    }

    .chat-list {
      background-color: transparent;
      height: calc(100% - 50px);
      width: 100%;
      box-sizing: border-box;
      padding: 0px 8px;
      margin-top: 8px;
      overflow-y: scroll;
      overflow-x: hidden;

      .chat-item {
        width: 100%;
        display: flex;
        margin-bottom: 8px;
        position: relative;
        flex-direction: row-reverse;

        .avatar {
          flex-shrink: 0;
          width: 32px;
          height: 32px;
          margin-left: 8px;
          margin-top: 12px;
        }

        &.is-admin {
          flex-direction: row;

          .avatar {
            margin-left: 0px;
            margin-right: 8px;
          }
        }

        .chat-content {
          margin-top: 12px;
          background-color: rgba(231, 205, 163, 1);
          color: rgba(51, 51, 51, 0.9);
          padding: 8px;
          max-width: 280px;
          border-radius: 8px;

          .text {
            white-space: pre-wrap;
          }

          .ai-tips {
            font-size: 11px;
            color: rgba(153, 153, 153, 0.8);
            margin-top: 4px;
          }
        }
      }

      .chat-item.is-admin .chat-content {
        max-width: 280px;
        background-color: #fff;
        flex: 1;
        position: relative;
        padding: 12px 8px 12px 8px; // 增加底部内边距
        border-radius: 8px;
        font-size: 14px;

        .text {
          white-space: pre-wrap;
          word-wrap: break-word;
          word-break: break-all;
          line-height: 18px; // 设置合适的行高
          min-height: 20px; // 确保最小高度

          // 确保文字完整显示
          overflow: visible;
          display: block;
        }
      }

      .drop {
        box-sizing: border-box;
        width: 72px;
        height: 32px;
        padding: 2px 8px 2px 4px;
        display: flex;
        font-size: 12px;
        color: #fff;
        position: absolute;
        line-height: 18px;
        top: 0px;
        left: 40px;

        .play-icon {
          width: 18px;
          height: 18px;
          margin-right: 2px;
          position: absolute;
        }

        .duration {
          position: absolute;
          left: 24px;
        }

        .bubble {
          width: 72px;
          height: 32px;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 0;
        }
      }
    }
  }

  /* 状态指示器样式 */
  .ai-status {
    width: 200px;
    position: fixed;
    top: 100rpx;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 122, 255, 0.9);
    color: white;
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    z-index: 1001;
    backdrop-filter: blur(10rpx);
  }

  .audio-status {
    position: fixed;
    top: 140rpx;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 107, 53, 0.9);
    color: white;
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    z-index: 1001;
    backdrop-filter: blur(10rpx);
  }

  .recognition-result {
    position: fixed;
    bottom: 200rpx;
    left: 30rpx;
    right: 30rpx;
    background: rgba(255, 255, 255, 0.95);
    padding: 20rpx;
    border-radius: 15rpx;
    font-size: 28rpx;
    color: #333;
    z-index: 1000;
  }

  .connection-status {
    position: fixed;
    top: 180rpx;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 59, 48, 0.9);
    color: white;
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    z-index: 1001;
    backdrop-filter: blur(10rpx);
  }
}
</style>
