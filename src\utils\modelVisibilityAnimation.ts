/*
 * @Author: Gemini
 * @des: 使用gsap实现对模型的渐显/隐动画
 * @Date: 2025-08-29 09:37:27 
 */
import * as THREE from "three"
import { gsap } from "gsap"

const win = (window as any)
// new - building - show - animation
export const buildingAnimation: Map<string, {
  type: 0 | 1,
  visible: boolean
  gsapAnimation: any
}> = (new Map())
// 引用计数器
let activeAnimationsCount = 0;
let repaintIsActive = false;
// 主体盒子 - ids
const boxIds = ['EastMuseum-exterior', 'WestMuseum-exterior', 'NorthMuseum-exterior', 'SouthMuseum-exterior']

// 箭头函数确保 this 指向正确
const repaintFunction = () => {
  if (win.mapApp && win.mapApp.map) {
    win.mapApp.map.triggerRepaint();
  }
};

/**
 * 启动重绘
 */
const startRepaint = () => {
  if (!repaintIsActive) {
    gsap.ticker.add(repaintFunction);
    repaintIsActive = true;
  }
}

/**
 * 停止重绘
 */
const stopRepaint = () => {
  if (repaintIsActive) {
    repaintIsActive = false;
    buildingAnimation.clear()
    setTimeout(() => {
      gsap.ticker.remove(repaintFunction);
    }, 100)
  }
}

/**
 * 手动控制 - 停止重绘
 */
const handleStopRepaint = () => {
  activeAnimationsCount--;
  if (activeAnimationsCount <= 0) stopRepaint();
}

/**
 * 状态判断是否需要继续进行动画
 */
export const checkAnimation = (object: THREE.Object3D, visible: boolean, id: string) => {
  const _modelAnimation = buildingAnimation.get(id)
  if (_modelAnimation && (_modelAnimation.visible == visible)) {
    return false;
  }
  // 先移除前面的动画
  if (_modelAnimation && _modelAnimation.gsapAnimation) {
    _modelAnimation.gsapAnimation.kill()
    handleStopAnimation(object, _modelAnimation.visible)
    id && buildingAnimation.delete(id)
  }
  return true
}

// 判断 - 当前加载的动画里面是否包含 - 主体的动画
const checkIsBoxId = () => {
  for (let i = 0; i < boxIds.length; i++) {
    const _modelAnimation = buildingAnimation.get(boxIds[i])
    if (_modelAnimation) return true
  }
  return false
}

/**
 * 渐显或渐隐动画函数 - 修复版本
 * * @param object 要执行动画的 Three.js 对象
 * @param duration 动画持续时间
 * @param visible 目标状态，true 为渐显，false 为渐隐
 * @param id 可选，用于管理动画的唯一标识符
 */
export const fadeInOrFadeOut = (object: THREE.Object3D, duration: number, visible: boolean, id?: string) => {
  console.log(`${visible ? "👁️ 显示" : "🙈 隐藏"}模型: ${id}`);

  // 找到所有可动画的材质
  const animatableMaterials: THREE.Material[] = [];
  object.traverse((child) => {
    if (child instanceof THREE.Mesh && child.isMesh && child.material) {
      const materials = Array.isArray(child.material) ? child.material : [child.material];
      materials.forEach(mat => {
        // 确保材质可以透明
        if ('opacity' in mat) {
          animatableMaterials.push(mat as THREE.Material);
        }
      });
    }
  });

  // 关键步骤：在动画开始前设置 Three.js 材质属性
  animatableMaterials.forEach(mat => {
    // 必须启用 transparent 属性，否则 opacity 属性无效
    (mat as THREE.Material & { transparent: boolean }).transparent = true;
    // 移除 alphaMap，避免干扰 opacity 动画
    (mat as THREE.Material & { alphaMap: null }).alphaMap = null;

    // 设置动画的初始状态
    (mat as THREE.Material & { opacity: number }).opacity = visible ? 0 : 1;
    mat.needsUpdate = true;
  });

  // 渲染顺序调整
  if (boxIds.includes(id!)) {
    object.renderOrder = 1;
  } else if (!checkIsBoxId()) {
    object.renderOrder = !visible ? 1 : 0;
  }

  const timeline = gsap.timeline({
    onStart: () => {
      // activeAnimationsCount++;
      object.visible = true;
      // startRepaint();
    },
    onComplete: () => {
      if (!visible) {
        object.visible = false;
      }

      // 动画完成后，将材质恢复到不透明状态以优化性能
      animatableMaterials.forEach(mat => {
        (mat as THREE.Material & { transparent: boolean }).transparent = false;
        (mat as THREE.Material & { opacity: number }).opacity = 1;
        mat.needsUpdate = true;
      });

      // handleStopRepaint();
      object.renderOrder = 0;
      id && buildingAnimation.delete(id);
    }
  });

  timeline.to(animatableMaterials, {
    opacity: visible ? 1 : 0,
    duration: duration,
    ease: 'power2.inOut'
  });

  if (id) {
    buildingAnimation.set(id, {
      type: 0,
      visible: visible,
      gsapAnimation: timeline
    });
  }
}

// 手动重置 模型动画
export const handleStopAnimation = (object: THREE.Object3D, visible: boolean) => {
  const animatableMaterials: THREE.Material[] = [];
  object.traverse((child) => {
    if (child instanceof THREE.Mesh && child.isMesh && child.material) {
      const materials = Array.isArray(child.material) ? child.material : [child.material];
      materials.forEach(mat => {
        if ('opacity' in mat) {
          animatableMaterials.push(mat as THREE.Material);
        }
      });
    }
  });

  animatableMaterials.forEach(mat => {
    (mat as THREE.Material & { opacity: number }).opacity = visible ? 1 : 0;
    (mat as THREE.Material & { transparent: boolean }).transparent = false;
    mat.needsUpdate = true;
  });

  object.visible = visible
  object.renderOrder = 0;
}