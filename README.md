# MapViewer - 3D地图查看器

一个基于Three.js和Vue 3的3D地图查看器，支持图层管理和标注功能。

## 功能特性

### 图层管理
- 支持加载3D建筑模型（.obj/.mtl格式）
- 图层树形结构管理
- 图层透明度、位置、旋转、缩放调整
- 场景整体变换控制

### 标注管理 ✨ 新增功能
- **点标注**：在地图上添加标记点
- **线标注**：绘制路径或边界线
- **面标注**：绘制区域多边形
- **标注属性**：
  - 自定义名称
  - 颜色选择（支持透明度）
  - 自动生成唯一ID
- **标注操作**：
  - 编辑标注名称和颜色
  - 删除单个标注
  - 清空所有标注
- **数据导出**：
  - 导出为二维GeoJSON格式（使用threeToLngLat转换坐标）
  - 线段和点标注分别导出为两个文件
  - 包含完整的几何信息和属性
  - 自动计算线段长度、多边形面积和周长
  - 支持文件下载
- **界面优化**：
  - 现代化标注列表设计
  - 图标化标注类型显示
  - 悬停效果和动画
  - 更好的视觉层次
- **显示效果**：
  - 修复meshline双面显示问题
  - 添加发光和混合效果
  - 改进点标注和多边形显示

## 使用方法

### 添加标注
1. 切换到"标注管理"标签页
2. 点击"添加点"、"添加线"或"添加面"按钮
3. 在弹出的对话框中设置标注名称和颜色
4. 在地图上点击添加标注：
   - **点标注**：单击一次完成
   - **线标注**：单击添加点，右键或双击结束
   - **面标注**：单击添加点，右键或双击结束（至少3个点）

### 管理标注
- 在标注列表中查看所有标注
- 点击"编辑"按钮修改标注属性
- 点击"删除"按钮移除标注
- 使用"导出"按钮下载标注数据

### 导出数据
导出的GeoJSON文件包含：
- 标注的几何信息（二维坐标，经度纬度）
- 属性信息（ID、名称、颜色、类型）
- 计算属性（长度、面积、周长）
- 元数据（创建时间、导出时间、统计信息）

**导出文件说明**：
- `lines_and_polygons_时间戳.geojson`：包含所有线段和面标注
- `points_时间戳.geojson`：包含所有点标注

## 技术栈

- **前端框架**：Vue 3 + TypeScript
- **3D引擎**：Three.js
- **UI组件**：Element Plus
- **构建工具**：Vite
- **包管理**：pnpm

## 开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

## 项目结构

```
src/
├── components/
│   ├── MapViewer.vue          # 主地图组件
│   └── LayerManagerPanel.vue  # 图层和标注管理面板
├── lib/
│   ├── MapApplication.ts      # 地图应用主类
│   ├── MapMarker.ts          # 标注管理器
│   ├── LayerManager.ts       # 图层管理器
│   └── tiandituConfig.ts     # 天地图配置
└── main.ts                   # 应用入口
```

## 标注数据格式

导出的二维GeoJSON格式示例：

```json
{
  "type": "FeatureCollection",
  "properties": {
    "name": "点标注数据",
    "description": "从MapViewer导出的点标注数据（二维）",
    "exportTime": "2024-01-01T12:00:00.000Z",
    "totalFeatures": 1,
    "featureTypes": {
      "point": 1
    }
  },
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [114.359972, 30.563739]
      },
      "properties": {
        "id": "point_1234567890",
        "name": "重要位置",
        "color": "#ff0000",
        "type": "point",
        "createdAt": "2024-01-01T12:00:00.000Z",
        "position": {
          "lng": 114.359972,
          "lat": 30.563739,
          "x": 10,
          "y": 20,
          "z": 0
        }
      }
    }
  ]
}
```
