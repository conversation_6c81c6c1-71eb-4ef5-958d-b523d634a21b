
// window.apiURL = "http://10.100.201.91:8003/api/"; // 测试环境
window.apiURL = 'https://aidl.hbww.org.cn/api/'; // 正式环境

// 客流量api地址
// window.FlowDataUrl = "https://pcticket.hbww.org.cn/api/kl?p=w&language=1"
window.FlowDataUrl = "https://aidl.hbww.org.cn/api/kl?p=w&language=1"
// 客流量数据获取频率
window.FlowDataTimer = 60;// 5秒每次
window.modelUrl = "https://das-future-map-1301434080.cos.ap-nanjing.myqcloud.com/shengbo/scene/";

// window.iconUrl = "http://10.100.5.49:5173";// 5秒每次
window.iconUrl = "https://aidl.hbww.org.cn";// 5秒每次

// 导航时超出范围阈值，默认10米
window.NavigationLimitDistance = 10;
// 导航时超出范围持续时间阈值，默认30秒
window.NavigationLimitTime = 30;
// 是否开启导航超出范围重新绘制逻辑
window.isOpenNavigationRestart = false;
// 是否开启调试信息
window.isOpenDebugger = true;
// 调用后端更新位置频率,默认3秒一次
window.updateUserPosServerTimer = 1;