/**
 * 轨迹平滑处理工具
 * 解决轨迹点更新不平滑的问题
 */

/**
 * 轨迹平滑器
 */
export class TrajectorySmoothing {
  constructor(options = {}) {
    this.config = {
      // 平滑参数
      positionSmoothing: options.positionSmoothing || 0.3,
      headingSmoothing: options.headingSmoothing || 0.2,
      velocitySmoothing: options.velocitySmoothing || 0.4,
      
      // 插值参数
      interpolationSteps: options.interpolationSteps || 3,
      maxInterpolationDistance: options.maxInterpolationDistance || 2.0,
      
      // 质量控制
      minConfidenceForSmoothing: options.minConfidenceForSmoothing || 0.3,
      maxSmoothingDistance: options.maxSmoothingDistance || 5.0,
      
      // 自适应参数
      adaptiveSmoothing: options.adaptiveSmoothing !== false,
      movingAverageWindow: options.movingAverageWindow || 5
    };
    
    // 历史数据
    this.history = {
      positions: [],
      headings: [],
      velocities: [],
      timestamps: []
    };
    
    // 插值状态
    this.interpolationState = {
      isInterpolating: false,
      targetPosition: null,
      currentStep: 0,
      totalSteps: 0,
      startPosition: null
    };
  }
  
  /**
   * 处理位置更新，返回平滑后的位置
   * @param {Object} newPosition - 新位置数据
   * @returns {Object} 平滑后的位置数据
   */
  processPosition(newPosition) {
    console.log('🔍 TrajectorySmoothing.processPosition 输入:', newPosition);

    if (!newPosition) {
      console.log('🔍 TrajectorySmoothing: 输入为空，返回null');
      return null;
    }

    // 添加到历史记录
    this.addToHistory(newPosition);
    console.log('🔍 TrajectorySmoothing: 历史记录长度:', this.history.positions.length);

    // 如果是第一个位置，直接返回
    if (this.history.positions.length === 1) {
      console.log('🔍 TrajectorySmoothing: 第一个位置，直接返回');
      return { ...newPosition };
    }
    
    const lastPosition = this.history.positions[this.history.positions.length - 2];
    
    // 计算位移距离
    const distance = this.calculateDistance(newPosition, lastPosition);
    
    // 如果距离太大，可能是跳跃，需要插值
    if (distance > this.config.maxInterpolationDistance) {
      return this.handleLargeJump(lastPosition, newPosition);
    }
    
    // 应用平滑处理
    return this.applySmoothing(newPosition, lastPosition);
  }
  
  /**
   * 添加到历史记录
   */
  addToHistory(position) {
    this.history.positions.push({ ...position });
    this.history.headings.push(position.heading || 0);
    this.history.velocities.push(position.velocity || 0);
    this.history.timestamps.push(position.timestamp || Date.now());
    
    // 限制历史长度
    const maxHistory = this.config.movingAverageWindow * 2;
    if (this.history.positions.length > maxHistory) {
      this.history.positions.shift();
      this.history.headings.shift();
      this.history.velocities.shift();
      this.history.timestamps.shift();
    }
  }
  
  /**
   * 处理大幅跳跃
   */
  handleLargeJump(lastPosition, newPosition) {
    // 启动插值模式
    this.interpolationState = {
      isInterpolating: true,
      startPosition: { ...lastPosition },
      targetPosition: { ...newPosition },
      currentStep: 0,
      totalSteps: this.config.interpolationSteps
    };
    
    // 返回第一个插值点
    return this.getNextInterpolatedPosition();
  }
  
  /**
   * 获取下一个插值位置
   */
  getNextInterpolatedPosition() {
    if (!this.interpolationState.isInterpolating) {
      return null;
    }
    
    const { startPosition, targetPosition, currentStep, totalSteps } = this.interpolationState;
    const progress = currentStep / totalSteps;
    
    // 线性插值位置
    const interpolatedPosition = {
      x: startPosition.x + (targetPosition.x - startPosition.x) * progress,
      y: startPosition.y + (targetPosition.y - startPosition.y) * progress,
      z: startPosition.z + (targetPosition.z - startPosition.z) * progress,
      heading: this.interpolateAngle(startPosition.heading, targetPosition.heading, progress),
      velocity: startPosition.velocity + (targetPosition.velocity - startPosition.velocity) * progress,
      confidence: Math.min(startPosition.confidence, targetPosition.confidence) * 0.8, // 降低插值点置信度
      timestamp: Date.now(),
      source: 'interpolated'
    };
    
    this.interpolationState.currentStep++;
    
    // 检查是否完成插值
    if (this.interpolationState.currentStep >= totalSteps) {
      this.interpolationState.isInterpolating = false;
    }
    
    return interpolatedPosition;
  }
  
  /**
   * 应用平滑处理
   */
  applySmoothing(newPosition, lastPosition) {
    const confidence = newPosition.confidence || 0.5;
    
    // 如果置信度太低，不进行平滑
    if (confidence < this.config.minConfidenceForSmoothing) {
      return { ...newPosition };
    }
    
    // 计算自适应平滑系数
    const smoothingFactors = this.calculateAdaptiveSmoothingFactors(newPosition, lastPosition);
    
    // 应用位置平滑
    const smoothedPosition = {
      ...newPosition,
      x: lastPosition.x + (newPosition.x - lastPosition.x) * (1 - smoothingFactors.position),
      y: lastPosition.y + (newPosition.y - lastPosition.y) * (1 - smoothingFactors.position),
      z: lastPosition.z + (newPosition.z - lastPosition.z) * (1 - smoothingFactors.position),
      heading: this.smoothAngle(lastPosition.heading, newPosition.heading, smoothingFactors.heading),
      velocity: lastPosition.velocity + (newPosition.velocity - lastPosition.velocity) * (1 - smoothingFactors.velocity),
      source: 'smoothed'
    };
    
    return smoothedPosition;
  }
  
  /**
   * 计算自适应平滑系数
   */
  calculateAdaptiveSmoothingFactors(newPosition, lastPosition) {
    let positionSmoothing = this.config.positionSmoothing;
    let headingSmoothing = this.config.headingSmoothing;
    let velocitySmoothing = this.config.velocitySmoothing;
    
    if (this.config.adaptiveSmoothing) {
      // 基于位移距离调整
      const distance = this.calculateDistance(newPosition, lastPosition);
      const distanceFactor = Math.min(1.0, distance / 1.0); // 1米以上减少平滑
      
      // 基于航向变化调整
      const headingDiff = Math.abs(this.normalizeAngleDiff(newPosition.heading - lastPosition.heading));
      const headingFactor = Math.min(1.0, headingDiff / 10.0); // 10度以上减少平滑
      
      // 基于速度变化调整
      const velocityDiff = Math.abs(newPosition.velocity - lastPosition.velocity);
      const velocityFactor = Math.min(1.0, velocityDiff / 0.5); // 0.5m/s以上减少平滑
      
      positionSmoothing *= (1 - distanceFactor * 0.5);
      headingSmoothing *= (1 - headingFactor * 0.5);
      velocitySmoothing *= (1 - velocityFactor * 0.5);
    }
    
    return {
      position: positionSmoothing,
      heading: headingSmoothing,
      velocity: velocitySmoothing
    };
  }
  
  /**
   * 平滑角度
   */
  smoothAngle(lastAngle, newAngle, smoothingFactor) {
    const angleDiff = this.normalizeAngleDiff(newAngle - lastAngle);
    return this.normalizeAngle(lastAngle + angleDiff * (1 - smoothingFactor));
  }
  
  /**
   * 插值角度
   */
  interpolateAngle(startAngle, endAngle, progress) {
    const angleDiff = this.normalizeAngleDiff(endAngle - startAngle);
    return this.normalizeAngle(startAngle + angleDiff * progress);
  }
  
  /**
   * 计算两点间距离
   */
  calculateDistance(pos1, pos2) {
    return Math.sqrt(
      Math.pow(pos1.x - pos2.x, 2) + 
      Math.pow(pos1.y - pos2.y, 2) + 
      Math.pow(pos1.z - pos2.z, 2)
    );
  }
  
  /**
   * 标准化角度差值
   */
  normalizeAngleDiff(angleDiff) {
    while (angleDiff > 180) angleDiff -= 360;
    while (angleDiff < -180) angleDiff += 360;
    return angleDiff;
  }
  
  /**
   * 标准化角度
   */
  normalizeAngle(angle) {
    while (angle < 0) angle += 360;
    while (angle >= 360) angle -= 360;
    return angle;
  }
  
  /**
   * 获取移动平均位置
   */
  getMovingAveragePosition() {
    if (this.history.positions.length < this.config.movingAverageWindow) {
      return null;
    }
    
    const recentPositions = this.history.positions.slice(-this.config.movingAverageWindow);
    
    const avgPosition = {
      x: recentPositions.reduce((sum, pos) => sum + pos.x, 0) / recentPositions.length,
      y: recentPositions.reduce((sum, pos) => sum + pos.y, 0) / recentPositions.length,
      z: recentPositions.reduce((sum, pos) => sum + pos.z, 0) / recentPositions.length,
      heading: this.calculateCircularMean(recentPositions.map(pos => pos.heading)),
      velocity: recentPositions.reduce((sum, pos) => sum + pos.velocity, 0) / recentPositions.length,
      confidence: recentPositions.reduce((sum, pos) => sum + pos.confidence, 0) / recentPositions.length,
      timestamp: Date.now(),
      source: 'moving_average'
    };
    
    return avgPosition;
  }
  
  /**
   * 计算圆形平均值（用于角度）
   */
  calculateCircularMean(angles) {
    let sinSum = 0;
    let cosSum = 0;
    
    for (const angle of angles) {
      const rad = (angle * Math.PI) / 180;
      sinSum += Math.sin(rad);
      cosSum += Math.cos(rad);
    }
    
    const meanRad = Math.atan2(sinSum / angles.length, cosSum / angles.length);
    return this.normalizeAngle((meanRad * 180) / Math.PI);
  }
  
  /**
   * 重置平滑器状态
   */
  reset() {
    this.history = {
      positions: [],
      headings: [],
      velocities: [],
      timestamps: []
    };
    
    this.interpolationState = {
      isInterpolating: false,
      targetPosition: null,
      currentStep: 0,
      totalSteps: 0,
      startPosition: null
    };
  }
  
  /**
   * 获取平滑器状态
   */
  getStatus() {
    return {
      historyLength: this.history.positions.length,
      isInterpolating: this.interpolationState.isInterpolating,
      interpolationProgress: this.interpolationState.isInterpolating ? 
        this.interpolationState.currentStep / this.interpolationState.totalSteps : 0,
      config: { ...this.config }
    };
  }
}
