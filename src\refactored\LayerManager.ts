import * as THREE from "three"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FB<PERSON><PERSON>oa<PERSON> } from "three/examples/jsm/Addons.js"
import * as BufferGeometryUtils from "three/examples/jsm/utils/BufferGeometryUtils.js"
import {
	type ModelNodeData,
	type ModelTreeManager,
	type BuildingExteriorNode,
	type BuildingFloorNode,
	type ModelNode,
	type RuntimeModelData,
	type ModelResource,
	ModelNodeType,
	ModelLoadState,
} from "./ModelTypes"
import { eventBus } from "./EventBus"
import gsap from "gsap"
// import { ShadowConfigUtils } from "./ShadowConfig"
// 动画 - 显影
import { fadeInOrFadeOut } from '@/utils/modelVisibilityAnimation'

/**
 * 加载进度回调
 */
export type LoadProgressCallback = (nodeId: string, progress: number) => void

/**
 * 加载完成回调
 */
export type LoadCompleteCallback = (
	nodeId: string,
	object3D: THREE.Object3D,
	success: boolean,
	error?: string
) => void

/**
 * 图层管理器 - 基于模型数据类型的实现
 */
export class LayerManager {
	private scene: THREE.Scene
	private treeManager: ModelTreeManager
	private runtimeData: Map<string, RuntimeModelData> = new Map()
	private mtlLoader: MTLLoader = new MTLLoader()
	private objLoader: OBJLoader = new OBJLoader()
	private fbxLoader: FBXLoader = new FBXLoader()
	private loadingQueue: Set<string> = new Set()
	private currentAnimationInfo = {
		buildingId: "",
		targetVisible: false,
		onComplete: null,
		tween: null
	}

	constructor(scene: THREE.Scene, treeManager: ModelTreeManager) {
		this.scene = scene
		this.treeManager = treeManager
		this.setupLoaders()
		eventBus.on("set-node-visibility", this.setNodeVisibility.bind(this))
	}

	/**
	 * 设置加载器
	 */
	private setupLoaders(): void {
		// 可以在这里配置加载器的基础路径等
		// this.mtlLoader.setPath('/models/')
		// this.objLoader.setPath('/models/')
		// this.fbxLoader.setPath('/models/')

		// 配置FBX加载器的管理器
		this.fbxLoader.manager.onLoad = () => {
			console.log("🎯 FBX模型加载完成")
			eventBus.emit("modelLoaded")
		}

		this.fbxLoader.manager.onError = (url) => {
			console.error("❌ FBX模型加载失败:", url)
		}
	}

	/**
	 * 加载单个节点
	 */
	async loadNode(
		nodeId: string,
		onProgress?: LoadProgressCallback,
		onComplete?: LoadCompleteCallback
	): Promise<THREE.Object3D | null> {
		const nodeData = this.treeManager.getNodeById(nodeId)
		if (!nodeData) {
			throw new Error(`Node ${nodeId} not found`)
		}

		// 检查是否已经在加载中
		if (this.loadingQueue.has(nodeId)) {
			console.warn(`Node ${nodeId} is already loading`)
			return null
		}

		// 检查是否已经加载
		const existing = this.runtimeData.get(nodeId)
		if (existing?.object3D) {
			return existing.object3D
		}

		// 场景根节点不需要加载
		if (nodeData.type === ModelNodeType.SCENE) {
			return null
		}

		this.loadingQueue.add(nodeId)

		try {
			// 更新节点状态为加载中
			this.treeManager.updateNode(nodeId, {
				loadState: ModelLoadState.LOADING,
			})

			// 初始化运行时数据
			const runtimeData: RuntimeModelData = {
				nodeData,
				loadProgress: 0,
				object3D: undefined,
			}
			this.runtimeData.set(nodeId, runtimeData)

			// 根据节点类型加载模型
			let object3D: THREE.Object3D | null = null

			switch (nodeData.type) {
				case ModelNodeType.BUILDING_EXTERIOR:
				case ModelNodeType.BUILDING_FLOOR:
				case ModelNodeType.MODEL:
					object3D = await this.loadModelFromResource(
						nodeData as
						| BuildingExteriorNode
						| BuildingFloorNode
						| ModelNode,
						(progress) => {
							runtimeData.loadProgress = progress
							onProgress?.(nodeId, progress)
							eventBus.emit("model-load-progress", {
								nodeId,
								progress,
							})
						}
					)
					break
				case ModelNodeType.GROUP:
					object3D = new THREE.Group()
					object3D.name = nodeData.name
					break
				default:
					throw new Error(
						`Unsupported node type: ${(nodeData as any).type}`
					)
			}

			if (object3D) {
				// 应用变换
				this.applyTransform(object3D, nodeData)

				// 🔧 修复：室内模型z轴偏移，避免z-fighting
				if (nodeData.type === ModelNodeType.BUILDING_FLOOR) {
					object3D.position.y += 0.05
					console.log(
						`🏢 室内模型z轴偏移: ${nodeData.name}, z += 0.05`
					)
				}

				// 设置可见性
				object3D.visible = nodeData.visible

				// 添加到场景或父对象
				this.addToParent(object3D, nodeData)

				// 更新运行时数据
				runtimeData.object3D = object3D
				runtimeData.loadProgress = 1

				// 更新节点状态
				this.treeManager.updateNode(nodeId, {
					loadState: ModelLoadState.LOADED,
				})

				// 触发事件
				eventBus.emit("model-loaded", { nodeId, object3D, nodeData })
				onComplete?.(nodeId, object3D, true)

				console.log(`Successfully loaded model: ${nodeData.name}`)
				return object3D
			} else {
				throw new Error("Failed to create 3D object")
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unknown error"

			// 更新运行时数据
			const runtimeData = this.runtimeData.get(nodeId)
			if (runtimeData) {
				runtimeData.error = errorMessage
			}

			// 更新节点状态
			this.treeManager.updateNode(nodeId, {
				loadState: ModelLoadState.ERROR,
			})

			// 触发事件
			eventBus.emit("model-load-error", {
				nodeId,
				error: errorMessage,
				nodeData,
			})
			onComplete?.(nodeId, new THREE.Object3D(), false, errorMessage)

			console.error(`Failed to load model ${nodeData.name}:`, error)
			return null
		} finally {
			this.loadingQueue.delete(nodeId)
		}
	}

	/**
	 * 从资源加载模型
	 */
	private async loadModelFromResource(
		nodeData: BuildingExteriorNode | BuildingFloorNode | ModelNode,
		onProgress?: (progress: number) => void
	): Promise<THREE.Object3D> {
		const { resource } = nodeData

		// 自动检测模型类型
		const modelType = this.detectModelType(resource)

		// console.log(`🔄 加载${modelType.toUpperCase()}模型: ${nodeData.name}`)

		switch (modelType) {
			case "fbx":
				return this.loadFBXModel(resource, nodeData, onProgress)
			case "obj":
				return this.loadOBJModel(resource, nodeData, onProgress)
			case "gltf":
			case "glb":
				return this.loadGLTFModel(resource, nodeData, onProgress)
			default:
				throw new Error(`不支持的模型格式: ${modelType}`)
		}
	}

	/**
	 * 检测模型类型
	 */
	private detectModelType(resource: ModelResource): string {
		// 优先使用明确指定的类型
		if (resource.type) {
			return resource.type
		}

		// 根据文件路径自动检测
		if (resource.fbxPath) {
			return "fbx"
		}
		if (resource.gltfPath) {
			return resource.gltfPath.toLowerCase().endsWith(".glb")
				? "glb"
				: "gltf"
		}
		if (resource.objPath) {
			return "obj"
		}

		throw new Error(
			"无法检测模型类型，请指定resource.type或提供相应的文件路径"
		)
	}

	/**
	 * 加载FBX模型
	 */
	private loadFBXModel(
		resource: ModelResource,
		nodeData: BuildingExteriorNode | BuildingFloorNode | ModelNode,
		onProgress?: (progress: number) => void
	): Promise<THREE.Object3D> {
		console.log(resource)

		return new Promise((resolve, reject) => {
			if (!resource.fbxPath) {
				reject(new Error("FBX文件路径未指定"))
				return
			}

			// console.log(`📦 开始加载FBX模型: ${resource.fbxPath}`)

			this.fbxLoader.load(
				resource.fbxPath,
				(object) => {
					// console.log(`✅ FBX模型加载成功: ${nodeData.name}`)

					// 应用基础设置
					this.setupLoadedModel(object, nodeData, resource)

					onProgress?.(1.0)
					resolve(object)
				},
				(xhr) => {
					if (xhr.lengthComputable) {
						const progress = xhr.loaded / xhr.total
						onProgress?.(progress)
						// console.log(
						// 	`📊 FBX加载进度: ${(progress * 100).toFixed(1)}%`
						// )
					}
				},
				(error) => {
					console.error(
						`❌ FBX模型加载失败: ${resource.fbxPath}`,
						error
					)
					reject(new Error(`Failed to load FBX: ${error}`))
				}
			)
		})
	}

	/**
	 * 加载OBJ模型（保持原有逻辑）
	 */
	private loadOBJModel(
		resource: ModelResource,
		nodeData: BuildingExteriorNode | BuildingFloorNode | ModelNode,
		onProgress?: (progress: number) => void
	): Promise<THREE.Object3D> {
		return new Promise((resolve, reject) => {
			if (!resource.objPath) {
				reject(new Error("OBJ文件路径未指定"))
				return
			}

			// console.log(`📦 开始加载OBJ模型: ${resource.objPath}`)

			// 如果有MTL文件，先加载材质
			if (resource.mtlPath) {
				this.mtlLoader.load(
					resource.mtlPath,
					(materials) => {
						materials.preload()
						onProgress?.(0.3)

						this.objLoader.setMaterials(materials)
						this.loadOBJFile(
							resource.objPath!,
							nodeData,
							resource,
							onProgress,
							resolve,
							reject
						)
					},
					(xhr) => {
						if (xhr.lengthComputable) {
							const progress = (xhr.loaded / xhr.total) * 0.3
							onProgress?.(progress)
						}
					},
					(error) => {
						console.warn(`⚠️ MTL加载失败，继续加载OBJ: ${error}`)
						this.loadOBJFile(
							resource.objPath!,
							nodeData,
							resource,
							onProgress,
							resolve,
							reject
						)
					}
				)
			} else {
				this.loadOBJFile(
					resource.objPath,
					nodeData,
					resource,
					onProgress,
					resolve,
					reject
				)
			}
		})
	}

	/**
	 * 加载OBJ文件
	 */
	private loadOBJFile(
		objPath: string,
		nodeData: BuildingExteriorNode | BuildingFloorNode | ModelNode,
		resource: ModelResource,
		onProgress?: (progress: number) => void,
		resolve?: (object: THREE.Object3D) => void,
		reject?: (error: Error) => void
	): void {
		this.objLoader.load(
			objPath,
			(object) => {
				// console.log(`✅ OBJ模型加载成功: ${nodeData.name}`)
				this.setupLoadedModel(object, nodeData, resource)
				onProgress?.(1.0)
				resolve?.(object)
			},
			(xhr) => {
				if (xhr.lengthComputable) {
					const baseProgress = resource.mtlPath ? 0.3 : 0
					const progress =
						baseProgress +
						(xhr.loaded / xhr.total) * (1 - baseProgress)
					onProgress?.(progress)
				}
			},
			(error) => {
				console.error(`❌ OBJ模型加载失败: ${objPath}`, error)
				reject?.(new Error(`Failed to load OBJ: ${error}`))
			}
		)
	}

	/**
	 * 加载GLTF/GLB模型（预留接口）
	 */
	private loadGLTFModel(
		resource: ModelResource,
		nodeData: BuildingExteriorNode | BuildingFloorNode | ModelNode,
		onProgress?: (progress: number) => void
	): Promise<THREE.Object3D> {
		return new Promise((resolve, reject) => {
			// TODO: 实现GLTF/GLB加载器
			reject(new Error("GLTF/GLB加载器尚未实现"))
		})
	}

	/**
	 * 为模型设置阴影和材质优化
	 */
	private setupShadowsForModel(
		object: THREE.Object3D,
		nodeData: BuildingExteriorNode | BuildingFloorNode | ModelNode
	): void {
		object.traverse((child) => {
			if (child instanceof THREE.Mesh) {
				// 🔧 修正阴影逻辑：根据模型类型设置阴影
				if (
					nodeData.type === ModelNodeType.BUILDING_EXTERIOR ||
					nodeData.type === ModelNodeType.MODEL
				) {
					// 外立面和地形模型：投射和接收阴影
					child.castShadow = true
					child.receiveShadow = true
				} else if (nodeData.type === ModelNodeType.BUILDING_FLOOR) {
				} else {
					// 其他类型模型：默认不启用阴影
					child.castShadow = false
					child.receiveShadow = false
				}

				// 优化材质以减少摩尔纹
				// if (child.material) {
				// 	if (Array.isArray(child.material)) {
				// 		child.material.forEach((material) => {
				// 			this.optimizeMaterialForMoire(material)
				// 		})
				// 	} else {
				// 		this.optimizeMaterialForMoire(child.material)
				// 	}
				// }
			}
		})

		// console.log(
		// 	`🌑 设置阴影属性: ${nodeData.name} (${nodeData.type
		// 	}) - castShadow: ${nodeData.type === ModelNodeType.BUILDING_EXTERIOR ||
		// 	nodeData.type === ModelNodeType.MODEL
		// 	}, receiveShadow: ${nodeData.type === ModelNodeType.BUILDING_EXTERIOR ||
		// 	nodeData.type === ModelNodeType.BUILDING_FLOOR ||
		// 	nodeData.type === ModelNodeType.MODEL
		// 	}`
		// )
	}

	/**
	 * 优化材质以减少摩尔纹
	 */
	// 设置纹理过滤以减少摩尔纹
	private optimizeMaterialForMoire(material: THREE.Material): void {
		if (
			(material as any).map &&
			(material as any).map instanceof THREE.Texture
		) {
			const texture = (material as any).map as THREE.Texture
			texture.generateMipmaps = true
			texture.minFilter = THREE.LinearMipmapLinearFilter
			texture.magFilter = THREE.LinearFilter
			texture.anisotropy = 4
		}

		// 设置法线贴图过滤
		if (
			(material as any).normalMap &&
			(material as any).normalMap instanceof THREE.Texture
		) {
			const normalMap = (material as any).normalMap as THREE.Texture
			normalMap.generateMipmaps = true
			normalMap.minFilter = THREE.LinearMipmapLinearFilter
			normalMap.magFilter = THREE.LinearFilter
		}

		// 启用材质的深度写入和测试
		material.depthWrite = true
		material.depthTest = true
	}

	/**
	 * 优化模型几何体 - 合并几何体组
	 */
	private optimizeModelGeometry(
		object: THREE.Object3D,
		nodeData: BuildingExteriorNode | BuildingFloorNode | ModelNode
	): void {
		// console.log(`🔧 开始优化模型几何体: ${nodeData.name}`)

		let optimizedCount = 0
		let totalMeshes = 0
		const startTime = performance.now()

		// 遍历所有子对象
		object.traverse((child) => {
			if (
				child instanceof THREE.Mesh &&
				child.geometry instanceof THREE.BufferGeometry
			) {
				totalMeshes++

				// 检查几何体是否有groups
				if (child.geometry.groups && child.geometry.groups.length > 1) {
					try {
						const vertexCount =
							child.geometry.attributes.position?.count || 0

						// console.log(
						// 	`🔄 合并几何体组: ${
						// 		child.name || "unnamed"
						// 	}, 组数: ${
						// 		child.geometry.groups.length
						// 	}, 顶点数: ${vertexCount}`
						// )

						// 备份原始几何体（以防合并失败）
						const originalGeometry = child.geometry

						// 使用BufferGeometryUtils.mergeGroups合并组
						const mergedGeometry = BufferGeometryUtils.mergeGroups(
							child.geometry
						)

						// 验证合并结果
						if (
							mergedGeometry &&
							mergedGeometry.attributes.position
						) {
							// 替换原几何体
							child.geometry = mergedGeometry
							originalGeometry.dispose() // 清理原几何体

							optimizedCount++
							// console.log(
							// 	`✅ 几何体组合并完成: ${child.name || "unnamed"
							// 	}`
							// )
						} else {
							console.warn(
								`⚠️ 合并结果无效: ${child.name || "unnamed"}`
							)
						}
					} catch (error) {
						console.warn(
							`⚠️ 几何体组合并失败: ${child.name || "unnamed"}`,
							error
						)
					}
				}
			}
		})

		const endTime = performance.now()
		const duration = (endTime - startTime).toFixed(2)

		console.log(
			`🎯 模型 ${nodeData.name} 优化完成: 总网格数 ${totalMeshes}, 优化数 ${optimizedCount}, 耗时 ${duration}ms`
		)
	}

	/**
	 * 设置加载完成的模型
	 */
	private setupLoadedModel(
		object: THREE.Object3D,
		nodeData: BuildingExteriorNode | BuildingFloorNode | ModelNode,
		resource: ModelResource
	): void {
		// 对所有模型进行几何体合并优化
		this.optimizeModelGeometry(object, nodeData)

		// 设置阴影
		this.setupShadowsForModel(object, nodeData)

		// 设置名称和用户数据
		object.name = nodeData.id
		object.userData.nodeId = nodeData.id
		object.userData.nodeName = nodeData.name
		object.userData.modelType = this.detectModelType(resource)

		// 应用缩放
		if (resource.scale) {
			object.scale.setScalar(resource.scale)
		}

		// 应用旋转
		if (resource.rotation) {
			if (resource.rotation.x !== undefined)
				object.rotation.x = resource.rotation.x
			if (resource.rotation.y !== undefined)
				object.rotation.y = resource.rotation.y
			if (resource.rotation.z !== undefined)
				object.rotation.z = resource.rotation.z
		}

		console.log(
			`🎯 模型设置完成: ${nodeData.name}, 类型: ${object.userData.modelType}`
		)
	}

	/**
	 * 应用变换到3D对象
	 */
	private applyTransform(
		object3D: THREE.Object3D,
		nodeData: ModelNodeData
	): void {
		if (!nodeData.transform) return

		const { position, rotation, scale } = nodeData.transform

		if (position) {
			object3D.position.set(position.x, position.y, position.z)
		}

		if (rotation) {
			object3D.rotation.set(rotation.x, rotation.y, rotation.z)
		}

		if (scale) {
			object3D.scale.set(scale.x, scale.y, scale.z)
		}
	}

	/**
	 * 添加到父对象或场景
	 */
	private addToParent(
		object3D: THREE.Object3D,
		nodeData: ModelNodeData
	): void {
		// 🔧 修复：室内模型强制添加到场景根节点，避免成为外立面的子对象
		if (nodeData.type === ModelNodeType.BUILDING_FLOOR) {
			this.scene.add(object3D)
			console.log(`🏢 室内模型直接添加到场景: ${nodeData.name}`)
			return
		}

		// 其他模型按原逻辑处理
		if (nodeData.parentId) {
			const parentRuntime = this.runtimeData.get(nodeData.parentId)
			if (parentRuntime?.object3D) {
				parentRuntime.object3D.add(object3D)
				console.log(
					`📎 添加到父对象: ${nodeData.name} → ${nodeData.parentId}`
				)
			} else {
				// 父对象还未加载，添加到场景
				this.scene.add(object3D)
				console.log(`🌍 父对象未加载，添加到场景: ${nodeData.name}`)
			}
		} else {
			this.scene.add(object3D)
			console.log(`🌍 直接添加到场景: ${nodeData.name}`)
		}
	}

	/**
	 * 批量加载节点
	 */
	async loadNodes(
		nodeIds: string[],
		onProgress?: (completedCount: number, totalCount: number) => void,
		onNodeComplete?: LoadCompleteCallback
	): Promise<(THREE.Object3D | null)[]> {
		const results: (THREE.Object3D | null)[] = []
		let completedCount = 0

		for (const nodeId of nodeIds) {
			try {
				const object3D = await this.loadNode(
					nodeId,
					undefined,
					onNodeComplete
				)
				results.push(object3D)
			} catch (error) {
				console.error(`Failed to load node ${nodeId}:`, error)
				results.push(null)
			}

			completedCount++
			onProgress?.(completedCount, nodeIds.length)
		}

		return results
	}

	/**
	 * 加载建筑（外立面 + 所有楼层）
	 */
	async loadBuilding(
		buildingId: string,
		onProgress?: (progress: number) => void
	): Promise<{
		exterior: THREE.Object3D | null
		floors: (THREE.Object3D | null)[]
	}> {
		const building = this.treeManager.getNodeById(
			buildingId
		) as BuildingExteriorNode
		if (!building || building.type !== ModelNodeType.BUILDING_EXTERIOR) {
			throw new Error(`Building ${buildingId} not found`)
		}

		const floors = this.treeManager.getBuildingFloors(buildingId)
		const totalItems = 1 + floors.length
		let completedItems = 0

		// 加载外立面
		const exterior = await this.loadNode(buildingId)
		completedItems++
		onProgress?.(completedItems / totalItems)

		// 加载所有楼层
		const floorObjects: (THREE.Object3D | null)[] = []
		for (const floor of floors) {
			const floorObject = await this.loadNode(floor.id)
			floorObjects.push(floorObject)
			completedItems++
			onProgress?.(completedItems / totalItems)
		}

		return { exterior, floors: floorObjects }
	}

	/**
	 * 获取节点的3D对象
	 */
	getObject3D(nodeId: string): THREE.Object3D | undefined {
		return this.runtimeData.get(nodeId)?.object3D
	}

	/**
	 * 获取运行时数据
	 */
	getRuntimeData(nodeId: string): RuntimeModelData | undefined {
		return this.runtimeData.get(nodeId)
	}

	getWholeRuntimeData(): Map<string, RuntimeModelData> {
		return this.runtimeData
	}

	/**
	 * 设置节点可见性
	 */
	setNodeVisibility(nodeId: string, visible: boolean, duration?: number): void {
		console.log(`设置 ${nodeId} 可见性: ${visible}`);

		const object3D = this.getObject3D(nodeId)
		if (object3D) {
			// 新
			fadeInOrFadeOut(object3D, duration || 1, visible, nodeId)
			// // 旧
			// // console.log(`设置 ${nodeId} 3D对象可见性: ${visible}`)
			// object3D.visible = visible
			// // 🔧 对于室内模型，确保所有子对象也正确设置可见性
			// const nodeData = this.treeManager.getNodeById(nodeId)
			// if (nodeData?.type === ModelNodeType.BUILDING_FLOOR && visible) {
			// 	let meshCount = 0
			// 	object3D.traverse((child) => {
			// 		if (child.type === "Mesh" || child.type === "Group") {
			// 			child.visible = true
			// 			if (child.type === "Mesh") meshCount++
			// 		}
			// 	})
			// 	// console.log(
			// 	// 	`🏢 室内模型子对象设置完成: ${nodeId}, 网格数: ${meshCount}`
			// 	// )
			// }
		} else {
			console.warn(`节点 ${nodeId} 的3D对象未找到，可能尚未加载`)
		}

		// 更新节点数据
		this.treeManager.updateNode(nodeId, { visible })
		// console.log(`已更新节点 ${nodeId} 数据状态: visible=${visible}`)
	}

	startFade(nodeId: string, visible: boolean, duration: number) {
		const target = this.getObject3D(nodeId)!
		console.log(target);
		
		const mateiral = target.children[0].material
		mateiral.forEach(m => {
			m.transparent = true
		})
		const targetVisible = visible
		const targetOpacity = visible ? 1 : 0
		const _this = this
		const onComplete = () => {
			target.visible = targetVisible
			if(targetVisible){
				mateiral.forEach(m=>{
					m.opacity = targetOpacity
					m.transparent = false
				})
			}

			_this.currentAnimationInfo = {

			}
		}


		this.currentAnimationInfo = {
			buildingId: nodeId,
			tween: gsap.to(mateiral, {
				opacity: targetOpacity,
				duration,
				onComplete
			}),
			targetVisible,
			onComplete

		}
	}



	/**
	 * 移除节点
	 */
	removeNode(nodeId: string): void {
		const runtimeData = this.runtimeData.get(nodeId)
		if (runtimeData?.object3D) {
			// 从场景中移除
			if (runtimeData.object3D.parent) {
				runtimeData.object3D.parent.remove(runtimeData.object3D)
			}

			// 清理几何体和材质
			this.disposeObject3D(runtimeData.object3D)
		}

		// 清理运行时数据
		this.runtimeData.delete(nodeId)

		// 从树管理器中移除
		this.treeManager.removeNode(nodeId)

		eventBus.emit("model-removed", { nodeId })
	}

	/**
	 * 清理3D对象资源
	 */
	private disposeObject3D(object3D: THREE.Object3D): void {
		object3D.traverse((child) => {
			if (child instanceof THREE.Mesh) {
				child.geometry?.dispose()

				if (Array.isArray(child.material)) {
					child.material.forEach((material) => material.dispose())
				} else {
					child.material?.dispose()
				}
			}
		})
	}

	/**
	 * 获取加载统计信息
	 */
	getLoadStats(): {
		total: number
		loaded: number
		loading: number
		error: number
		pending: number
	} {
		const allNodes = this.treeManager.getAllNodes()
		const stats = {
			total: allNodes.length,
			loaded: 0,
			loading: 0,
			error: 0,
			pending: 0,
		}

		allNodes.forEach((node) => {
			switch (node.loadState) {
				case ModelLoadState.LOADED:
					stats.loaded++
					break
				case ModelLoadState.LOADING:
					stats.loading++
					break
				case ModelLoadState.ERROR:
					stats.error++
					break
				case ModelLoadState.PENDING:
					stats.pending++
					break
			}
		})

		return stats
	}

	/**
	 * 清理所有资源
	 */
	dispose(): void {
		// 清理所有3D对象
		this.runtimeData.forEach((data) => {
			if (data.object3D) {
				this.disposeObject3D(data.object3D)
			}
		})

		// 清理数据
		this.runtimeData.clear()
		this.loadingQueue.clear()
	}
}
