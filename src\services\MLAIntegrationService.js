// 博物馆900+信标MLA集成服务
// 支持小程序5秒定时校正的完整方案

import { becaonData } from '../../public/BecaonData.js';
import { eventBus } from '@/refactored/EventBus';

export class MLAIntegrationService {
  constructor() {
    this.navigation = null;
    this.mlaNodeIndex = new Map(); // 节点快速查找索引
    this.activeNodes = new Map();  // 当前激活的MLA节点
    this.correctionHistory = [];   // 校正历史
    this.lastMiniProgramData = null; // 最新小程序数据
    this.miniProgramTimer = null;  // 小程序数据超时检测
    
    this.currentContext = {
      buildingId: null,
      floor: null,
      lastUpdate: 0
    };
    
    this.config = {
      maxActiveNodes: 15,        // 最大激活节点数
      nodeUpdateRadius: 50,      // 节点更新半径(米)
      miniProgramTimeout: 10000, // 小程序数据超时(10秒)
      correctionHistoryLimit: 100 // 校正历史限制
    };
    
    this.initializeMLANodes();
    // this.setupEventListeners();
  }

  // 初始化MLA节点索引
  initializeMLANodes() {
    console.log('🏗️ 初始化MLA节点索引...');
    
    // 按建筑+楼层建立索引
    becaonData.forEach(beacon => {
      const key = `${beacon.buildingId}_${beacon.floor}`;
      if (!this.mlaNodeIndex.has(key)) {
        this.mlaNodeIndex.set(key, []);
      }
      
      // 转换为MLA节点格式
      const mlaNode = {
        id: beacon.beaconId,
        name: `${beacon.buildingId}_F${beacon.floor}_${beacon.beaconId}`,
        x: parseFloat(beacon.x.toFixed(4)),
        y: parseFloat(beacon.y.toFixed(4)),
        z: parseFloat(beacon.height.toFixed(4)),
        confidence: this.calculateNodeConfidence(beacon),
        buildingId: beacon.buildingId,
        floor: beacon.floor,
        beaconId: beacon.beaconId,
        range: this.calculateNodeRange(beacon),
        metadata: {
          lon: beacon.lon,
          lat: beacon.lat,
          lastActive: 0
        }
      };
      
      this.mlaNodeIndex.get(key).push(mlaNode);
    });
    
    console.log(`✅ 已索引 ${this.mlaNodeIndex.size} 个区域的MLA节点`);
  }

  // 计算节点置信度 (基于位置和环境)
  calculateNodeConfidence(beacon) {
    let confidence = 0.7; // 基础置信度
    
    // 根据建筑类型调整
    const buildingBonus = {
      'EastMuseum': 0.1,  // 东馆较新，精度更高
      'WestMuseum': 0.05,
      'SouthMuseum': 0.08,
      'NorthMuseum': 0.06
    };
    confidence += buildingBonus[beacon.buildingId] || 0;
    
    // 根据楼层调整 (1楼通常精度更高)
    if (beacon.floor === 1) confidence += 0.1;
    
    // 根据高度调整 (标准高度3.5-4米最佳)
    const heightDiff = Math.abs(beacon.height - 3.7);
    if (heightDiff < 0.3) confidence += 0.05;
    
    return Math.min(1.0, confidence);
  }

  // 计算节点影响范围
  calculateNodeRange(beacon) {
    // 根据楼层和位置调整范围
    let range = 5.0; // 默认5米
    
    // 1楼范围更大 (人流密集)
    if (beacon.floor === 1) range += 2.0;
    
    // 边界节点范围更大
    const isEdge = Math.abs(beacon.x) > 70 || Math.abs(beacon.y) > 25;
    if (isEdge) range += 1.0;
    
    return range;
  }

  // 设置导航实例
  setNavigation(navigation) {
    this.navigation = navigation;
    console.log('🧭 MLA服务已绑定导航实例');
  }

  // 处理小程序传来的MLA校正数据
  handleMiniProgramMLA(data) {
    console.log('📱 收到小程序MLA数据:', data);
    window.writeLog && window.writeLog(`<span style='color:blue;'>📱 收到小程序MLA数据</span>: ${JSON.stringify(data)}`);

    this.lastMiniProgramData = {
      ...data,
      receivedAt: Date.now()
    };

    // 重置超时定时器
    if (this.miniProgramTimer) {
      clearTimeout(this.miniProgramTimer);
    }
    this.miniProgramTimer = setTimeout(() => {
      console.log('⚠️ 小程序MLA数据超时');
      this.lastMiniProgramData = null;
    }, this.config.miniProgramTimeout);

    // 应用MLA校正
    this.applyMLACorrection(data);
  }

  // 应用MLA校正 - 新方案：立即更新位置，不依赖传感器更新频率
  applyMLACorrection(mlaData) {
    if (!this.navigation) return;

    console.log('🎯 收到MLA校正数据:', mlaData);

    // 方案A: 直接坐标校正
    if (mlaData.position) {
      const correctionData = {
        id: `mla_${Date.now()}`,
        name: 'miniprogram_correction',
        position: {
          x: mlaData.position.x,
          y: mlaData.position.y,
          z: mlaData.position.z || 0
        },
        confidence: mlaData.confidence || 0.8,
        buildingId: mlaData.buildingId || 'unknown',
        floor: mlaData.floor || 1,
        source: 'miniprogram_direct',
        timestamp: mlaData.timestamp || Date.now()
      };

      // 立即应用校正
      window.writeLog && window.writeLog(`<span style='color:orange;'>🎯 准备应用MLA直接坐标校正</span>: x=${correctionData.position.x}, y=${correctionData.position.y}`);
      const success = this.navigation.applyImmediateMlaCorrection(correctionData);
      if (success) {
        console.log('✅ MLA直接坐标校正成功');
        window.writeLog && window.writeLog(`<span style='color:green;'>✅ MLA直接坐标校正成功</span>`);
        this.recordCorrection(mlaData);
        return;
      } else {
        window.writeLog && window.writeLog(`<span style='color:red;'>❌ MLA直接坐标校正失败</span>`);
      }
    }

    // 方案B: 信标ID校正
    else if (mlaData.beaconId) {
      const beaconNode = this.findNodeByBeaconId(mlaData.beaconId);
      if (beaconNode) {
        // 根据距离计算校正位置
        const correctionPos = this.calculateCorrectionPosition(
          beaconNode,
          mlaData.distance,
          mlaData.rssi
        );

        const correctionData = {
          id: `beacon_${mlaData.beaconId}`,
          name: beaconNode.name,
          position: {
            x: correctionPos.x,
            y: correctionPos.y,
            z: correctionPos.z
          },
          confidence: beaconNode.confidence * this.getRSSIConfidence(mlaData.rssi),
          buildingId: mlaData.buildingId || beaconNode.buildingId,
          floor: mlaData.floor || beaconNode.floor,
          beaconId: mlaData.beaconId,
          source: 'miniprogram_beacon',
          timestamp: mlaData.timestamp || Date.now()
        };

        // 立即应用校正
        const success = this.navigation.applyImmediateMlaCorrection(correctionData);
        if (success) {
          console.log('✅ MLA信标校正成功');
          this.recordCorrection(mlaData);
          return;
        }
      }
    }

    console.warn('⚠️ MLA校正数据格式不正确或处理失败');
  }

  // 根据信标ID查找MLA节点
  findNodeByBeaconId(beaconId) {
    for (const [key, nodes] of this.mlaNodeIndex.entries()) {
      const node = nodes.find(n => n.beaconId === beaconId);
      if (node) return node;
    }
    return null;
  }

  // 根据RSSI计算置信度加成
  getRSSIConfidence(rssi) {
    if (rssi > -50) return 1.0;      // 很强信号
    if (rssi > -65) return 0.9;      // 强信号
    if (rssi > -75) return 0.7;      // 中等信号
    if (rssi > -85) return 0.5;      // 弱信号
    return 0.3;                      // 很弱信号
  }

  // 计算基于信标距离的校正位置
  calculateCorrectionPosition(beaconNode, distance, rssi) {
    // 简化实现：以信标为中心，根据距离和当前位置推算
    const currentPos = this.navigation.getCurrentLocation().position;
    const toBeacon = {
      x: beaconNode.x - currentPos.x,
      y: beaconNode.y - currentPos.y
    };
    const currentDistance = Math.sqrt(toBeacon.x ** 2 + toBeacon.y ** 2);
    
    if (currentDistance < 0.1) return beaconNode; // 已在信标位置
    
    // 按实测距离调整位置
    const ratio = distance / currentDistance;
    return {
      x: currentPos.x + toBeacon.x * ratio,
      y: currentPos.y + toBeacon.y * ratio,
      z: beaconNode.z
    };
  }

  // 动态更新激活的MLA节点
  updateActiveNodes(currentPos) {
    const key = `${currentPos.buildingId}_${currentPos.floor}`;
    const floorNodes = this.mlaNodeIndex.get(key) || [];
    
    // 筛选附近节点
    const nearbyNodes = floorNodes
      .map(node => ({
        ...node,
        distance: Math.sqrt((node.x - currentPos.x) ** 2 + (node.y - currentPos.y) ** 2)
      }))
      .filter(node => node.distance <= this.config.nodeUpdateRadius)
      .sort((a, b) => (a.distance / a.confidence) - (b.distance / b.confidence))
      .slice(0, this.config.maxActiveNodes);
    
    // 更新导航系统
    if (this.navigation) {
      this.navigation.setMlaNodes(nearbyNodes);
      console.log(`🎯 已更新${nearbyNodes.length}个激活MLA节点`);
    }
    
    this.activeNodes.set(key, nearbyNodes);
    return nearbyNodes;
  }

  // 记录校正历史
  recordCorrection(mlaData) {
    this.correctionHistory.push({
      timestamp: Date.now(),
      data: mlaData,
      source: 'miniprogram'
    });
    
    // 限制历史长度
    if (this.correctionHistory.length > this.config.correctionHistoryLimit) {
      this.correctionHistory.shift();
    }
  }

  // 设置事件监听
  setupEventListeners() {
    // 监听楼层切换
    eventBus.on('floorChange', (floorData) => {
      this.currentContext = {
        buildingId: floorData.buildingId,
        floor: floorData.floor,
        lastUpdate: Date.now()
      };
      
      // 更新激活节点
      this.updateActiveNodes({
        buildingId: floorData.buildingId,
        floor: floorData.floor,
        x: this.navigation?.getCurrentLocation()?.position?.x || 0,
        y: this.navigation?.getCurrentLocation()?.position?.y || 0
      });
    });

    // 监听小程序MLA数据 (通过URL参数)
    window.addEventListener('hashchange', () => {
      this.parseMLAFromURL();
    });
  }

  // 从URL解析小程序MLA数据
  parseMLAFromURL() {
    const urlParams = new URLSearchParams(window.location.hash.split('?')[1]);
    
    // 解析MLA校正数据
    if (urlParams.get('mlaCorrection')?.length > 0) {
      const mlaStr = urlParams.get('mlaCorrection').replace(/(\w+):/g, '"$1":');
      const mlaData = JSON.parse(decodeURIComponent(mlaStr));
      this.handleMiniProgramMLA(mlaData);
    }
  }

  // 获取统计信息
  getStatistics() {
    const recentCorrections = this.correctionHistory.filter(
      c => Date.now() - c.timestamp < 300000 // 最近5分钟
    );
    
    return {
      totalNodes: Array.from(this.mlaNodeIndex.values()).flat().length,
      activeNodes: Array.from(this.activeNodes.values()).flat().length,
      recentCorrections: recentCorrections.length,
      averageAccuracy: this.calculateAverageAccuracy(recentCorrections),
      miniProgramStatus: this.lastMiniProgramData ? 'connected' : 'disconnected',
      lastMiniProgramUpdate: this.lastMiniProgramData?.receivedAt || 0
    };
  }

  // 计算平均校正精度
  calculateAverageAccuracy(corrections) {
    if (corrections.length === 0) return 0;
    
    const avgConfidence = corrections.reduce((sum, c) => 
      sum + (c.data.confidence || 0.5), 0) / corrections.length;
    
    return (avgConfidence * 100).toFixed(1);
  }

  // 清理资源
  dispose() {
    if (this.miniProgramTimer) {
      clearTimeout(this.miniProgramTimer);
    }
    eventBus.off('floorChange');
    console.log('🗑️ MLA集成服务已清理');
  }
}

// 在index.vue中的集成代码
export const indexVueIntegration = {
  // 1. 导入和初始化
  setup: `
    import { MLAIntegrationService } from '@/services/MLAIntegrationService.js';
    
    // 创建MLA服务实例
    const mlaService = ref(null);
    
    onMounted(() => {
      mlaService.value = new MLAIntegrationService();
    });
  `,
  
  // 2. 修改initPDR函数
  modifiedInitPDR: `
    const initPDR = async () => {
      nav = new InertialNavigationWeb({
        initialPosition: { x: 114.361111, y: 30.563889, z: 0 },
        mode: 'precise',
        sampleRate: 50,
        enableMLA: true,  // 🆕 启用MLA
        fusion: {
          enabled: true,
          pdrWeight: 0.3,
          mlaWeight: 0.4,   // 蓝牙环境下较高MLA权重
          adaptiveWeighting: true,
          maxCorrection: 8.0,
          confidenceThreshold: 0.3
        }
      });

      // 🆕 绑定MLA服务
      mlaService.value.setNavigation(nav);

      // 🆕 增强位置更新回调
      nav.onPositionUpdate = (location) => {
        // 更新激活MLA节点
        mlaService.value.updateActiveNodes({
          buildingId: location.buildingId || 'WestMuseum',
          floor: location.floor || 1,
          x: location.position.x,
          y: location.position.y
        });
        
        // 原有逻辑
        test_userPos.value.rotate = location.heading.toFixed(2);
        eventBus.emit("updateUserPosInfo", {
          x: location.position.x,
          y: location.position.y,
          rotate: location.heading,
          floor: location.floor || 1,
          buildingId: location.buildingId || "sw",
          // 🆕 MLA状态信息
          corrected: location.corrected,
          correctionDistance: location.correctionDistance,
          mlaActiveNodes: mlaService.value.activeNodes.size
        });
      };

      // 启动导航
      const success = await nav.start();
      if (success) {
        alert('惯导定位+MLA校正启动成功');
      }
    };
  `,
  
  // 3. URL参数处理增强
  enhancedHashChange: `
    // 在hashChange函数中添加MLA数据处理
    if (params.get("mlaCorrection")?.length > 0) {
      const mlaStr = params.get("mlaCorrection").replace(/(\w+):/g, '"$1":');
      const mlaData = JSON.parse(decodeURIComponent(mlaStr));
      
      // 传递给MLA服务处理
      if (mlaService.value) {
        mlaService.value.handleMiniProgramMLA(mlaData);
      }
    }
  `
};

// 小程序端MLA数据发送示例
export const miniProgramMLAExample = {
  // 方案A: 发送计算好的坐标
  sendPosition: `
    // 小程序端每5秒发送
    setInterval(() => {
      const mlaData = {
        timestamp: Date.now(),
        position: { 
          x: beaconTriangulation.x,  // 三角测量结果
          y: beaconTriangulation.y, 
          z: currentFloorHeight 
        },
        confidence: calculateTriangulationConfidence(nearbyBeacons),
        buildingId: currentBuilding,
        floor: currentFloor,
        source: "beacon_triangulation"
      };
      
      // 通过URL参数传递
      const mlaParam = encodeURIComponent(JSON.stringify(mlaData));
      postMessage({ 
        type: 'navigate',
        url: \`#/map?mlaCorrection=\${mlaParam}\`
      });
    }, 5000);
  `,
  
  // 方案B: 发送最强信标+距离
  sendBeaconDistance: `
    setInterval(() => {
      const strongestBeacon = findStrongestBeacon();
      const mlaData = {
        timestamp: Date.now(),
        beaconId: strongestBeacon.id,
        distance: strongestBeacon.distance,
        rssi: strongestBeacon.rssi,
        buildingId: currentBuilding,
        floor: currentFloor,
        source: "beacon_proximity"
      };
      
      const mlaParam = encodeURIComponent(JSON.stringify(mlaData));
      postMessage({ 
        type: 'navigate',
        url: \`#/map?mlaCorrection=\${mlaParam}\`
      });
    }, 5000);
  `
};

export default MLAIntegrationService;