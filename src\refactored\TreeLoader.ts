import * as THREE from "three"
import { FBXLoader } from "three/examples/jsm/Addons.js"

/**
 * 水面效果类
 * 参照 Three.js ocean 示例实现
 */
export class TreeLoader {
	constructor(scene: THREE.Scene) {

		const loader = new FBXLoader();

		// 1. 加载点位 FBX
		loader.load('/fbx/树点位.fbx', (pointsFbx) => {
			const positions: any = [];

			pointsFbx.traverse((child: any) => {
				if (child.isMesh) {
					// 获取每个矩形条的位置（世界坐标）
					const pos = new THREE.Vector3();
					child.getWorldPosition(pos);
					positions.push(pos);
				}
			});

			// 可以选择隐藏点位模型
			pointsFbx.visible = false;
			scene.add(pointsFbx);

			// 2. 加载树模型 FBX
			loader.load('/fbx/tree.fbx', (treeFbx) => {
				this.addTreesInstanced(treeFbx, positions, scene);
			});

		});

		// eventBus.on("water-visibility-change", (eventData) => {
		// 	const { visible } = eventData
		// 	this.setVisible(visible)
		// })
	}
	/**
	 * 批量布置树模型（InstancedMesh）
	 * @param {THREE.Object3D} treeModel - 已加载的树模型（FBX/GLTF等）
	 * @param {THREE.Vector3[]} pointPositions - 树点位数组
	 * @param {THREE.Scene} scene - 场景对象
	 * @param {Object} options - 可选参数：随机缩放、高度范围
	 */
	private addTreesInstanced(treeModel: any, pointPositions: any, scene: any) {

		// 获取树的几何和材质
		let geometry: any, material: any;

		treeModel.traverse((child: any) => {
			if (child.isMesh && !geometry) {
				geometry = child.geometry;
				material = child.material;
			}
		});

		if (!geometry || !material) {
			console.error("treeModel must contain a Mesh with geometry and material");
			return;
		}

		const count = pointPositions.length;
		const instancedMesh = new THREE.InstancedMesh(geometry, material, count);

		const dummy = new THREE.Object3D();

		pointPositions.forEach((pos: any, i: any) => {
			// 随机缩放
			// const scaleFactor = scaleMin + Math.random() * (scaleMax - scaleMin);
			const scaleFactor = 0.0003;
			dummy.scale.set(scaleFactor, scaleFactor, scaleFactor);

			// 随机高度缩放
			const heightFactor = 0.5 + Math.random() * 1.5;
			dummy.scale.z *= heightFactor;

			// 设置位置
			dummy.position.copy(pos);

			// 随机旋转 Y
			dummy.rotation.z = THREE.MathUtils.degToRad(Math.random() * 360);
			dummy.rotation.x = -Math.PI / 2; // 或 rotation.z = -Math.PI/2, 根据模型调整

			// 更新矩阵
			dummy.updateMatrix();

			// 写入实例矩阵
			instancedMesh.setMatrixAt(i, dummy.matrix);
		});

		scene.add(instancedMesh);

		return instancedMesh;
	}

}
