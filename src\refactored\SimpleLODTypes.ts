import * as THREE from "three"

/**
 * 简化的LOD级别定义
 */
export enum SimpleLODLevel {
	VISIBLE = "visible",   // 可见
	HIDDEN = "hidden",     // 隐藏
}

/**
 * 简化的LOD模型数据
 */
export interface SimpleLODModelData {
	id: string
	name: string
	position: THREE.Vector3
	object: THREE.Object3D
	level: SimpleLODLevel
	isVisible: boolean
	distanceToCamera: number
	lastUpdateTime: number
}

/**
 * 简化的LOD配置
 */
export interface SimpleLODConfig {
	enabled: boolean           // 是否启用LOD
	maxDistance: number        // 最大可见距离
	updateInterval: number     // 更新间隔（毫秒）
}

/**
 * 默认配置
 */
export const DEFAULT_SIMPLE_LOD_CONFIG: SimpleLODConfig = {
	enabled: true,
	maxDistance: 100,
	updateInterval: 100,
}

/**
 * 简化的LOD管理器接口
 */
export interface SimpleLODManagerInterface {
	// 基础控制
	setEnabled(enabled: boolean): void
	isEnabled(): boolean
	
	// 模型管理
	addModel(modelData: {
		id: string
		name: string
		position: THREE.Vector3
		object: THREE.Object3D
	}): string
	removeModel(id: string): void
	
	// 可见性控制
	setModelVisible(id: string, visible: boolean): void
	isModelVisible(id: string): boolean
	showModel(id: string): void
	hideModel(id: string): void
	toggleModel(id: string): void
	showAllModels(): void
	hideAllModels(): void
	
	// LOD更新
	updateLOD(camera: THREE.Camera, maxDistance?: number): void
	
	// 查询
	getModel(id: string): SimpleLODModelData | undefined
	getAllModels(): SimpleLODModelData[]
	getVisibleModelCount(): number
	getTotalModelCount(): number
	
	// 工具
	getStats(): {
		total: number
		visible: number
		hidden: number
		enabled: boolean
	}
	debug(): void
	dispose(): void
}

/**
 * LOD事件类型
 */
export enum SimpleLODEventType {
	MODEL_ADDED = "model-added",
	MODEL_REMOVED = "model-removed", 
	VISIBILITY_CHANGED = "visibility-changed",
	LOD_UPDATED = "lod-updated",
	CONFIG_CHANGED = "config-changed",
}

/**
 * LOD事件数据
 */
export interface SimpleLODEventData {
	modelId?: string
	visible?: boolean
	distance?: number
	timestamp?: number
}

/**
 * 工具函数：计算两点间距离
 */
export function calculateDistance(
	pos1: THREE.Vector3,
	pos2: THREE.Vector3
): number {
	return pos1.distanceTo(pos2)
}

/**
 * 工具函数：检查对象是否在相机视野内
 */
export function isInCameraView(
	object: THREE.Object3D,
	camera: THREE.Camera
): boolean {
	// 简单的距离检查
	const distance = object.position.distanceTo(camera.position)
	return distance <= 1000 // 简单的可见距离判断
}

/**
 * 工具函数：格式化统计信息
 */
export function formatStats(stats: {
	total: number
	visible: number
	hidden: number
	enabled: boolean
}): string {
	return `SimpleLOD Stats: ${stats.visible}/${stats.total} visible (${stats.enabled ? "enabled" : "disabled"})`
}

/**
 * 工具函数：创建简单的LOD模型数据
 */
export function createSimpleLODModel(
	id: string,
	name: string,
	position: THREE.Vector3,
	object: THREE.Object3D
): Omit<SimpleLODModelData, "level" | "isVisible" | "distanceToCamera" | "lastUpdateTime"> {
	return {
		id,
		name,
		position: position.clone(),
		object,
	}
}
