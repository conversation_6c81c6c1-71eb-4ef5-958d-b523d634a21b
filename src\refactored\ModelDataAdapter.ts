import {
	type ModelNodeData,
	ModelNodeType,
	ModelLoadState,
	createBuildingExteriorNode,
	createBuildingFloorNode,
	createModelNode,
} from "./ModelTypes"

/**
 * 原始JSON数据格式
 */
export interface RawModelData {
	id: string
	name: string
	type: string // 'obj' | 'fbx' | 'gltf' | 'glb'
	modelName: string
	parentId: string
	floor?: number
	// FBX相关字段
	fbxPath?: string
	scale?: number
	rotation?: {
		x?: number
		y?: number
		z?: number
	}
	// 位置信息
	position?: {
		x?: number
		y?: number
		z?: number
	}
	// 材质和纹理
	textures?: string[]
	materialConfig?: any
	switchDistance?: number
}

/**
 * 模型数据适配器
 * 将public/modelList.json的数据转换为我们的数据类型
 */
export class ModelDataAdapter {
	/**
	 * 从JSON文件加载模型数据
	 */
	static async loadFromJSON(): Promise<ModelNodeData[]> {
		try {
			const response = await fetch("https://fm-demo.daspatial.com/demo/web-indoor-navigation/dist/fbxModelList.json")
			const rawData: RawModelData[] = await response.json()
			console.log(rawData)

			return this.convertRawData(rawData)
		} catch (error) {
			console.error("Failed to load model data:", error)
			return []
		}
	}

	/**
	 * 转换原始数据为我们的数据类型
	 */
	static convertRawData(rawData: RawModelData[]): ModelNodeData[] {
		const convertedNodes: ModelNodeData[] = []

		// 首先处理建筑外立面
		const exteriorBuildings = rawData.filter(
			(item) =>
				item.parentId === "scene" &&
				(item.type === "obj" || item.type === "fbx") &&
				item.id !== "ground"
		)

		exteriorBuildings.forEach((building) => {
			// 检查是否有楼层
			const floors = rawData.filter(
				(item) => item.parentId === building.id
			)
			const hasInterior = floors.length > 0

			// 构建资源配置
			const resource = this.buildResourceConfig(building)

			// 获取位置信息
			const position = this.getModelPosition(building)

			console.log(`🏢 处理建筑外立面: ${building.name}`, {
				type: building.type,
				resource,
				position,
				hasInterior,
			})

			// 创建建筑外立面节点
			const buildingNode = createBuildingExteriorNode(
				building.id,
				building.name,
				resource,
				position,
				hasInterior,
				hasInterior
					? {
							switchDistance: building.switchDistance || 500,
							fadeDistance: 10,
							defaultFloor:
								floors.length > 0
									? Math.min(
											...floors.map((f) => f.floor || 1)
									  )
									: 1,
					  }
					: undefined
			)

			convertedNodes.push(buildingNode)

			// 处理楼层
			floors.forEach((floor) => {
				// 构建楼层资源配置
				const floorResource = this.buildResourceConfig(floor)

				console.log(`🏠 处理楼层: ${floor.name}`, {
					type: floor.type,
					resource: floorResource,
					floor: floor.floor,
				})

				const floorNode = createBuildingFloorNode(
					floor.id,
					floor.name,
					building.id,
					floorResource,
					floor.floor || 1,
					floor.name,
					floor.floor === 1 // 默认显示一层
				)
				convertedNodes.push(floorNode)
			})
		})

		// 处理地面等其他模型
		const otherModels = rawData.filter(
			(item) => item.parentId === "scene" && item.id === "ground"
		)

		otherModels.forEach((model) => {
			// 构建其他模型资源配置
			const modelResource = this.buildResourceConfig(model)

			console.log(`🌍 处理其他模型: ${model.name}`, {
				type: model.type,
				resource: modelResource,
			})

			const modelNode = createModelNode(
				model.id,
				model.name,
				modelResource
			)

			// 设置变换信息（如果有的话）
			const position = this.getModelPosition(model)
			if (position.x !== 0 || position.y !== 0 || position.z !== 0) {
				modelNode.transform = {
					position: position,
				}
			}
			convertedNodes.push(modelNode)
		})

		return convertedNodes
	}

	/**
	 * 构建模型资源配置
	 */
	private static buildResourceConfig(item: RawModelData) {
		const resource: any = {}

		// 根据类型设置文件路径
		if (item.type === "fbx") {
			resource.type = "fbx"
			resource.fbxPath = item.fbxPath || `${window.modelUrl}/models/${item.modelName}.fbx`
		} else if (item.type === "obj") {
			resource.type = "obj"
			resource.objPath = `/20250711/${item.modelName}.obj`
			resource.mtlPath = `/20250711/${item.modelName}.mtl`
		} else if (item.type === "gltf") {
			resource.type = "gltf"
			resource.gltfPath = `/models/${item.modelName}.gltf`
		} else if (item.type === "glb") {
			resource.type = "glb"
			resource.gltfPath = `/models/${item.modelName}.glb`
		} else {
			// 默认为OBJ格式（向后兼容）
			resource.objPath = `/20250711/${item.modelName}.obj`
			resource.mtlPath = `/20250711/${item.modelName}.mtl`
		}

		// 设置缩放
		if (item.scale !== undefined) {
			resource.scale = item.scale
		}

		// 设置旋转
		if (item.rotation) {
			resource.rotation = {
				x: item.rotation.x || 0,
				y: item.rotation.y || 0,
				z: item.rotation.z || 0,
			}
		}

		// 设置纹理
		if (item.textures && item.textures.length > 0) {
			resource.texturePaths = item.textures
		}

		return resource
	}

	/**
	 * 获取模型位置
	 */
	private static getModelPosition(item: RawModelData) {
		return {
			x: item.position?.x || 0,
			y: item.position?.y || 0,
			z: item.position?.z || 0,
		}
	}

	/**
	 * 获取建筑列表（用于UI显示）
	 */
	static getBuildingList(nodes: ModelNodeData[]): Array<{
		id: string
		name: string
		floors: Array<{
			id: string
			name: string
			floor: number
			visible: boolean
		}>
	}> {
		const buildings = nodes.filter(
			(node) => node.type === ModelNodeType.BUILDING_EXTERIOR
		)

		return buildings.map((building) => ({
			id: building.id,
			name: building.name,
			floors: nodes
				.filter(
					(node) =>
						node.type === ModelNodeType.BUILDING_FLOOR &&
						node.parentId === building.id
				)
				.map((floor) => ({
					id: floor.id,
					name: floor.name,
					floor: (floor as any).floor || 1,
					visible: floor.visible,
				}))
				.sort((a, b) => a.floor - b.floor),
		}))
	}

	/**
	 * 获取普通模型列表
	 */
	static getModelList(nodes: ModelNodeData[]): Array<{
		id: string
		name: string
		type: string
		loadState: ModelLoadState
		visible: boolean
	}> {
		const filteredNodes = nodes.filter(
			(node) =>
				node.type === ModelNodeType.MODEL ||
				node.type === ModelNodeType.BUILDING_EXTERIOR
		)

		// console.log(
		// 	"getModelList - 过滤后的节点:",
		// 	filteredNodes.map((n) => ({
		// 		id: n.id,
		// 		name: n.name,
		// 		type: n.type,
		// 		visible: n.visible,
		// 	}))
		// )

		return filteredNodes.map((node) => ({
			id: node.id,
			name: node.name,
			type: this.getNodeTypeLabel(node.type),
			loadState: node.loadState,
			visible: node.visible,
		}))
	}

	/**
	 * 获取节点类型标签
	 */
	static getNodeTypeLabel(type: ModelNodeType): string {
		const labels = {
			[ModelNodeType.SCENE]: "场景",
			[ModelNodeType.BUILDING_EXTERIOR]: "建筑外立面",
			[ModelNodeType.BUILDING_FLOOR]: "建筑楼层",
			[ModelNodeType.MODEL]: "模型",
			[ModelNodeType.GROUP]: "模型组",
		}
		return labels[type] || type
	}

	/**
	 * 获取统计信息
	 */
	static getStats(nodes: ModelNodeData[]): {
		total: number
		buildings: number
		floors: number
		models: number
		loaded: number
		loading: number
		error: number
		pending: number
	} {
		const stats = {
			total: nodes.length,
			buildings: 0,
			floors: 0,
			models: 0,
			loaded: 0,
			loading: 0,
			error: 0,
			pending: 0,
		}

		nodes.forEach((node) => {
			// 统计类型
			switch (node.type) {
				case ModelNodeType.BUILDING_EXTERIOR:
					stats.buildings++
					break
				case ModelNodeType.BUILDING_FLOOR:
					stats.floors++
					break
				case ModelNodeType.MODEL:
					stats.models++
					break
			}

			// 统计状态
			switch (node.loadState) {
				case ModelLoadState.LOADED:
					stats.loaded++
					break
				case ModelLoadState.LOADING:
					stats.loading++
					break
				case ModelLoadState.ERROR:
					stats.error++
					break
				case ModelLoadState.PENDING:
					stats.pending++
					break
			}
		})

		return stats
	}
}
