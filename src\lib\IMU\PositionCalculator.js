/**
 * 位置计算器 - Web版本
 * 负责PDR位置递推和传感器融合
 * 移植自微信小程序版本，适配Web浏览器环境
 *
 * 坐标系统说明：
 * - 使用地理坐标系：0°指向正北，90°指向正东，顺时针递增
 * - X轴：东西方向（正值向东）
 * - Y轴：南北方向（正值向北）
 * - 航向角：0°=北，90°=东，180°=南，270°=西
 */

import MotionAnalyzer from './MotionAnalyzer.js';

export default class PositionCalculator {
  constructor(config = {}) {
    this.config = {
      // PDR计算配置
      pdr: {
        enabled: true,
        stepLengthMode: 'adaptive', // 'fixed', 'adaptive', 'calibrated'
        headingCorrection: true,
        smoothingFactor: 0.3,
        ...config.pdr
      },

      // 融合配置
      fusion: {
        enabled: config.enableMLA !== false,
        pdrWeight: 0.7,
        mlaWeight: 0.3,
        adaptiveWeighting: true,
        confidenceThreshold: 0.4,
        outlierThreshold: 10.0,
        maxCorrection: 5.0,
        ...config.fusion
      },

      // 质量控制
      quality: {
        minConfidence: 0.2,
        maxDeviation: 5.0,
        positionValidation: true,
        ...config.quality
      },

      // 位置校正配置
      correction: {
        enabled: true,
        minMovementThreshold: 0.15,      // 最小移动阈值（米）
        staticPositionLock: true,        // 静止位置锁定
        staticThreshold: 0.05,           // 静止判断阈值（米）
        staticDuration: 1000,            // 静止持续时间（毫秒）
        driftCorrection: true,           // 漂移校正
        driftThreshold: 0.8,             // 漂移检测阈值（米）
        maxDriftCorrection: 0.3,         // 最大漂移校正距离（米）
        ...config.correction
      }
    };
    this.mlaTimmer = 0;
    // 运动分析器
    this.motionAnalyzer = new MotionAnalyzer(config);

    // 当前位置状态
    this.position = {
      x: 0,
      y: 0,
      z: 0,
      heading: 0,
      confidence: 1.0,
      timestamp: Date.now()
    };

    // 初始位置
    this.initialPosition = { x: 0, y: 0, z: 0 };

    // 位置历史
    this.positionHistory = [];
    this.maxHistoryLength = 200;

    // PDR状态
    this.pdrState = {
      totalDistance: 0,
      totalSteps: 0,
      avgStepLength: 0.75,
      lastStepPosition: null,
      velocity: 0,
      isInitialized: false
    };

    // 融合状态
    this.fusionState = {
      lastCorrection: null,
      correctionCount: 0,
      adaptiveWeights: {
        pdr: this.config.fusion.pdrWeight,
        mla: this.config.fusion.mlaWeight
      },
      qualityMetrics: {
        consistency: 1.0,
        accuracy: 1.0,
        reliability: 1.0
      }
    };

    // MLA节点数据（可选）
    this.mlaNodes = [];

    // 位置校正状态
    this.correctionState = {
      isStatic: false,               // 当前是否静止
      staticStartTime: 0,            // 开始静止的时间
      staticPosition: null,          // 静止锁定位置
      lastValidPosition: null,       // 最后一次有效位置
      consecutiveSmallMoves: 0,      // 连续小移动计数
      driftAccumulated: 0,           // 累积漂移距离
      lastCorrectionTime: 0          // 最后校正时间
    };

    // 性能统计
    this.statistics = {
      positionUpdates: 0,
      corrections: 0,
      driftCorrections: 0,
      staticLocks: 0,
      rejectedMoves: 0,
      averageConfidence: 0,
      processingTime: [],
      driftDistance: 0
    };

    console.log('📍 Web位置计算器初始化完成');
  }

  /**
   * 初始化位置计算器
   * @param {Object} initialPosition - 初始位置 {x, y, z}
   */
  initialize(initialPosition = { x: 0, y: 0, z: 0 }) {
    this.initialPosition = { ...initialPosition };
    this.position = {
      ...initialPosition,
      heading: 0,
      confidence: 1.0,
      timestamp: Date.now()
    };

    this.pdrState.isInitialized = true;
    this.positionHistory = [{ ...this.position }];

    console.log('✅ 位置计算器已初始化，起始位置:', initialPosition);
  }

  /**
   * 更新位置计算
   * @param {Object} sensorData - 传感器数据
   * @returns {Object} 位置更新结果
   */
  update(sensorData) {
    if (!this.pdrState.isInitialized) {
      console.warn('⚠️ 位置计算器未初始化');
      return null;
    }

    const startTime = Date.now();

    try {
      // 运动分析
      const motionResult = this.motionAnalyzer.analyze(sensorData);

      // PDR位置更新
      let positionUpdate = null;
      if (motionResult && motionResult.stepDetected) {
        positionUpdate = this.updatePDRPosition(motionResult);
      }

      // 航向角更新
      if (motionResult && motionResult.headingUpdated) {
        this.updateHeading(motionResult.headingData);
      }

      // 平滑位置更新（即使没有步态也要更新状态）
      if (!positionUpdate) {
        positionUpdate = this.smoothPositionUpdate(sensorData);
      }

      // 传感器融合校正
      if (this.config.fusion.enabled) {
        positionUpdate = this.applySensorFusion(positionUpdate, sensorData);
      }

      // 质量验证
      const validationResult = this.validatePosition(positionUpdate);
      if (!validationResult.isValid) {
        console.warn('⚠️ 位置更新被拒绝:', validationResult.reason);
        return this.createUpdateResult(false, 'validation_failed');
      }

      // 应用位置更新
      this.applyPositionUpdate(positionUpdate);

      // 更新统计信息
      this.updateStatistics(Date.now() - startTime, positionUpdate);

      const result = this.createUpdateResult(true, 'success', positionUpdate);
      console.log('🔍 PositionCalculator 创建结果:', result);
      return result;

    } catch (error) {
      console.error('❌ 位置更新失败:', error);
      return this.createUpdateResult(false, 'error', null, error);
    }
  }

  /**
   * 更新PDR位置（带位置校正）
   */
  updatePDRPosition(motionResult) {
    const stepData = motionResult.stepData;
    const currentMotion = this.motionAnalyzer.getCurrentState();
    const timestamp = Date.now();

    // 获取步长和航向
    let stepLength = stepData.stepLength;
    const heading = currentMotion.heading;

    // 检查最小移动阈值
    if (this.config.correction.enabled) {
      const movementCheck = this.checkMinimumMovement(stepLength, stepData.confidence);
      if (!movementCheck.isValid) {
        // 移动距离太小，可能是噪声
        this.correctionState.consecutiveSmallMoves++;
        this.statistics.rejectedMoves++;
        return this.handleSmallMovement(timestamp);
      }
    }

    // PDR递推公式 - 使用地理坐标系位移计算
    const headingRad = (heading * Math.PI) / 180;
    const displacement = {
      deltaX: stepLength * Math.sin(headingRad), // 东西方向
      deltaY: stepLength * Math.cos(headingRad)  // 南北方向
    };
    let deltaX = displacement.deltaX;  // 东西方向位移
    let deltaY = displacement.deltaY;  // 南北方向位移

    // 应用漂移校正
    if (this.config.correction.driftCorrection) {
      const driftCorrection = this.calculateDriftCorrection(deltaX, deltaY, stepData.confidence);
      deltaX += driftCorrection.x;
      deltaY += driftCorrection.y;
    }

    // 计算原始新位置
    const rawNewPosition = {
      x: this.position.x + deltaX,
      y: this.position.y + deltaY,
      z: this.position.z, // 暂不考虑高度变化
      heading: heading,
      stepLength: stepLength,
      velocity: currentMotion.velocity,
      confidence: Math.min(stepData.confidence, currentMotion.confidence),
      timestamp: timestamp,
      stepDetected: true,
      source: 'pdr'
    };

    // 应用位置校正
    const correctedPosition = this.applyPositionCorrection(rawNewPosition);

    // 更新PDR状态
    this.pdrState.totalSteps++;
    this.pdrState.totalDistance += stepLength;
    this.pdrState.avgStepLength = this.pdrState.totalDistance / this.pdrState.totalSteps;
    this.pdrState.velocity = currentMotion.velocity;
    this.pdrState.lastStepPosition = { ...correctedPosition };

    // 更新校正状态
    this.updateCorrectionState(correctedPosition);

    return correctedPosition;
  }

  /**
   * 检查最小移动阈值
   */
  checkMinimumMovement(stepLength, confidence) {
    const threshold = this.config.correction.minMovementThreshold;

    // 基于置信度调整阈值
    const adjustedThreshold = threshold * (2.0 - confidence);

    if (stepLength < adjustedThreshold) {
      return {
        isValid: false,
        reason: 'below_threshold',
        actualDistance: stepLength,
        threshold: adjustedThreshold
      };
    }

    return { isValid: true };
  }

  /**
   * 处理小移动
   */
  handleSmallMovement(timestamp) {
    // 如果连续出现小移动，考虑进入静止状态
    if (this.correctionState.consecutiveSmallMoves > 3) {
      return this.enterStaticState(timestamp);
    }

    // 返回当前位置，但更新时间戳
    return {
      ...this.position,
      timestamp,
      stepDetected: false,
      source: 'static',
      reason: 'small_movement'
    };
  }

  /**
   * 进入静止状态
   */
  enterStaticState(timestamp) {
    if (!this.correctionState.isStatic) {
      this.correctionState.isStatic = true;
      this.correctionState.staticStartTime = timestamp;
      this.correctionState.staticPosition = { ...this.position };
      this.statistics.staticLocks++;
    }

    return {
      ...this.correctionState.staticPosition,
      timestamp,
      stepDetected: false,
      source: 'static_locked',
      isStatic: true
    };
  }

  /**
   * 计算漂移校正
   */
  calculateDriftCorrection(deltaX, deltaY, confidence) {
    if (!this.config.correction.driftCorrection) {
      return { x: 0, y: 0 };
    }

    // 计算与理想位置的偏差
    const totalDistance = Math.sqrt(
      Math.pow(this.position.x - this.initialPosition.x, 2) +
      Math.pow(this.position.y - this.initialPosition.y, 2)
    );

    // 如果累积距离超过阈值，应用漂移校正
    if (totalDistance > this.config.correction.driftThreshold) {
      const correctionFactor = this.config.correction.maxDriftCorrection / totalDistance;
      const adjustedFactor = correctionFactor * (1.0 - confidence); // 置信度低时校正更多

      return {
        x: -this.position.x * adjustedFactor * 0.1, // 10%的校正
        y: -this.position.y * adjustedFactor * 0.1
      };
    }

    return { x: 0, y: 0 };
  }

  /**
   * 应用位置校正
   */
  applyPositionCorrection(rawPosition) {
    if (!this.config.correction.enabled) {
      return rawPosition;
    }

    let correctedPosition = { ...rawPosition };

    // 静止位置锁定校正
    if (this.config.correction.staticPositionLock && this.correctionState.isStatic) {
      const distanceFromStatic = this.calculateDistance(
        rawPosition,
        this.correctionState.staticPosition
      );

      // 如果移动距离小于静止阈值，保持锁定
      if (distanceFromStatic < this.config.correction.staticThreshold) {
        correctedPosition = {
          ...this.correctionState.staticPosition,
          timestamp: rawPosition.timestamp,
          source: 'static_locked'
        };
      } else {
        // 移动距离足够大，解除静止锁定
        this.exitStaticState();
      }
    }

    // 位置平滑校正
    if (this.correctionState.lastValidPosition && correctedPosition.confidence < 0.7) {
      correctedPosition = this.applySmoothingCorrection(correctedPosition);
    }

    return correctedPosition;
  }

  /**
   * 退出静止状态
   */
  exitStaticState() {
    this.correctionState.isStatic = false;
    this.correctionState.staticStartTime = 0;
    this.correctionState.staticPosition = null;
    this.correctionState.consecutiveSmallMoves = 0;
  }

  /**
   * 应用平滑校正 - 优化平滑算法
   */
  applySmoothingCorrection(position) {
    const lastPos = this.correctionState.lastValidPosition;

    // 基于置信度动态调整平滑系数
    const baseSmoothing = 0.2; // 降低基础平滑系数
    const confidenceAdjustment = (1 - position.confidence) * 0.3;
    const smoothingFactor = Math.min(0.5, baseSmoothing + confidenceAdjustment);

    // 计算位移距离
    const deltaX = position.x - lastPos.x;
    const deltaY = position.y - lastPos.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // 如果位移很大，减少平滑以保持响应性
    const distanceAdjustment = Math.min(1.0, distance / 2.0); // 2米以上不平滑
    const finalSmoothingFactor = smoothingFactor * (1 - distanceAdjustment);

    return {
      ...position,
      x: lastPos.x + deltaX * (1 - finalSmoothingFactor),
      y: lastPos.y + deltaY * (1 - finalSmoothingFactor),
      source: 'smoothed'
    };
  }

  /**
   * 更新校正状态
   */
  updateCorrectionState(position) {
    // 更新最后有效位置
    if (position.confidence > 0.6) {
      this.correctionState.lastValidPosition = { ...position };
    }

    // 重置连续小移动计数
    this.correctionState.consecutiveSmallMoves = 0;

    // 更新漂移累积
    if (this.correctionState.lastValidPosition) {
      const distance = this.calculateDistance(position, this.correctionState.lastValidPosition);
      this.correctionState.driftAccumulated += distance;
    }
  }

  /**
   * 计算两点间距离
   */
  calculateDistance(pos1, pos2) {
    return Math.sqrt(
      Math.pow(pos1.x - pos2.x, 2) +
      Math.pow(pos1.y - pos2.y, 2)
    );
  }

  /**
   * 平滑位置更新（无步态时）- 修复停止后继续移动问题
   */
  smoothPositionUpdate(sensorData) {
    const currentMotion = this.motionAnalyzer.getCurrentState();
    const currentTime = Date.now();

    // 检查距离上次步态的时间
    const timeSinceLastStep = currentTime - (currentMotion.lastStepTime || 0);

    // 如果超过2秒没有步态，强制进入静止状态
    if (timeSinceLastStep > 2000) {
      return {
        ...this.position,
        heading: currentMotion.heading,
        velocity: 0, // 强制速度为0
        timestamp: currentTime,
        stepDetected: false,
        source: 'forced_stop',
        reason: 'no_step_timeout'
      };
    }

    // 检查是否真正在移动 - 更严格的条件
    const isReallyMoving = currentMotion.isMoving &&
      currentMotion.velocity > 0.05 && // 提高速度阈值
      currentMotion.confidence > 0.4 && // 需要足够的置信度
      timeSinceLastStep < 1500; // 1.5秒内有步态活动

    // 如果处于运动状态但没有检测到步态，进行平滑更新
    if (isReallyMoving) {
      const dt = sensorData.timestamp ?
        (sensorData.timestamp - this.position.timestamp) / 1000 : 0.02;

      if (dt > 0 && dt < 1.0) { // 合理的时间间隔
        const heading = currentMotion.heading;
        const headingRad = heading * Math.PI / 180;

        // 基于时间衰减速度，防止停止后继续移动
        let adjustedVelocity = currentMotion.velocity;

        // 应用时间衰减：距离上次步态越久，速度衰减越多
        const decayFactor = Math.max(0.1, 1.0 - (timeSinceLastStep / 2000));
        adjustedVelocity *= decayFactor;

        // 如果速度太小，直接停止
        if (adjustedVelocity < 0.02) {
          return {
            ...this.position,
            heading: heading,
            velocity: 0,
            timestamp: currentTime,
            stepDetected: false,
            source: 'velocity_decay_stop'
          };
        }

        const distance = adjustedVelocity * dt;

        // 限制最大移动距离，防止异常跳跃
        const maxDistance = 0.05; // 降低到5cm最大移动
        const clampedDistance = Math.min(distance, maxDistance);

        const deltaX = clampedDistance * Math.cos(headingRad);
        const deltaY = clampedDistance * Math.sin(headingRad);

        return {
          x: this.position.x + deltaX,
          y: this.position.y + deltaY,
          z: this.position.z,
          heading: heading,
          velocity: adjustedVelocity,
          confidence: Math.max(0.3, currentMotion.confidence * 0.8), // 降低置信度
          timestamp: currentTime,
          stepDetected: false,
          source: 'smooth_with_decay'
        };
      }
    }

    // 静止状态，只更新航向和时间戳，位置保持不变
    return {
      ...this.position,
      heading: currentMotion.heading,
      velocity: 0,
      timestamp: Date.now(),
      stepDetected: false,
      source: 'stationary'
    };
  }

  /**
   * 更新航向角 - 优化平滑处理
   */
  updateHeading(headingData) {
    if (!this.config.pdr.headingCorrection) {
      this.position.heading = headingData.heading;
      return;
    }

    const angleDiff = this.normalizeAngleDiff(headingData.heading - this.position.heading);

    // 动态调整平滑系数
    let smoothingFactor = this.config.pdr.smoothingFactor || 0.3;

    // 基于角度变化幅度调整平滑
    const absAngleDiff = Math.abs(angleDiff);
    if (absAngleDiff > 30) {
      // 大幅度变化时减少平滑，快速响应
      smoothingFactor = 0.1;
    } else if (absAngleDiff < 2) {
      // 小幅度变化时增加平滑，减少抖动
      smoothingFactor = 0.6;
    }

    // 基于置信度调整平滑
    const confidence = headingData.confidence || 0.5;
    smoothingFactor = smoothingFactor * confidence + (1 - confidence) * 0.8;

    this.position.heading = this.normalizeAngle(
      this.position.heading + angleDiff * smoothingFactor
    );
  }

  /**
   * 应用传感器融合
   */
  applySensorFusion(positionUpdate, sensorData) {
    if (!positionUpdate) return null;

    // 新方案：MLA校正已通过applyImmediateMlaCorrection直接处理
    // 这里只处理传统的传感器融合逻辑（如果需要的话）

    // 直接返回PDR结果，MLA校正不再通过传感器更新频率处理
    return positionUpdate;
  }

  /**
   * 寻找最近的MLA节点
   */
  findNearestMlaNode(position) {
    if (this.mlaNodes.length === 0) return null;

    let nearestNode = null;
    let minDistance = Infinity;

    for (const node of this.mlaNodes) {
      const distance = this.calculateDistance(position, node);
      if (distance < minDistance) {
        minDistance = distance;
        nearestNode = {
          ...node,
          distance: distance,
          weight: this.calculateNodeWeight(distance, node)
        };
      }
    }

    return nearestNode;
  }

  /**
   * 计算融合权重
   */
  calculateFusionWeights(pdrPosition, mlaNode) {
    let pdrWeight = this.config.fusion.pdrWeight;
    let mlaWeight = this.config.fusion.mlaWeight;

    if (this.config.fusion.adaptiveWeighting) {
      // 基于距离的自适应权重
      const distanceFactor = Math.max(0.1, Math.min(1.0,
        (this.config.fusion.outlierThreshold - mlaNode.distance) / this.config.fusion.outlierThreshold
      ));

      // 基于置信度的权重调整
      const confidenceFactor = pdrPosition.confidence || 0.5;

      // 基于历史校正效果的权重调整
      const correctionFactor = this.fusionState.qualityMetrics.accuracy;

      // 综合权重计算
      mlaWeight = this.config.fusion.mlaWeight * distanceFactor * correctionFactor;
      pdrWeight = 1.0 - mlaWeight;

      // 更新自适应权重
      this.fusionState.adaptiveWeights.pdr = pdrWeight;
      this.fusionState.adaptiveWeights.mla = mlaWeight;
    }

    return { pdr: pdrWeight, mla: mlaWeight };
  }

  /**
   * 位置融合
   */
  fusePositions(pdrPosition, mlaNode, weights) {
    const fusedX = pdrPosition.x * weights.pdr + mlaNode.x * weights.mla;
    const fusedY = pdrPosition.y * weights.pdr + mlaNode.y * weights.mla;
    const fusedZ = pdrPosition.z * weights.pdr + (mlaNode.z || pdrPosition.z) * weights.mla;

    // 限制校正幅度
    const correctionDistance = this.calculateDistance(
      { x: fusedX, y: fusedY, z: fusedZ },
      pdrPosition
    );

    if (correctionDistance > this.config.fusion.maxCorrection) {
      // 限制校正距离
      const correctionRatio = this.config.fusion.maxCorrection / correctionDistance;
      const limitedX = pdrPosition.x + (fusedX - pdrPosition.x) * correctionRatio;
      const limitedY = pdrPosition.y + (fusedY - pdrPosition.y) * correctionRatio;
      const limitedZ = pdrPosition.z + (fusedZ - pdrPosition.z) * correctionRatio;

      return {
        ...pdrPosition,
        x: limitedX,
        y: limitedY,
        z: limitedZ,
        confidence: Math.min(pdrPosition.confidence, 0.8), // 降低置信度
        corrected: true,
        correctionDistance: this.config.fusion.maxCorrection,
        source: 'fused_limited'
      };
    }

    return {
      ...pdrPosition,
      x: fusedX,
      y: fusedY,
      z: fusedZ,
      confidence: Math.min(1.0, pdrPosition.confidence + weights.mla * 0.3),
      corrected: true,
      correctionDistance: correctionDistance,
      source: 'fused'
    };
  }

  /**
   * 更新融合状态
   */
  updateFusionState(originalPosition, fusedPosition, mlaNode) {
    // 记录最后一次校正
    this.fusionState.lastCorrection = {
      timestamp: Date.now(),
      originalPosition: { ...originalPosition },
      fusedPosition: { ...fusedPosition },
      mlaNode: { ...mlaNode },
      correctionDistance: fusedPosition.correctionDistance
    };

    this.fusionState.correctionCount++;

    // 更新质量指标
    this.updateFusionQualityMetrics(fusedPosition);
  }

  /**
   * 更新融合质量指标
   */
  updateFusionQualityMetrics(fusedPosition) {
    // 一致性评估：基于连续校正的变化
    const recentCorrections = this.positionHistory
      .filter(p => p.corrected && Date.now() - p.timestamp < 10000)
      .slice(-5);

    if (recentCorrections.length >= 2) {
      const variations = recentCorrections.map((pos, index) => {
        if (index === 0) return 0;
        return this.calculateDistance(pos, recentCorrections[index - 1]);
      }).slice(1);

      const avgVariation = variations.reduce((a, b) => a + b, 0) / variations.length;
      this.fusionState.qualityMetrics.consistency = Math.max(0.1, 1.0 - avgVariation / 2.0);
    }

    // 精度评估：基于校正距离
    if (fusedPosition.correctionDistance !== undefined) {
      const accuracyScore = Math.max(0.1, 1.0 - fusedPosition.correctionDistance / 5.0);
      this.fusionState.qualityMetrics.accuracy =
        0.8 * this.fusionState.qualityMetrics.accuracy + 0.2 * accuracyScore;
    }

    // 可靠性评估：基于校正频率
    const recentTime = 30000; // 30秒
    const recentUpdates = this.positionHistory.filter(p =>
      Date.now() - p.timestamp < recentTime
    ).length;
    const recentCorrectionRate = recentCorrections.length / Math.max(1, recentUpdates);
    this.fusionState.qualityMetrics.reliability = Math.min(1.0, recentCorrectionRate * 2);
  }

  /**
   * 位置验证
   */
  validatePosition(positionUpdate) {
    if (!positionUpdate) {
      return { isValid: false, reason: '位置更新为空' };
    }

    // 检查坐标有效性
    if (!isFinite(positionUpdate.x) || !isFinite(positionUpdate.y) || !isFinite(positionUpdate.z)) {
      return { isValid: false, reason: '坐标包含无效值' };
    }

    // 检查置信度
    if (positionUpdate.confidence < this.config.quality.minConfidence) {
      return { isValid: false, reason: '置信度过低' };
    }

    // 检查位置跳跃
    if (this.positionHistory.length > 0) {
      const lastPosition = this.positionHistory[this.positionHistory.length - 1];
      const jumpDistance = this.calculateDistance(positionUpdate, lastPosition);
      const timeInterval = (positionUpdate.timestamp - lastPosition.timestamp) / 1000;

      if (timeInterval > 0) {
        const maxSpeed = 5.0; // 最大速度 5m/s
        const maxJump = maxSpeed * timeInterval;

        if (jumpDistance > maxJump) {
          return {
            isValid: false,
            reason: `位置跳跃过大: ${jumpDistance.toFixed(2)}m > ${maxJump.toFixed(2)}m`
          };
        }
      }
    }

    return { isValid: true };
  }

  /**
   * 应用位置更新
   */
  applyPositionUpdate(positionUpdate) {
    // 更新当前位置
    this.position = { ...positionUpdate };

    // 添加到历史记录
    this.positionHistory.push({ ...positionUpdate });

    // 限制历史长度
    if (this.positionHistory.length > this.maxHistoryLength) {
      this.positionHistory.shift();
    }
  }

  /**
   * 更新统计信息
   */
  updateStatistics(processingTime, positionUpdate) {
    this.statistics.positionUpdates++;
    this.statistics.processingTime.push(processingTime);

    if (this.statistics.processingTime.length > 100) {
      this.statistics.processingTime.shift();
    }

    if (positionUpdate && positionUpdate.corrected) {
      this.statistics.corrections++;
    }

    // 更新平均置信度
    if (positionUpdate && positionUpdate.confidence !== undefined) {
      const alpha = 0.1;
      this.statistics.averageConfidence = alpha * positionUpdate.confidence +
        (1 - alpha) * this.statistics.averageConfidence;
    }

    // 计算累积漂移距离
    this.statistics.driftDistance = this.calculateDistance(this.position, this.initialPosition);
  }

  /**
   * 创建更新结果
   */
  createUpdateResult(success, status, positionUpdate = null, error = null) {
    return {
      success,
      status,
      position: success ? { ...this.position } : null,
      stepDetected: positionUpdate ? positionUpdate.stepDetected : false,
      stepLength: positionUpdate ? positionUpdate.stepLength : null,
      velocity: positionUpdate ? positionUpdate.velocity : 0,
      confidence: this.position.confidence,
      corrected: positionUpdate ? positionUpdate.corrected : false,
      timestamp: Date.now(),
      error: error ? error.message : null
    };
  }

  // /**
  //  * 辅助函数：计算距离
  //  */
  // calculateDistance(pos1, pos2) {
  //   const dx = pos1.x - pos2.x;
  //   const dy = pos1.y - pos2.y;
  //   const dz = pos1.z - pos2.z;
  //   return Math.sqrt(dx * dx + dy * dy + dz * dz);
  // }

  /**
   * 辅助函数：计算节点权重
   */
  calculateNodeWeight(distance, node) {
    const maxDistance = this.config.fusion.outlierThreshold;
    return Math.max(0.1, (maxDistance - distance) / maxDistance);
  }

  /**
   * 辅助函数：角度差值标准化
   */
  normalizeAngleDiff(angleDiff) {
    while (angleDiff > 180) angleDiff -= 360;
    while (angleDiff < -180) angleDiff += 360;
    return angleDiff;
  }

  /**
   * 辅助函数：角度标准化
   */
  normalizeAngle(angle) {
    while (angle >= 360) angle -= 360;
    while (angle < 0) angle += 360;
    return angle;
  }

  // ==================== 公共API方法 ====================

  /**
   * 获取当前位置
   */
  getCurrentPosition() {
    return {
      ...this.position,
      motionState: this.motionAnalyzer.getCurrentState(),
      pdrState: { ...this.pdrState },
      fusionState: {
        adaptiveWeights: { ...this.fusionState.adaptiveWeights },
        qualityMetrics: { ...this.fusionState.qualityMetrics },
        correctionCount: this.fusionState.correctionCount
      }
    };
  }

  /**
   * 获取位置历史
   */
  getPositionHistory(limit = null) {
    return limit ? this.positionHistory.slice(-limit) : [...this.positionHistory];
  }

  /**
   * 设置MLA节点
   */
  setMlaNodes(nodes) {
    this.mlaNodes = [...nodes];
    console.log('📍 已设置MLA节点:', nodes.length, '个');
  }

  /**
   * 添加MLA节点
   */
  addMlaNode(node) {
    this.mlaNodes.push(node);
  }

  /**
   * 重置位置
   */
  reset(newPosition = null) {
    const resetPosition = newPosition || this.initialPosition;
    this.initialize(resetPosition);

    // 重置统计
    this.statistics = {
      positionUpdates: 0,
      corrections: 0,
      averageConfidence: 0,
      processingTime: [],
      driftDistance: 0
    };

    // 重置融合状态
    this.fusionState.lastCorrection = null;
    this.fusionState.correctionCount = 0;
    this.fusionState.adaptiveWeights = {
      pdr: this.config.fusion.pdrWeight,
      mla: this.config.fusion.mlaWeight
    };

    // 重置运动分析器
    this.motionAnalyzer.reset();

    console.log('🔄 位置计算器已重置');
  }

  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    this.motionAnalyzer.updateConfig(config);
  }

  /**
   * 获取性能统计
   */
  getStatistics() {
    const avgProcessingTime = this.statistics.processingTime.length > 0 ?
      this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length : 0;

    return {
      ...this.statistics,
      averageProcessingTime: avgProcessingTime,
      correctionRate: this.statistics.positionUpdates > 0 ?
        this.statistics.corrections / this.statistics.positionUpdates : 0,
      motionStatistics: this.motionAnalyzer.getPerformanceReport(),
      fusionQuality: { ...this.fusionState.qualityMetrics }
    };
  }
}