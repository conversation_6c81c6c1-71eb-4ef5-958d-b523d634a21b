<template>
  <div>
    <van-image v-show="props.isShow" :src="passengerUrl_selected" class="passenger"></van-image>
    <van-image v-show="!props.isShow" :src="passengerUrl" class="passenger"></van-image>
  </div>
</template>

<script setup lang="ts">
import passengerUrl from "@/assets/image/passenger.svg";
import passengerUrl_selected from "@/assets/image/passenger_selected.svg";
const props = defineProps({
  isShow: {
    type: Boolean,
    default: true,
  },
});
</script>

<style scoped lang="scss">
.passenger {
  width: 44px;
  height: 35px;
  position: absolute;
  left: 17px;
  bottom: 280px;
  z-index: 999;
}
</style>
