html,
body,
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  touch-action: pan-x pan-y;
  user-scalable: no;
}

#app canvas {
  background-color: #e2f0fe;
  width: 100%;
  height: 100vh;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;

  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

}
/* css2d标签 */
.building-label{
  height: 22px;
}
.label-element {
  position: fixed;
  pointer-events: none;
}
.label-element p {
  transform: translate(-50%, -50%);
  font-weight: 500;
  font-size: 16px;
  font-family: 'Inter', sans-serif;
  color: #3d5699;
  /* 使用 text-shadow 创建白色描边效果 */
  text-shadow: 
      -1px -1px 0 #fff,  /* 左上 */
      1px -1px 0 #fff,   /* 右上 */
      -1px 1px 0 #fff,   /* 左下 */
      1px 1px 0 #fff;    /* 右下 */
}