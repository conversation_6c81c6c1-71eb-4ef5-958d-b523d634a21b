import * as THREE from "three"
import { AccurateCoordinateConverter } from "./MapboxCoordinateConverter"
import { SceneConfig } from "./SceneConfig"
import mapboxgl from "mapbox-gl"
import { eventBus } from "./EventBus"

export class CameraController {
	private map: mapboxgl.Map
	private converter: AccurateCoordinateConverter =
		new AccurateCoordinateConverter(
			SceneConfig.getMapCenter(),
			SceneConfig.getModelRotation(),
			SceneConfig.getBaseAltitude()
		)
	constructor(map: mapboxgl.Map) {
		this.map = map
		eventBus.on("setCamera", (cameraPosition: THREE.Vector3, lookAtThreePoint: THREE.Vector3) => {
			this.setCamera(cameraPosition, lookAtThreePoint)
		})
	}
	lookAtThreePoint(point: THREE.Vector3) {
		const geoPoint = this.converter.worldToGeo(point)
		// const mercator = mapboxgl.MercatorCoordinate.fromLngLat(
		// 	[geoPoint.longitude, geoPoint.latitude],
		// 	point.z
		// )
		const camera = this.map.getFreeCameraOptions()
		camera.lookAtPoint([geoPoint.longitude, geoPoint.latitude])

		this.map.setFreeCameraOptions(camera)
	}
	setCameraPosition(position: THREE.Vector3) {
		const geoPoint = this.converter.worldToGeo(position)
		const camera = this.map.getFreeCameraOptions()

		const mercator = mapboxgl.MercatorCoordinate.fromLngLat(
			[geoPoint.longitude, geoPoint.latitude],
			position.z
		)

		camera.position = mercator
		this.map.setFreeCameraOptions(camera)
	}

	setCamera(cameraPosition: THREE.Vector3, lookAtThreePoint: THREE.Vector3) {
		const cameraPositionLngLat = this.converter.worldToGeo(cameraPosition)
		const lookAtThreePointLngLat = this.converter.worldToGeo(lookAtThreePoint)
		console.log(cameraPositionLngLat, lookAtThreePointLngLat);
		
		const camera = this.map.getFreeCameraOptions()
		const mercatorPosition = mapboxgl.MercatorCoordinate.fromLngLat(
			[cameraPositionLngLat.longitude, cameraPositionLngLat.latitude],
			cameraPosition.y
		)
		camera.position = mercatorPosition
		camera.lookAtPoint([lookAtThreePointLngLat.longitude, lookAtThreePointLngLat.latitude])
		this.map.setFreeCameraOptions(camera)
	}
}
