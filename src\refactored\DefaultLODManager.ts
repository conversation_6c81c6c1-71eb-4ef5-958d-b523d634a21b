import * as THREE from "three"
import { LODLevel } from "./LODTypes"
import { gsap } from "gsap"
import { eventBus } from "./EventBus"
import { AccurateCoordinateConverter } from "./MapboxCoordinateConverter"
import { SceneConfig } from "./SceneConfig"
// 动画 - 显影
import {
	checkAnimation,
	fadeInOrFadeOut,
} from "@/utils/modelVisibilityAnimation"
// 拆楼
import { splitModelStart, splitModelEnd } from "@/utils/splitModelAnimation"

/**
 * LOD事件类型
 */
export enum LODEventType {
	MODEL_ADDED = "model-added",
	MODEL_REMOVED = "model-removed",
	VISIBILITY_CHANGED = "visibility-changed",
	LOD_UPDATED = "lod-updated",
	LEVEL_CHANGED = "level-changed",
	CONFIG_CHANGED = "config-changed",
	WATER_VISIBILITY_CHANGED = "water-visibility-change",
}

/**
 * 建筑LOD模型数据
 */
interface BuildingLODModel {
	id: string
	name: string
	position: THREE.Vector3
	object: THREE.Object3D
	isVisible: boolean
	distanceToCamera: number
	// 建筑相关属性
	isExterior?: boolean
	isInterior?: boolean
	buildingId?: string
	floor?: number
	isDefault?: boolean
	hasInterior?: boolean
	switchDistance?: number
	fadeDistance?: number
}

/**
 * 最佳建筑信息
 */
interface BestBuildingInfo {
	id: string
	name: string
	distance: number
	pixelSize: number
	pixelRatio: number
	isInsideBoundingBox: boolean
	centerRaycastScore: number
	totalScore: number
	exteriorModel: BuildingLODModel
}

/**
 * 极简LOD管理器 - 只负责基础的显隐控制
 */
export class DefaultLODManager {
	private scene: THREE.Scene
	private models: Map<string, BuildingLODModel> = new Map()
	private enabled: boolean = true
	private controlEnabled: boolean = true
	private layerManager?: any

	private map: mapboxgl.Map

	// 楼层管理
	private currentFloors: Map<string, number> = new Map() // buildingId -> floor
	private defaultSwitchDistance: number = 500
	private defaultFadeDistance: number = 10

	// 当前LOD建筑管理
	private currentLODBuildingId: string | null = null
	private currentLODBuildingInfo: {
		id: string
		name: string
		floor: number
		distance: number
	} | null = null

	// 拆楼模式状态
	private explodedMode: boolean = false
	private explodedBuildingId: string | null = null
	private originalPositions: Map<string, THREE.Vector3> = new Map()
	private explodedOffset: number = 100 // Y轴偏移距离
	private originalCameraSettings: any = null // 保存原始相机设置

	// GSAP动画相关
	private explodedTimeline: gsap.core.Timeline | null = null
	private animationDuration: number = 0.3 // 动画持续时间（秒）

	// 相机位置保存
	private originalCameraPosition: THREE.Vector3 | null = null

	// 节流相关
	private lastUpdateTime: number = 0
	private updateThrottleDelay: number = 150 // 150ms节流延迟

	// 包围盒渲染相关
	private boundingBoxHelpers: Map<string, THREE.Object3D> = new Map()
	private showBoundingBoxes: boolean = false

	//缓存observer
	private observerPosition: THREE.Vector3 = new THREE.Vector3()

	constructor(scene: THREE.Scene, map: mapboxgl.Map) {
		this.map = map
		this.scene = scene
		console.log("✅ 建筑LOD管理器已初始化")

		// 从localStorage恢复楼层设置
		// this.loadFloorSettings()

		eventBus.on("LODControlEnabled", (enabled: boolean) => {
			this.controlEnabled = enabled
		})
		// eventBus.on("upFloor", () => {
		// 	this.switchToFloorInExplodedMode(this.currentLODBuildingId!, this.currentFloors.get(this.currentLODBuildingId!)! + 1)
		// })
		// eventBus.on("downFloor", () => {
		// 	this.switchToFloorInExplodedMode(this.currentLODBuildingId!, this.currentFloors.get(this.currentLODBuildingId!)! - 1)
		// })
	}

	/**
	 * 事件监听方法 - 支持 lodManager.on() 调用
	 */
	on(event: string, handler: (...args: any[]) => void): void {
		eventBus.on(event, handler)
	}

	/**
	 * 移除事件监听
	 */
	off(event: string, handler: (...args: any[]) => void): void {
		eventBus.off(event, handler)
	}

	/**
	 * 触发事件
	 */
	private emit(event: string, ...args: any[]): void {
		eventBus.emit(event, ...args)
	}

	/**
	 * 获取真实的observer位置（适用于Mapbox环境）
	 */
	private getObserverPosition(camera: THREE.Camera): THREE.Vector3 {
		try {
			// 方法1：通过投影矩阵逆变换获取observer位置
			const camInverseProjection = camera.projectionMatrix
				.clone()
				.invert()
			const observerPosition = new THREE.Vector3().applyMatrix4(
				camInverseProjection
			)

			// 验证结果是否合理
			if (observerPosition.length() > 0 && isFinite(observerPosition.x)) {
				return observerPosition
			}
		} catch (error) {
			console.warn("⚠️ 投影矩阵逆变换失败:", error)
		}

		// 降级方案：使用相机position
		console.warn("⚠️ 使用降级方案：相机position")
		return camera.position.clone()
	}

	/**
	 * 检查相机是否在包围盒内
	 */
	private isCameraInsideBoundingBox(
		boundingBox: THREE.Box3,
		cameraPosition: THREE.Vector3
	): boolean {
		return boundingBox.containsPoint(cameraPosition)
	}

	/**
	 * 检查模型是否在相机前方（用于处理极近距离的情况）
	 */
	private isModelInFrontOfCamera(
		model: BuildingLODModel,
		camera: THREE.Camera
	): boolean {
		if (!model.object) return false

		// 获取相机的前方向量
		const cameraDirection = new THREE.Vector3()
		camera.getWorldDirection(cameraDirection)

		// 获取模型包围盒中心
		const boundingBox = new THREE.Box3().setFromObject(model.object)
		const modelCenter = new THREE.Vector3()
		boundingBox.getCenter(modelCenter)

		// 计算从相机到模型中心的向量
		const cameraPosition = this.getObserverPosition(camera)
		const toModel = new THREE.Vector3().subVectors(
			modelCenter,
			cameraPosition
		)

		// 检查模型是否在相机前方（点积大于0）
		const dotProduct = cameraDirection.dot(toModel.normalize())
		return dotProduct > 0.1 // 允许一定的角度偏差
	}

	/**
	 * 从屏幕中心发起射线检测，返回射线与模型的交点信息
	 * 参照MapApplication.raycast方法实现，适配Mapbox环境
	 */
	private performCenterRaycast(
		model: BuildingLODModel,
		camera: THREE.Camera
	): {
		hasIntersection: boolean
		distance: number
		intersectionPoint?: THREE.Vector3
	} {
		if (!model.object) {
			return { hasIntersection: false, distance: Infinity }
		}

		try {
			// 创建射线投射器
			const raycaster = new THREE.Raycaster()

			// 屏幕中心坐标 (0, 0) 在NDC坐标系中
			const screenCenter = new THREE.Vector2(0, 0)

			// 参照MapApplication.raycast方法，使用相机投影矩阵逆变换
			const camInverseProjection = camera.projectionMatrix
				.clone()
				.invert()

			// 计算相机位置
			const cameraPosition = new THREE.Vector3().applyMatrix4(
				camInverseProjection
			)

			// 计算屏幕中心在世界坐标中的位置
			const centerPosition = new THREE.Vector3(
				screenCenter.x,
				screenCenter.y,
				1
			).applyMatrix4(camInverseProjection)

			// 计算射线方向
			const viewDirection = centerPosition
				.clone()
				.sub(cameraPosition)
				.normalize()

			// 设置射线
			raycaster.set(cameraPosition, viewDirection)

			// 收集模型的所有可射线检测的对象
			const intersectableObjects: THREE.Object3D[] = []
			model.object.traverse((child) => {
				if (child instanceof THREE.Mesh) {
					intersectableObjects.push(child)
				}
			})

			if (intersectableObjects.length === 0) {
				return { hasIntersection: false, distance: Infinity }
			}

			// 执行射线检测
			const intersections = raycaster.intersectObjects(
				intersectableObjects,
				false
			)

			if (intersections.length > 0) {
				const closestIntersection = intersections[0]
				return {
					hasIntersection: true,
					distance: closestIntersection.distance,
					intersectionPoint: closestIntersection.point,
				}
			}

			return { hasIntersection: false, distance: Infinity }
		} catch (error) {
			console.warn(`⚠️ 射线检测失败 ${model.name}:`, error)
			return { hasIntersection: false, distance: Infinity }
		}
	}

	/**
	 * 计算模型在屏幕中心的权重分数
	 */
	private calculateCenterRaycastScore(
		model: BuildingLODModel,
		camera: THREE.Camera
	): number {
		const raycastResult = this.performCenterRaycast(model, camera)

		if (!raycastResult.hasIntersection) {
			return 0 // 射线未命中，权重为0
		}

		// 基于距离计算权重，距离越近权重越高
		const maxDistance = 1200 // 最大有效距离
		const normalizedDistance =
			Math.min(raycastResult.distance, maxDistance) / maxDistance
		const distanceScore = 1 - normalizedDistance // 距离越近分数越高

		// 基础权重分数 - 大幅提高射线检测的权重
		let score = distanceScore * 800 // 从500提高到800

		// 如果射线命中，给予额外的中心权重加成
		score += 500 // 从300提高到500，命中加成

		console.log(
			`🎯 射线检测 ${
				model.name
			}: 命中距离=${raycastResult.distance.toFixed(
				1
			)}m, 权重=${score.toFixed(1)}`
		)

		return score
	}

	/**
	 * 计算模型包围盒在屏幕上的像素大小
	 */
	private calculateScreenPixelSize(
		model: BuildingLODModel,
		camera: THREE.Camera,
		screenWidth: number,
		screenHeight: number
	): { pixelSize: number; isInsideBoundingBox: boolean } {
		if (!model.object) return { pixelSize: 0, isInsideBoundingBox: false }

		// 计算模型的包围盒
		const boundingBox = new THREE.Box3().setFromObject(model.object)
		if (boundingBox.isEmpty())
			return { pixelSize: 0, isInsideBoundingBox: false }

		// 获取相机位置
		const cameraPosition = this.getObserverPosition(camera)

		// 检查相机是否在包围盒内
		const isInsideBoundingBox = this.isCameraInsideBoundingBox(
			boundingBox,
			cameraPosition
		)

		// 如果相机在包围盒内，返回最大像素大小
		if (isInsideBoundingBox) {
			console.log(`📦 相机在建筑包围盒内: ${model.name}`)
			return {
				pixelSize: screenWidth * screenHeight, // 返回全屏大小
				isInsideBoundingBox: true,
			}
		}

		// 获取包围盒的8个顶点
		const corners = [
			new THREE.Vector3(
				boundingBox.min.x,
				boundingBox.min.y,
				boundingBox.min.z
			),
			new THREE.Vector3(
				boundingBox.max.x,
				boundingBox.min.y,
				boundingBox.min.z
			),
			new THREE.Vector3(
				boundingBox.min.x,
				boundingBox.max.y,
				boundingBox.min.z
			),
			new THREE.Vector3(
				boundingBox.max.x,
				boundingBox.max.y,
				boundingBox.min.z
			),
			new THREE.Vector3(
				boundingBox.min.x,
				boundingBox.min.y,
				boundingBox.max.z
			),
			new THREE.Vector3(
				boundingBox.max.x,
				boundingBox.min.y,
				boundingBox.max.z
			),
			new THREE.Vector3(
				boundingBox.min.x,
				boundingBox.max.y,
				boundingBox.max.z
			),
			new THREE.Vector3(
				boundingBox.max.x,
				boundingBox.max.y,
				boundingBox.max.z
			),
		]

		// 将所有顶点投影到屏幕坐标（包括视锥体外的点）
		const screenPoints: THREE.Vector2[] = []
		const projectionMatrix = camera.projectionMatrix
		const matrixWorldInverse = camera.matrixWorldInverse

		// 记录有多少点在视锥体内
		let pointsInFrustum = 0

		for (const corner of corners) {
			// 转换到相机坐标系
			const viewPoint = corner.clone().applyMatrix4(matrixWorldInverse)

			// 投影到NDC坐标 (-1 到 1)
			const ndcPoint = viewPoint.clone().applyMatrix4(projectionMatrix)

			// 检查是否在视锥体内
			const inFrustum =
				Math.abs(ndcPoint.x) <= 1 &&
				Math.abs(ndcPoint.y) <= 1 &&
				ndcPoint.z >= -1 &&
				ndcPoint.z <= 1

			if (inFrustum) {
				pointsInFrustum++
			}

			// 将所有点都投影到屏幕坐标（即使在视锥体外）
			// 这样可以处理模型部分在视锥体外的情况
			const screenX = (ndcPoint.x + 1) * 0.5 * screenWidth
			const screenY = (1 - ndcPoint.y) * 0.5 * screenHeight
			screenPoints.push(new THREE.Vector2(screenX, screenY))
		}

		// 如果没有任何顶点在视锥体内，但相机距离很近，可能是相机在模型内部或非常接近
		if (pointsInFrustum === 0) {
			// 计算相机到包围盒中心的距离
			const center = new THREE.Vector3()
			boundingBox.getCenter(center)
			const distanceToCenter = cameraPosition.distanceTo(center)

			// 计算包围盒的最大尺寸
			const size = new THREE.Vector3()
			boundingBox.getSize(size)
			const maxSize = Math.max(size.x, size.y, size.z)

			// 如果相机距离包围盒中心很近（小于包围盒最大尺寸的1.5倍），
			// 并且模型在相机前方，认为模型占据大部分屏幕
			if (
				distanceToCenter < maxSize * 1.5 &&
				this.isModelInFrontOfCamera(model, camera)
			) {
				// console.log(
				// 	`📏 相机非常接近模型 ${
				// 		model.name
				// 	}，距离: ${distanceToCenter.toFixed(
				// 		1
				// 	)}m，包围盒大小: ${maxSize.toFixed(1)}m`
				// )
				return {
					pixelSize: screenWidth * screenHeight * 1, // 假设占据80%屏幕
					isInsideBoundingBox: false,
				}
			}

			// 否则返回0
			return { pixelSize: 0, isInsideBoundingBox: false }
		}

		// 计算屏幕上包围盒的大小
		let minX = Infinity,
			maxX = -Infinity
		let minY = Infinity,
			maxY = -Infinity

		for (const point of screenPoints) {
			minX = Math.min(minX, point.x)
			maxX = Math.max(maxX, point.x)
			minY = Math.min(minY, point.y)
			maxY = Math.max(maxY, point.y)
		}

		// 将包围盒裁剪到屏幕范围内
		minX = Math.max(0, Math.min(screenWidth, minX))
		maxX = Math.max(0, Math.min(screenWidth, maxX))
		minY = Math.max(0, Math.min(screenHeight, minY))
		maxY = Math.max(0, Math.min(screenHeight, maxY))

		// 计算屏幕上的像素面积
		const pixelWidth = Math.max(0, maxX - minX)
		const pixelHeight = Math.max(0, maxY - minY)
		const pixelArea = pixelWidth * pixelHeight

		// 如果计算出的面积很小，但有部分顶点在视锥体内，可能是因为模型很大很近
		// 在这种情况下，给一个合理的最小占比
		if (
			pixelArea < screenWidth * screenHeight * 0.01 &&
			pointsInFrustum > 0
		) {
			// 根据在视锥体内的顶点数量来估算占比
			const estimatedRatio = Math.min(0.5, (pointsInFrustum / 8) * 0.3) // 最多50%，按顶点比例估算
			const estimatedPixelArea =
				screenWidth * screenHeight * estimatedRatio

			console.log(
				`📐 模型 ${model.name} 使用估算面积: ${
					estimatedRatio * 100
				}% (${pointsInFrustum}/8 顶点在视锥体内)`
			)
			return { pixelSize: estimatedPixelArea, isInsideBoundingBox: false }
		}

		return { pixelSize: pixelArea, isInsideBoundingBox: false }
	}

	/**
	 * 计算屏幕像素占比
	 */
	private calculateScreenPixelRatio(
		pixelSize: number,
		screenWidth: number,
		screenHeight: number
	): number {
		const totalScreenPixels = screenWidth * screenHeight
		return pixelSize / totalScreenPixels
	}

	/**
	 * 获取屏幕尺寸
	 */
	private getScreenSize(): { width: number; height: number } {
		// 尝试从多个来源获取屏幕尺寸

		// 方法1：从window对象获取
		if (typeof window !== "undefined") {
			return {
				width: window.innerWidth,
				height: window.innerHeight,
			}
		}

		// 方法2：从document获取
		if (typeof document !== "undefined" && document.documentElement) {
			return {
				width: document.documentElement.clientWidth,
				height: document.documentElement.clientHeight,
			}
		}

		// 降级方案：使用默认值
		return {
			width: 1920,
			height: 1080,
		}
	}

	/**
	 * 创建包围盒辅助线
	 */
	private createBoundingBoxHelper(
		model: BuildingLODModel
	): THREE.LineSegments | null {
		if (!model.object) return null

		const boundingBox = new THREE.Box3().setFromObject(model.object)
		if (boundingBox.isEmpty()) return null

		// 直接创建包围盒的线框几何体
		const size = new THREE.Vector3()
		boundingBox.getSize(size)
		const center = new THREE.Vector3()
		boundingBox.getCenter(center)

		// 创建包围盒的边框几何体
		const geometry = new THREE.EdgesGeometry(
			new THREE.BoxGeometry(size.x, size.y, size.z)
		)

		// 创建线材质
		const material = new THREE.LineBasicMaterial({
			color: model.isExterior ? 0xff0000 : 0x00ff00, // 外立面红色，室内绿色
			transparent: true,
			opacity: 0.8,
			linewidth: 2,
		})

		// 创建线段对象
		const wireframe = new THREE.LineSegments(geometry, material)
		wireframe.position.copy(center)
		wireframe.name = `boundingBox-${model.id}`

		console.log(
			`📦 创建包围盒线框: ${model.name}, 大小: ${size.x.toFixed(
				1
			)}x${size.y.toFixed(1)}x${size.z.toFixed(
				1
			)}, 中心: ${center.x.toFixed(1)},${center.y.toFixed(
				1
			)},${center.z.toFixed(1)}`
		)

		return wireframe
	}

	/**
	 * 显示/隐藏包围盒
	 */
	toggleBoundingBoxes(show: boolean): void {
		this.showBoundingBoxes = show

		if (show) {
			// 为所有外立面模型创建包围盒
			this.models.forEach((model) => {
				if (
					model.isExterior &&
					!this.boundingBoxHelpers.has(model.id)
				) {
					const boxHelper = this.createBoundingBoxHelper(model)
					if (boxHelper) {
						this.boundingBoxHelpers.set(model.id, boxHelper)
						this.scene.add(boxHelper)
						console.log(`📦 创建包围盒: ${model.name}`)
					}
				}
			})
		} else {
			// 隐藏所有包围盒
			this.boundingBoxHelpers.forEach((boxHelper) => {
				this.scene.remove(boxHelper)
				// 清理几何体和材质
				if (boxHelper instanceof THREE.LineSegments) {
					boxHelper.geometry.dispose()
					if (Array.isArray(boxHelper.material)) {
						boxHelper.material.forEach((mat) => mat.dispose())
					} else {
						boxHelper.material.dispose()
					}
				}
			})
			this.boundingBoxHelpers.clear()
			console.log("📦 隐藏所有包围盒")
		}
	}

	/**
	 * 更新包围盒显示
	 */
	private updateBoundingBoxDisplay(): void {
		if (!this.showBoundingBoxes) return

		// 更新现有包围盒的颜色，突出显示当前LOD建筑
		this.boundingBoxHelpers.forEach((boxHelper, modelId) => {
			const model = this.models.get(modelId)
			if (
				model &&
				model.isExterior &&
				boxHelper instanceof THREE.LineSegments
			) {
				// 如果是当前LOD建筑，使用黄色高亮
				const isCurrentLOD =
					model.buildingId === this.currentLODBuildingId
				const color = isCurrentLOD ? 0xffff00 : 0xff0000

				const material = boxHelper.material as THREE.LineBasicMaterial
				material.color.setHex(color)
				material.opacity = isCurrentLOD ? 0.8 : 0.3
			}
		})
	}

	// /**
	//  * 从localStorage加载楼层设置
	//  */
	// private loadFloorSettings(): void {
	// 	try {
	// 		// const saved = localStorage.getItem("building-floors")
	// 		// if (saved) {
	// 		// 	const data = JSON.parse(saved)
	// 		// 	this.currentFloors = new Map(Object.entries(data))
	// 		// 	console.log("✅ 已加载楼层设置:", this.currentFloors)
	// 		// }
	// 	} catch (error) {
	// 		console.warn("⚠️ 加载楼层设置失败:", error)
	// 	}
	// }

	// /**
	//  * 保存楼层设置到localStorage
	//  */
	// private saveFloorSettings(): void {
	// 	try {
	// 		const data = Object.fromEntries(this.currentFloors)
	// 		localStorage.setItem("building-floors", JSON.stringify(data))
	// 	} catch (error) {
	// 		console.warn("⚠️ 保存楼层设置失败:", error)
	// 	}
	// }

	/**
	 * 设置建筑当前楼层
	 */
	setCurrentFloor(buildingId: string, floor: number): void {
		this.currentFloors.set(buildingId, floor)
		// this.saveFloorSettings()

		console.log(`🏢 设置建筑 ${buildingId} 当前楼层: ${floor}`)

		// 检查是否在拆楼模式下
		if (this.explodedMode && this.explodedBuildingId === buildingId) {
			console.log(`🏗️ 拆楼模式下的楼层切换，调整相机高度而非显隐`)
			// 拆楼模式：调整相机高度而不是显示/隐藏楼层
			this.switchToFloorInExplodedMode(buildingId, floor)
		} else {
			// 正常模式：更新楼层显示
			this.updateBuildingDisplay(buildingId)
		}

		// 触发楼层变更事件
		this.emit("floor-changed", {
			buildingId,
			floor,
			timestamp: Date.now(),
		})
	}

	/**
	 * 获取建筑当前楼层
	 */
	getCurrentFloor(buildingId: string): number {
		let floor = this.currentFloors.get(buildingId)
		//如果没有设置 则返回最低楼层
		if (!floor) {
			const floors = Array.from(this.models.values())
				.filter(
					(m) => m.buildingId === buildingId && m.floor !== undefined
				)
				.map((m) => m.floor!) // 非空断言
			if (floors.length > 0) {
				floor = Math.min(...floors)
				this.currentFloors.set(buildingId, floor)
			}
		}
		return this.currentFloors.get(buildingId)!
	}

	/**
	 * 设置LayerManager引用
	 */
	setLayerManager(layerManager: any): void {
		this.layerManager = layerManager
		console.log("✅ LOD系统已连接LayerManager")
	}

	/**
	 * 启用/禁用LOD系统
	 */
	setEnabled(enabled: boolean): void {
		const oldEnabled = this.enabled
		this.enabled = enabled

		if (!enabled) {
			// 禁用时显示所有模型
			this.models.forEach((model) => {
				this.setModelVisible(model.id, true)
			})
		}

		console.log(`${enabled ? "🔛 启用" : "🔴 禁用"}LOD系统`)

		// 触发配置变更事件
		this.emit(LODEventType.CONFIG_CHANGED, {
			enabled,
			oldEnabled,
			timestamp: Date.now(),
		})
	}

	isEnabled(): boolean {
		return this.enabled
	}

	/**
	 * 添加模型（兼容原接口）
	 */
	addModelWithObject(modelData: {
		id: string
		name: string
		position: THREE.Vector3
		object: THREE.Object3D
		priority?: number
		isExterior?: boolean
		isInterior?: boolean
		buildingId?: string
		floor?: number
		isDefault?: boolean
		hasInterior?: boolean
		switchDistance?: number
		fadeDistance?: number
		hasParent?: boolean
	}): string {
		const model: BuildingLODModel = {
			id: modelData.id,
			name: modelData.name,
			position: modelData.position.clone(),
			object: modelData.object,
			isVisible: true,
			distanceToCamera: 0,
			// 建筑相关属性
			isExterior: modelData.isExterior,
			isInterior: modelData.isInterior,
			buildingId: modelData.buildingId,
			floor: modelData.floor,
			isDefault: modelData.isDefault,
			hasInterior: modelData.hasInterior,
			switchDistance:
				modelData.switchDistance || this.defaultSwitchDistance,
			fadeDistance: modelData.fadeDistance || this.defaultFadeDistance,
		}

		this.models.set(modelData.id, model)
		console.log(`✅ 添加模型: ${modelData.id}`)

		// 如果是外立面模型且正在显示包围盒，创建包围盒
		if (model.isExterior && this.showBoundingBoxes) {
			console.log("????????????????????????")

			const boxHelper = this.createBoundingBoxHelper(model)
			if (boxHelper) {
				this.boundingBoxHelpers.set(model.id, boxHelper)
				this.scene.add(boxHelper)
				console.log(`📦 自动创建包围盒: ${model.name}`)
			}
		}

		// 触发模型添加事件
		this.emit(LODEventType.MODEL_ADDED, {
			modelId: modelData.id,
			modelName: modelData.name,
			position: modelData.position,
			timestamp: Date.now(),
		})

		return modelData.id
	}

	/**
	 * 移除模型
	 */
	removeModel(id: string): void {
		const model = this.models.get(id)
		if (!model) return

		// 从场景中移除
		if (model.object && model.object.parent) {
			model.object.parent.remove(model.object)
		}

		// 移除对应的包围盒
		const boxHelper = this.boundingBoxHelpers.get(id)
		if (boxHelper) {
			this.scene.remove(boxHelper)
			// 清理几何体和材质
			if (boxHelper instanceof THREE.LineSegments) {
				boxHelper.geometry.dispose()
				if (Array.isArray(boxHelper.material)) {
					boxHelper.material.forEach((mat) => mat.dispose())
				} else {
					boxHelper.material.dispose()
				}
			}
			this.boundingBoxHelpers.delete(id)
			console.log(`📦 移除包围盒: ${id}`)
		}

		this.models.delete(id)
		console.log(`🗑️ 移除模型: ${id}`)

		// 触发模型移除事件
		this.emit(LODEventType.MODEL_REMOVED, {
			modelId: id,
			timestamp: Date.now(),
		})
	}

	/**
	 * 设置模型可见性
	 * - 新需求 对显影的模型进行 显影动画控制 - 加入渐显渐隐
	 * - 需求实现 gsap 控制 - THREE.Mesh 的 opacity
	 */
	setModelVisible(id: string, visible: boolean, duration?: number): void {
		const model = this.models.get(id)
		if (!model) {
			console.warn(`⚠️ 模型不存在: ${id}`)
			return
		}
		// 记录旧状态
		const oldVisible = model.isVisible
		// 更新状态
		model.isVisible = visible
		// 校验对应模型是否存在执行中的动画
		const _flag = checkAnimation(model.object, visible, id)
		if (!_flag) return

		// 校验模型 - 显影状态是否需要发送改变
		if (model.object.visible === visible) return

		// 通过LayerManager控制（如果可用）
		if (
			this.layerManager &&
			typeof this.layerManager.setNodeVisibility === "function"
		) {
			try {
				this.layerManager.setNodeVisibility(id, visible, duration)
				// console.log(`📡 LayerManager设置: ${id} → ${visible}`)
			} catch (error) {
				console.warn(`⚠️ LayerManager设置失败: ${id}`, error)
				// 降级到直接控制
				this.setObjectVisible(model.object, visible)
			}
		} else {
			// 直接控制3D对象
			this.setObjectVisible(model.object, visible, id)
		}

		// console.log(`${visible ? "👁️ 显示" : "🙈 隐藏"}模型: ${id}`)

		// 触发可见性变更事件（只有当状态真正改变时）
		if (oldVisible !== visible) {
			this.emit(LODEventType.VISIBILITY_CHANGED, {
				modelId: id,
				visible,
				oldVisible,
				timestamp: Date.now(),
			})
		}
	}

	/**
	 * 直接设置3D对象可见性
	 */
	private setObjectVisible(
		object: THREE.Object3D,
		visible: boolean,
		id?: string
	): void {
		// 旧 - 逻辑 - 直接修改
		// object.visible = visible
		// // 递归设置所有子对象
		// object.traverse((child) => {
		// 	child.visible = visible
		// })
		// 新 - 加入动画
		fadeInOrFadeOut(object, 1, visible, id)
	}

	/**
	 * 设置模型LOD级别（兼容原接口）
	 */
	setModelLevel(id: string, level: LODLevel): void {
		const model = this.models.get(id)
		if (!model) return

		const oldLevel = model.isVisible ? LODLevel.HIGH : LODLevel.HIDDEN
		const visible = level !== LODLevel.HIDDEN

		this.setModelVisible(id, visible)

		// 触发级别变更事件
		if (oldLevel !== level) {
			this.emit(LODEventType.LEVEL_CHANGED, {
				modelId: id,
				oldLevel,
				newLevel: level,
				distance: model.distanceToCamera,
				timestamp: Date.now(),
			})
		}
	}

	/**
	 * 获取模型LOD级别（兼容原接口）
	 */
	getModelLevel(id: string): LODLevel {
		const model = this.models.get(id)
		return model && model.isVisible ? LODLevel.HIGH : LODLevel.HIDDEN
	}

	/**
	 * 获取模型可见性
	 */
	isModelVisible(id: string): boolean {
		const model = this.models.get(id)
		return model ? model.isVisible : false
	}

	/**
	 * 显示模型
	 */
	showModel(id: string): void {
		this.setModelVisible(id, true)
	}

	/**
	 * 隐藏模型
	 */
	hideModel(id: string): void {
		this.setModelVisible(id, false)
	}

	/**
	 * 切换模型可见性
	 */
	toggleModel(id: string): void {
		const model = this.models.get(id)
		if (model) {
			this.setModelVisible(id, !model.isVisible)
		}
	}

	/**
	 * 显示所有模型
	 */
	showAllModels(): void {
		console.log("👁️ 显示所有模型")
		this.models.forEach((model) => {
			this.setModelVisible(model.id, true)
		})
	}

	/**
	 * 隐藏所有模型
	 */
	hideAllModels(): void {
		console.log("🙈 隐藏所有模型")
		this.models.forEach((model) => {
			this.setModelVisible(model.id, false)
		})
	}

	/**
	 * 切换到LOD建筑
	 */
	private switchToLODBuilding(
		buildingId: string,
		buildingName: string
	): void {
		// 退出之前的LOD建筑
		if (
			this.currentLODBuildingId &&
			this.currentLODBuildingId !== buildingId
		) {
			this.exitLODMode()
		}

		// 设置新的LOD建筑
		this.currentLODBuildingId = buildingId
		const currentFloor = this.getCurrentFloor(buildingId)

		this.currentLODBuildingInfo = {
			id: buildingId,
			name: buildingName,
			floor: currentFloor,
			distance: 0, // 将在updateBuildingDisplay中更新
		}

		// 更新建筑显示
		this.updateBuildingDisplay(buildingId)

		console.log(
			`🏢 进入LOD模式: ${buildingName} (${buildingId}), 楼层: ${currentFloor}`
		)

		// 触发事件
		this.emit("lod-building-entered", {
			buildingId,
			buildingName,
			floor: currentFloor,
			timestamp: Date.now(),
		})
	}

	/**
	 * 退出LOD模式
	 */
	private exitLODMode(): void {
		if (!this.currentLODBuildingId) return

		const previousBuilding = this.currentLODBuildingInfo

		// 显示所有外立面，隐藏所有室内
		if (this.controlEnabled)
			this.models.forEach((model) => {
				if (model.buildingId === this.currentLODBuildingId) {
					if (model.isExterior) {
						this.setModelVisible(model.id, true, 0.8)
					} else if (model.isInterior) {
						this.setModelVisible(model.id, false)
					}
				}
			})

		console.log(
			`🏛️ 退出LOD模式: ${previousBuilding?.name} (${this.currentLODBuildingId})`
		)

		// 清空当前LOD建筑
		this.currentLODBuildingId = null
		this.currentLODBuildingInfo = null

		// 触发事件
		this.emit("lod-building-exited", {
			previousBuilding,
			timestamp: Date.now(),
		})
	}

	/**
	 * 更新单个建筑的显示状态
	 */
	private updateBuildingDisplay(buildingId: string): void {
		if (!this.controlEnabled) return
		// 获取该建筑的所有模型
		const buildingModels = Array.from(this.models.values()).filter(
			(model) => model.buildingId === buildingId
		)

		if (buildingModels.length === 0) return

		const exteriorModel = buildingModels.find((m) => m.isExterior)
		const interiorModels = buildingModels.filter((m) => m.isInterior)

		if (!exteriorModel || interiorModels.length === 0) {
			// 没有室内模型的建筑，不参与LOD管理，保持原状
			return
		}

		// 获取当前楼层
		const currentFloor = this.getCurrentFloor(buildingId)

		// 找到当前楼层的模型
		const currentFloorModel = interiorModels.find(
			(m) => m.floor === currentFloor
		)
		const defaultFloorModel = interiorModels.find((m) => m.isDefault)
		const targetFloorModel =
			currentFloorModel || defaultFloorModel || interiorModels[0]

		// 更新当前LOD建筑信息中的距离
		if (
			this.currentLODBuildingInfo &&
			this.currentLODBuildingInfo.id === buildingId
		) {
			this.currentLODBuildingInfo.distance =
				exteriorModel.distanceToCamera
		}

		// 根据距离决定显示外立面还是室内
		const distance = exteriorModel.distanceToCamera
		const switchDistance =
			exteriorModel.switchDistance || this.defaultSwitchDistance

		if (distance > switchDistance) {
			// 远距离：显示外立面，隐藏所有室内
			this.setModelVisible(exteriorModel.id, true, 0.8)
			interiorModels.forEach((interior) => {
				this.setModelVisible(interior.id, false)
			})
			console.log(`🏛️ 远距离显示外立面: ${buildingId}`)
		} else {
			// 近距离：隐藏外立面，显示当前楼层
			this.setModelVisible(exteriorModel.id, false, 0.8)
			interiorModels.forEach((interior) => {
				const shouldShow = interior.id === targetFloorModel?.id
				this.setModelVisible(interior.id, shouldShow)
			})
			console.log(
				`🏢 近距离显示室内楼层: ${buildingId}, 楼层: ${targetFloorModel?.floor}`
			)
		}
	}

	/**
	 * 基于屏幕像素大小的建筑LOD更新
	 * 只对有室内模型的建筑进行LOD管理
	 */
	updateLOD(camera: THREE.Camera): void {
		if (!this.enabled) return

		// 拆楼模式下不执行LOD更新
		if (this.explodedMode) {
			console.log("🏗️ 拆楼模式下跳过LOD更新")
			return
		}

		// 节流检查
		const now = Date.now()
		if (now - this.lastUpdateTime < this.updateThrottleDelay) {
			return
		}
		this.lastUpdateTime = now

		const updateStartTime = Date.now()
		let changedModels = 0

		// 🔧 关键修复：在Mapbox中获取真实的observer位置
		const observerPosition = this.getObserverPosition(camera)

		const flag = observerPosition.equals(this.observerPosition)
		if (flag) {
			return
		}
		this.observerPosition = observerPosition

		// 获取屏幕尺寸
		const screenSize = this.getScreenSize()
		const screenWidth = screenSize.width
		const screenHeight = screenSize.height

		// 更新所有模型的距离和屏幕像素大小
		this.models.forEach((model) => {
			const distance = model.position.distanceTo(observerPosition)
			model.distanceToCamera = distance
		})

		// 🔧 新逻辑：找到屏幕像素占比最高且距离合适的有室内模型的建筑
		let bestBuilding: BestBuildingInfo | null = null

		// 收集所有有室内模型的建筑
		const buildingsWithInterior = new Map<string, BuildingLODModel>()

		this.models.forEach((model) => {
			if (model.isExterior && model.buildingId) {
				// 检查是否有室内模型
				const hasInterior = Array.from(this.models.values()).some(
					(m) => m.isInterior && m.buildingId === model.buildingId
				)

				if (hasInterior) {
					buildingsWithInterior.set(model.buildingId, model)
				}
			}
		})

		// 计算每个建筑的屏幕像素大小、占比和射线检测分数
		buildingsWithInterior.forEach((exteriorModel, buildingId) => {
			// 计算屏幕像素大小
			const pixelResult = this.calculateScreenPixelSize(
				exteriorModel,
				camera,
				screenWidth,
				screenHeight
			)

			// 计算屏幕像素占比
			const pixelRatio = this.calculateScreenPixelRatio(
				pixelResult.pixelSize,
				screenWidth,
				screenHeight
			)

			// 计算射线检测分数
			const centerRaycastScore = this.calculateCenterRaycastScore(
				exteriorModel,
				camera
			)

			// 距离检查：确保建筑在合理的距离范围内
			const distance = exteriorModel.distanceToCamera
			const isDistanceAppropriate = distance <= this.defaultSwitchDistance // 放宽距离限制

			// 特殊处理：如果相机在包围盒内，优先选择该建筑
			if (pixelResult.isInsideBoundingBox) {
				bestBuilding = {
					id: buildingId,
					name: exteriorModel.name,
					distance,
					pixelSize: pixelResult.pixelSize,
					pixelRatio: 1.0, // 设置为100%占比
					isInsideBoundingBox: true,
					centerRaycastScore: centerRaycastScore,
					totalScore: 50000, // 包围盒内给予最高分数，确保绝对优先级
					exteriorModel,
				}
				console.log(
					`📦 相机在建筑内部，强制选择: ${exteriorModel.name}`
				)
				return // 直接返回，不再检查其他建筑
			}

			// 计算综合分数
			// 像素占比权重 + 射线检测权重（射线检测占绝对主导）
			const pixelScore = pixelRatio * 1000 // 从3000大幅降低到1000，大幅减少像素占比权重
			const raycastScore = centerRaycastScore * 5 // 射线分数从3倍大幅提高到5倍
			const totalScore = pixelScore + raycastScore

			// console.log(
			// 	`🏢 建筑评分 ${exteriorModel.name}: 像素占比=${(
			// 		pixelRatio * 100
			// 	).toFixed(2)}% (${pixelScore.toFixed(
			// 		1
			// 	)}分), 射线分数=${centerRaycastScore.toFixed(
			// 		1
			// 	)}分 (加权后${raycastScore.toFixed(
			// 		1
			// 	)}分), 总分=${totalScore.toFixed(1)}分`
			// )

			// 选择综合分数最高且距离合适的建筑
			if (
				isDistanceAppropriate &&
				totalScore > 50 && // 最小阈值：综合分数>50分
				(!bestBuilding || totalScore > bestBuilding.totalScore)
			) {
				bestBuilding = {
					id: buildingId,
					name: exteriorModel.name,
					distance,
					pixelSize: pixelResult.pixelSize,
					pixelRatio,
					isInsideBoundingBox: false,
					centerRaycastScore: raycastScore, // 使用加权后的射线分数
					totalScore,
					exteriorModel,
				}
			}
		})

		// 处理LOD切换
		const previousLODBuilding = this.currentLODBuildingId

		if (
			bestBuilding &&
			((bestBuilding as BestBuildingInfo).isInsideBoundingBox ||
				(bestBuilding as BestBuildingInfo).totalScore > 100)
		) {
			// 相机在包围盒内或综合分数超过阈值（100分）
			const building = bestBuilding as BestBuildingInfo
			// 有建筑满足LOD条件
			if (this.currentLODBuildingId !== building.id) {
				// 切换到新建筑
				this.switchToLODBuilding(building.id, building.name)
				changedModels++
				// if (building.isInsideBoundingBox) {
				// 	console.log(
				// 		`🏢 相机在包围盒内切换LOD建筑: ${
				// 			building.name
				// 		}, 距离: ${building.distance.toFixed(
				// 			1
				// 		)}m, 总分: ${building.totalScore.toFixed(1)}`
				// 	)
				// } else {
				// 	console.log(
				// 		`🏢 基于综合评分切换LOD建筑: ${
				// 			building.name
				// 		}, 像素占比: ${(building.pixelRatio * 100).toFixed(
				// 			2
				// 		)}%, 射线分数: ${building.centerRaycastScore.toFixed(
				// 			1
				// 		)}, 总分: ${building.totalScore.toFixed(
				// 			1
				// 		)}, 距离: ${building.distance.toFixed(1)}m`
				// 	)
				// }
			} else {
				// 更新当前建筑显示
				this.updateBuildingDisplay(building.id)
			}
		} else {
			// 没有建筑满足LOD条件，退出LOD模式
			if (this.currentLODBuildingId) {
				this.exitLODMode()
				eventBus.emit("exitLod")
				changedModels++
				console.log("🏛️ 基于综合评分退出LOD模式：无建筑满足分数阈值")
			}
		}

		// 更新包围盒显示
		this.updateBoundingBoxDisplay()

		// 节流触发LOD更新事件
		this.throttledEmitLODUpdate({
			observerPosition: observerPosition.clone(),
			cameraPosition: camera.position.clone(),
			totalModels: this.models.size,
			changedModels,
			visibleModels: this.getVisibleModelCount(),
			currentLODBuilding: this.currentLODBuildingInfo,
			previousLODBuilding,
			bestBuilding: bestBuilding
				? {
						id: (bestBuilding as BestBuildingInfo).id,
						distance: (bestBuilding as BestBuildingInfo).distance,
						pixelSize: (bestBuilding as BestBuildingInfo).pixelSize,
						pixelRatio: (bestBuilding as BestBuildingInfo)
							.pixelRatio,
						isInsideBoundingBox: (bestBuilding as BestBuildingInfo)
							.isInsideBoundingBox,
						centerRaycastScore: (bestBuilding as BestBuildingInfo)
							.centerRaycastScore,
						totalScore: (bestBuilding as BestBuildingInfo)
							.totalScore,
				  }
				: null,
			updateTime: Date.now() - updateStartTime,
			timestamp: Date.now(),
		})
	}

	/**
	 * 节流的LOD更新事件发射
	 */
	private throttledEmitLODUpdate = (() => {
		let lastEmitTime = 0
		const emitThrottleDelay = 100 // 100ms节流延迟

		return (data: any) => {
			const now = Date.now()
			if (now - lastEmitTime >= emitThrottleDelay) {
				lastEmitTime = now
				this.emit(LODEventType.LOD_UPDATED, data)
			}
		}
	})()

	/**
	 * 获取模型信息
	 */
	getModel(id: string): BuildingLODModel | undefined {
		return this.models.get(id)
	}

	/**
	 * 获取所有模型
	 */
	getAllModels(): BuildingLODModel[] {
		return Array.from(this.models.values())
	}

	/**
	 * 获取可见模型数量
	 */
	getVisibleModelCount(): number {
		return Array.from(this.models.values()).filter((m) => m.isVisible)
			.length
	}

	/**
	 * 获取总模型数量
	 */
	getTotalModelCount(): number {
		return this.models.size
	}

	/**
	 * 获取简单统计信息（兼容原接口）
	 */
	getPerformanceStats(): {
		totalModels: number
		visibleModels: number
		levelCounts: {
			high: number
			medium: number
			low: number
			hidden: number
		}
	} {
		const visible = this.getVisibleModelCount()
		const total = this.getTotalModelCount()

		return {
			totalModels: total,
			visibleModels: visible,
			levelCounts: {
				high: visible,
				medium: 0,
				low: 0,
				hidden: total - visible,
			},
		}
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		console.log("🧹 清理LOD资源...")

		// 移除所有模型
		this.models.forEach((model) => {
			if (model.object && model.object.parent) {
				model.object.parent.remove(model.object)
			}
		})

		// 清理所有包围盒
		this.boundingBoxHelpers.forEach((boxHelper) => {
			this.scene.remove(boxHelper)
			// 清理几何体和材质
			if (boxHelper instanceof THREE.LineSegments) {
				boxHelper.geometry.dispose()
				if (Array.isArray(boxHelper.material)) {
					boxHelper.material.forEach((mat) => mat.dispose())
				} else {
					boxHelper.material.dispose()
				}
			}
		})
		this.boundingBoxHelpers.clear()

		this.models.clear()
		console.log("✅ LOD清理完成")
	}

	/**
	 * 获取所有建筑ID
	 */
	getAllBuildingIds(): string[] {
		const buildingIds = new Set<string>()
		this.models.forEach((model) => {
			if (model.buildingId) {
				buildingIds.add(model.buildingId)
			}
		})
		return Array.from(buildingIds)
	}

	/**
	 * 获取建筑的所有楼层
	 */
	getBuildingFloors(buildingId: string): number[] {
		const floors = new Set<number>()
		let totalModels = 0
		let buildingModels = 0
		let interiorModels = 0

		this.models.forEach((model) => {
			totalModels++
			if (model.buildingId === buildingId) {
				buildingModels++
				if (model.isInterior) {
					interiorModels++
					if (model.floor) {
						floors.add(model.floor)
					}
				}
			}
		})

		const result = Array.from(floors).sort((a, b) => a - b)

		console.log(`🏢 获取建筑楼层详情:`, {
			buildingId,
			totalModels,
			buildingModels,
			interiorModels,
			floors: result,
			allBuildingIds: Array.from(
				new Set(
					Array.from(this.models.values())
						.map((m) => m.buildingId)
						.filter(Boolean)
				)
			),
		})

		return result
	}

	/**
	 * 检查建筑是否有室内模型
	 */
	hasBuildingInterior(buildingId: string): boolean {
		return Array.from(this.models.values()).some(
			(model) => model.buildingId === buildingId && model.isInterior
		)
	}

	/**
	 * 获取当前所有楼层设置
	 */
	getAllFloorSettings(): Record<string, number> {
		return Object.fromEntries(this.currentFloors)
	}

	/**
	 * 获取当前LOD建筑信息
	 */
	getCurrentLODBuildingInfo(): {
		id: string
		name: string
		floor: number
		distance: number
	} | null {
		return this.currentLODBuildingInfo
			? { ...this.currentLODBuildingInfo }
			: null
	}

	/**
	 * 检查是否有建筑在LOD模式中
	 */
	isInLODMode(): boolean {
		return this.currentLODBuildingId !== null
	}

	/**
	 * 切换当前LOD建筑的楼层
	 */
	switchLODBuildingFloor(floor: number): boolean {
		if (!this.currentLODBuildingId || !this.currentLODBuildingInfo) {
			console.warn("⚠️ 没有建筑在LOD模式中")
			return false
		}

		const buildingId = this.currentLODBuildingId
		const availableFloors = this.getBuildingFloors(buildingId)

		if (!availableFloors.includes(floor)) {
			console.warn(
				`⚠️ 建筑 ${buildingId} 没有楼层 ${floor}，可用楼层: ${availableFloors.join(
					","
				)}`
			)
			return false
		}

		// 设置楼层
		this.setCurrentFloor(buildingId, floor)

		// 更新LOD建筑信息
		this.currentLODBuildingInfo.floor = floor

		// 立即更新显示
		this.updateBuildingDisplay(buildingId)

		console.log(`🏢 切换LOD建筑楼层: ${buildingId} → ${floor}F`)

		// 触发事件
		this.emit("lod-building-floor-changed", {
			buildingId,
			floor,
			availableFloors,
			timestamp: Date.now(),
		})

		return true
	}

	/**
	 * 设置拆楼模式
	 */
	setExplodedMode(buildingId: string, enabled: boolean): void {
		console.log(`🏗️ 设置拆楼模式: ${buildingId}, 启用: ${enabled}`)

		if (enabled) {
			this.enterExplodedMode(buildingId)
		} else {
			this.exitExplodedMode()
		}
	}

	/**
	 * 进入拆楼模式
	 */
	private enterExplodedMode(buildingId: string): void {
		// 如果已经在拆楼模式中，先退出
		if (this.explodedMode) {
			this.exitExplodedMode()
		}

		//合适视角
		// const targetModelPosition = this.getModel(`${buildingId}-exterior`)?.position
		// if(targetModelPosition){
		// 	const converter = new AccurateCoordinateConverter(SceneConfig.getMapCenter(),SceneConfig.getModelRotation(),SceneConfig.getModelAltitude())
		// 	const geo = converter.worldToGeo(targetModelPosition)
		// 	this.map.flyTo({
		// 		center:[geo.longitude,geo.latitude],
		// 		zoom: 18,

		// 	})
		// }

		this.explodedMode = true
		this.explodedBuildingId = buildingId

		console.log(`🏗️ 进入拆楼模式: ${buildingId}`)

		// 关闭LOD功能
		this.setEnabled(false)
		console.log(`🔒 拆楼模式：LOD功能已禁用`)

		// 隐藏除指定建筑室内模型外的所有模型（包括地面、底图）
		this.hideAllExceptBuildingInterior(buildingId)

		// 隐藏Mapbox底图
		this.hideMapboxBase()

		// 隐藏水体
		this.hideWater()

		// 保存原始位置并应用Y轴拆楼偏移
		this.applyExplodedOffsetY(buildingId)

		// 启用拆楼模式相机控制
		this.enableExplodedCameraControl(buildingId)

		// 强制显示当前建筑的所有模型（确保可见性）

		// 开启 - 拆楼监听 - 室内模型
		const interiorModels = Array.from(this.models.values()).filter(
			(model) => model.buildingId === buildingId && model.isInterior
		)
		const shellModel = Array.from(this.models.values()).filter(
			(model) => model.buildingId === buildingId && !model.isInterior
		)[0] // 外壳
		splitModelStart(
			shellModel,
			interiorModels,
			this.getCurrentFloor(buildingId)
		)
		// 触发事件
		this.emit("exploded-mode-entered", {
			buildingId,
			timestamp: Date.now(),
		})
	}

	/**
	 * 退出拆楼模式
	 */
	private exitExplodedMode(): void {
		if (!this.explodedMode || !this.explodedBuildingId) return

		const buildingId = this.explodedBuildingId

		console.log(`🏗️ 退出拆楼模式: ${buildingId}`)

		// 恢复原始位置（带动画）
		this.restoreOriginalPositionsAnimated()

		// 注意：其他恢复操作将在动画完成后执行（在restoreOriginalPositionsAnimated的onComplete中）

		// 移除 拆楼 监听
		splitModelEnd()

		// 触发事件
		this.emit("exploded-mode-exited", {
			buildingId,
			timestamp: Date.now(),
		})
	}

	/**
	 * 隐藏除指定建筑外的所有模型
	 */
	private hideAllExceptBuilding(buildingId: string): void {
		let hiddenCount = 0
		let keptCount = 0

		this.models.forEach((model) => {
			if (model.buildingId !== buildingId) {
				this.setModelVisible(model.id, false)
				hiddenCount++
			} else {
				keptCount++
			}
		})

		console.log(
			`🏗️ 拆楼模式模型过滤: 保留 ${keptCount} 个模型 (建筑: ${buildingId}), 隐藏 ${hiddenCount} 个其他模型`
		)
	}

	/**
	 * 隐藏除指定建筑和基础模型外的所有模型
	 */
	private hideAllExceptBuildingAndBase(buildingId: string): void {
		let hiddenCount = 0
		let keptCount = 0
		let baseCount = 0

		this.models.forEach((model) => {
			// 保留当前建筑的模型
			if (model.buildingId === buildingId) {
				keptCount++
				return
			}

			// 保留基础模型（没有buildingId或者是地面等基础设施）
			if (
				!model.buildingId ||
				model.id.includes("ground") ||
				model.id.includes("base") ||
				model.id.includes("terrain") ||
				model.id.includes("floor") ||
				model.id.includes("landscape")
			) {
				baseCount++
				return
			}

			// 隐藏其他建筑模型
			this.setModelVisible(model.id, false)
			hiddenCount++
		})

		console.log(
			`🏗️ 拆楼模式智能过滤: 保留建筑 ${keptCount} 个 (${buildingId}), 保留基础 ${baseCount} 个, 隐藏其他 ${hiddenCount} 个`
		)
	}

	/**
	 * 隐藏除指定建筑室内模型外的所有模型（包括地面、底图）
	 */
	private hideAllExceptBuildingInterior(buildingId: string): void {
		let hiddenCount = 0
		let interiorCount = 0

		this.models.forEach((model) => {
			// 只保留指定建筑的室内模型
			if (model.buildingId === buildingId && model.isInterior) {
				interiorCount++
				// 确保室内模型可见
				this.setModelVisible(model.id, true, 0.5)
				return
			}

			// 隐藏所有其他模型（包括外立面、地面、其他建筑等）
			this.setModelVisible(model.id, false, 0.01)
			hiddenCount++
		})

		console.log(
			`🏗️ 拆楼模式严格过滤: 只保留 ${interiorCount} 个室内模型 (建筑: ${buildingId}), 隐藏 ${hiddenCount} 个其他模型`
		)
	}

	/**
	 * 应用拆楼偏移（原Z轴版本，保留备用）
	 */
	private applyExplodedOffset(buildingId: string): void {
		const interiorModels = Array.from(this.models.values()).filter(
			(model) => model.buildingId === buildingId && model.isInterior
		)

		console.log(
			`🏗️ 找到室内模型数量: ${interiorModels.length}`,
			interiorModels.map((m) => ({
				id: m.id,
				floor: m.floor,
				visible: m.isVisible,
			}))
		)

		if (interiorModels.length === 0) {
			console.warn(`⚠️ 建筑 ${buildingId} 没有找到室内模型`)
			return
		}

		// 按楼层排序
		interiorModels.sort((a, b) => (a.floor || 0) - (b.floor || 0))

		interiorModels.forEach((model, index) => {
			// 保存原始位置
			if (model.object) {
				this.originalPositions.set(
					model.id,
					model.object.position.clone()
				)

				// 应用Z轴偏移
				const offset = index * this.explodedOffset
				model.object.position.z += offset

				// 确保室内模型可见 - 强制显示
				console.log(`🏗️ 强制显示室内模型: ${model.id}`)
				this.setModelVisible(model.id, true)

				// 双重检查：直接设置3D对象可见性
				if (model.object) {
					this.setObjectVisible(model.object, true)
				}

				console.log(
					`🏗️ 楼层偏移完成: ${model.id}, 楼层: ${model.floor}, 偏移: ${offset}, 可见: ${model.isVisible}`
				)
			} else {
				console.warn(`⚠️ 室内模型 ${model.id} 没有3D对象`)
			}
		})

		// 隐藏外立面
		const exteriorModel = Array.from(this.models.values()).find(
			(model) => model.buildingId === buildingId && model.isExterior
		)
		if (exteriorModel) {
			console.log(`🏗️ 隐藏外立面: ${exteriorModel.id}`)
			this.setModelVisible(exteriorModel.id, false)
		}
	}

	/**
	 * 应用Y轴拆楼偏移（使用GSAP动画）
	 */
	private applyExplodedOffsetY(buildingId: string): void {
		const interiorModels = Array.from(this.models.values()).filter(
			(model) => model.buildingId === buildingId && model.isInterior
		)

		console.log(
			`🏗️ GSAP拆楼动画 - 找到室内模型数量: ${interiorModels.length}`,
			interiorModels.map((m) => ({
				id: m.id,
				floor: m.floor,
				visible: m.isVisible,
			}))
		)

		if (interiorModels.length === 0) {
			console.warn(`⚠️ 建筑 ${buildingId} 没有找到室内模型`)
			return
		}

		// 按楼层排序
		interiorModels.sort((a, b) => (a.floor || 0) - (b.floor || 0))

		// 清除之前的动画
		if (this.explodedTimeline) {
			this.explodedTimeline.kill()
		}

		// 创建GSAP时间轴
		this.explodedTimeline = gsap.timeline({
			onComplete: () => {
				console.log(`✅ GSAP拆楼动画完成`)

				// 动画完成后，调整相机到当前选中楼层的视角
				this.adjustCameraToCurrentFloorInExplodedMode(buildingId)

				this.emit("exploded-animation-complete", {
					buildingId: this.explodedBuildingId,
					modelsCount: interiorModels.length,
					timestamp: Date.now(),
				})
			},
			onUpdate: () => {
				// 触发渲染更新
				this.emit("render-update-required", {
					reason: "gsap-exploded-animation",
					timestamp: Date.now(),
				})
			},
		})

		interiorModels.forEach((model, index) => {
			if (model.object) {
				// 保存原始位置
				this.originalPositions.set(
					model.id,
					model.object.position.clone()
				)

				// 确保室内模型可见
				this.setModelVisible(model.id, true)

				// 计算目标Y位置
				const targetOffset = index * this.explodedOffset
				const targetY = model.object.position.y + targetOffset

				console.log(
					`🏗️ GSAP准备动画: ${model.id}, 楼层: ${model.floor}, 目标Y偏移: ${targetOffset}`
				)

				// 添加到时间轴，所有楼层同时开始
				this.explodedTimeline!.to(
					model.object.position,
					{
						y: targetY,
						duration: this.animationDuration,
						ease: "power2.out", // easeOutQuad
					},
					0 // 所有楼层同时开始，无延迟
				)
			} else {
				console.warn(`⚠️ 室内模型 ${model.id} 没有3D对象`)
			}
		})

		console.log(
			`🎬 启动GSAP拆楼动画，所有楼层同时移动，持续时间: ${this.animationDuration}秒`
		)
	}

	/**
	 * 启用拆楼模式相机控制
	 */
	private enableExplodedCameraControl(buildingId: string): void {
		console.log(`🎥 启用拆楼模式相机控制: ${buildingId}`)

		// 发射保存相机位置的事件
		this.emit("save-camera-position", {
			buildingId,
			timestamp: Date.now(),
		})

		// 通过事件通知相机控制变更
		this.emit("exploded-camera-control", {
			enabled: true,
			buildingId,
			mode: "rotate-only", // 只允许绕模型旋转，禁用平移和缩放
			timestamp: Date.now(),
		})
	}

	/**
	 * 拆楼模式下的楼层切换（移动相机到指定楼层高度）
	 */
	public switchToFloorInExplodedMode(
		buildingId: string,
		targetFloor: number
	): void {
		if (!this.explodedMode || this.explodedBuildingId !== buildingId) {
			console.warn(`⚠️ 不在拆楼模式或建筑ID不匹配: ${buildingId}`)
			return
		}

		// 找到目标楼层的模型
		const targetFloorModel = Array.from(this.models.values()).find(
			(model) =>
				model.buildingId === buildingId &&
				model.isInterior &&
				model.floor === targetFloor
		)

		if (!targetFloorModel || !targetFloorModel.object) {
			console.warn(
				`⚠️ 找不到目标楼层模型: ${buildingId}, 楼层: ${targetFloor}`
			)
			return
		}

		// 旧 - 根据 默认设置的高度来调整 - 异常情况,出现视角挪动会导致y计算异常
		// 计算目标相机高度（楼层模型的Y位置）
		const targetCameraY = targetFloorModel.object.position.y
		console.log(
			`🎥 拆楼模式楼层切换: ${buildingId} → ${targetFloor}F, 目标高度: ${targetCameraY}`
		)
		// 发射相机高度调整事件
		this.emit("exploded-camera-height-change", {
			buildingId,
			targetFloor,
			targetHeight: targetCameraY,
			timestamp: Date.now(),
		})
	}

	/**
	 * 拆楼动画完成后，调整相机到当前选中楼层的视角
	 */
	private adjustCameraToCurrentFloorInExplodedMode(buildingId: string): void {
		// 获取当前选中的楼层
		const currentFloor = this.getCurrentFloor(buildingId)

		console.log(
			`🎥 拆楼动画完成，调整相机到当前选中楼层: ${buildingId} → ${currentFloor}F`
		)

		// 稍微延迟一下，确保拆楼动画完全完成
		setTimeout(() => {
			// 调用楼层切换方法来移动相机
			this.switchToFloorInExplodedMode(buildingId, currentFloor)
		}, 100) // 延迟100ms
	}

	/**
	 * 禁用拆楼模式相机控制
	 */
	private disableExplodedCameraControl(): void {
		console.log(`🎥 禁用拆楼模式相机控制`)

		// 发射恢复相机位置的事件
		this.emit("restore-camera-position", {
			buildingId: this.explodedBuildingId,
			timestamp: Date.now(),
		})

		// 通过事件通知相机控制恢复
		this.emit("exploded-camera-control", {
			enabled: false,
			buildingId: null,
			mode: "normal", // 恢复正常相机控制
			timestamp: Date.now(),
		})
	}

	/**
	 * 恢复原始位置
	 */
	private restoreOriginalPositions(): void {
		this.originalPositions.forEach((originalPos, modelId) => {
			const model = this.models.get(modelId)
			if (model?.object) {
				model.object.position.copy(originalPos)
			}
		})
	}

	/**
	 * 恢复原始位置（使用GSAP动画）
	 */
	private restoreOriginalPositionsAnimated(): void {
		console.log(`🔄 开始GSAP恢复动画流程`)

		// 清除之前的动画
		if (this.explodedTimeline) {
			this.explodedTimeline.kill()
			console.log(`🗑️ 清除之前的GSAP动画`)
		}

		// 创建恢复动画时间轴
		this.explodedTimeline = gsap.timeline({
			onComplete: () => {
				console.log(`✅ GSAP恢复动画完成`)

				// 最终位置确认
				this.originalPositions.forEach((originalPos, modelId) => {
					const model = this.models.get(modelId)
					if (model?.object) {
						model.object.position.copy(originalPos)
					}
				})

				// 动画完成后执行其他恢复操作
				this.completeExplodedModeExit()

				this.emit("restore-animation-complete", {
					buildingId: this.explodedBuildingId,
					modelsCount: this.originalPositions.size,
					timestamp: Date.now(),
				})
			},
			onUpdate: () => {
				// 触发渲染更新
				this.emit("render-update-required", {
					reason: "gsap-restore-animation",
					timestamp: Date.now(),
				})
			},
		})

		let animationCount = 0

		this.originalPositions.forEach((originalPos, modelId) => {
			const model = this.models.get(modelId)
			if (model?.object) {
				const targetY = originalPos.y

				console.log(
					`🔄 GSAP准备恢复动画: ${modelId}, 当前Y: ${model.object.position.y}, 目标Y: ${targetY}`
				)

				// 添加到时间轴，所有楼层同时恢复
				this.explodedTimeline!.to(
					model.object.position,
					{
						y: targetY,
						duration: this.animationDuration,
						ease: "power2.inOut", // easeInOutQuad
					},
					0 // 所有动画同时开始
				)

				animationCount++
			}
		})

		if (animationCount === 0) {
			console.log(`📍 没有需要恢复的模型位置`)
			return
		}

		console.log(
			`🎬 启动GSAP恢复动画，模型数量: ${animationCount}，持续时间: ${this.animationDuration}秒`
		)
	}

	/**
	 * 完成拆楼模式退出（在恢复动画完成后调用）
	 */
	private completeExplodedModeExit(): void {
		console.log(`🔄 完成拆楼模式退出流程`)

		// 显示Mapbox底图
		this.showMapboxBase()

		// 显示水体
		this.showWater()

		// 恢复正常LOD显示
		this.restoreNormalDisplay()

		// 禁用拆楼模式相机控制
		this.disableExplodedCameraControl()

		// 重新启用LOD功能
		this.setEnabled(true)
		console.log(`🔓 拆楼模式退出：LOD功能已重新启用`)

		// 清空拆楼模式状态
		this.explodedMode = false
		this.explodedBuildingId = null
		this.originalPositions.clear()

		console.log(`✅ 拆楼模式完全退出`)
	}

	/**
	 * 隐藏Mapbox底图
	 */
	private hideMapboxBase(): void {
		// 通过事件通知隐藏底图
		this.emit("mapbox-visibility-change", {
			visible: false,
			reason: "exploded-mode",
			timestamp: Date.now(),
		})
	}

	/**
	 * 显示Mapbox底图
	 */
	private showMapboxBase(): void {
		// 通过事件通知显示底图
		this.emit("mapbox-visibility-change", {
			visible: true,
			reason: "exploded-mode-exit",
			timestamp: Date.now(),
		})
	}

	/**
	 * 隐藏水体
	 */
	private hideWater(): void {
		// 通过事件通知隐藏水体
		this.emit("water-visibility-change", {
			visible: false,
			reason: "exploded-mode",
			timestamp: Date.now(),
		})
		console.log("🌊 拆楼模式：隐藏水体")
	}

	/**
	 * 显示水体
	 */
	private showWater(): void {
		// 通过事件通知显示水体
		this.emit("water-visibility-change", {
			visible: true,
			reason: "exploded-mode-exit",
			timestamp: Date.now(),
		})
		console.log("🌊 拆楼模式退出：显示水体")
	}

	/**
	 * 恢复正常显示
	 */
	private restoreNormalDisplay(): void {
		// 恢复所有模型的正常显示状态
		this.models.forEach((model) => {
			if (model.buildingId === this.explodedBuildingId) {
				// 恢复当前建筑的正常LOD显示
				this.updateBuildingDisplay(model.buildingId!)
			} else {
				// 显示其他模型
				this.setModelVisible(model.id, true)
			}
		})
	}

	/**
	 * 强制进入指定建筑的LOD模式
	 */
	forceLODBuilding(buildingId: string): boolean {
		const exteriorModel = Array.from(this.models.values()).find(
			(model) => model.buildingId === buildingId && model.isExterior
		)

		if (!exteriorModel) {
			console.warn(`⚠️ 找不到建筑 ${buildingId} 的外立面模型`)
			return false
		}

		const hasInterior = this.hasBuildingInterior(buildingId)
		if (!hasInterior) {
			console.warn(`⚠️ 建筑 ${buildingId} 没有室内模型`)
			return false
		}

		this.switchToLODBuilding(buildingId, exteriorModel.name)
		console.log(`🏢 强制进入LOD模式: ${buildingId}`)
		return true
	}

	/**
	 * 调试信息
	 */
	debug(): void {
		const stats = this.getPerformanceStats()
		console.log("🔍 建筑LOD调试信息:")
		console.log(`  总模型数: ${stats.totalModels}`)
		console.log(`  可见模型: ${stats.visibleModels}`)
		console.log(`  隐藏模型: ${stats.levelCounts.hidden}`)
		console.log(`  系统状态: ${this.enabled ? "启用" : "禁用"}`)
		console.log(`  包围盒显示: ${this.showBoundingBoxes ? "启用" : "禁用"}`)

		console.log("🏢 建筑信息:")
		const buildingIds = this.getAllBuildingIds()
		buildingIds.forEach((buildingId) => {
			const floors = this.getBuildingFloors(buildingId)
			const currentFloor = this.getCurrentFloor(buildingId)
			const hasInterior = this.hasBuildingInterior(buildingId)
			console.log(
				`  - ${buildingId}: 楼层${floors.join(
					","
				)} 当前${currentFloor} ${hasInterior ? "有室内" : "无室内"}`
			)
		})

		console.log("📋 模型列表:")
		this.models.forEach((model) => {
			const type = model.isExterior
				? "外立面"
				: model.isInterior
				? `室内${model.floor}F`
				: "其他"
			console.log(
				`  - ${model.id}: ${
					model.isVisible ? "可见" : "隐藏"
				} (${type}, 距离: ${model.distanceToCamera.toFixed(1)}m)`
			)
		})

		console.log("📦 包围盒信息:")
		console.log(`  包围盒数量: ${this.boundingBoxHelpers.size}`)
		this.boundingBoxHelpers.forEach((boxHelper, modelId) => {
			const model = this.models.get(modelId)
			console.log(
				`  - ${modelId}: ${model?.name || "未知"} (${boxHelper.name})`
			)
		})
	}

	/**
	 * 获取包围盒显示状态
	 */
	isBoundingBoxesVisible(): boolean {
		return this.showBoundingBoxes
	}
}
