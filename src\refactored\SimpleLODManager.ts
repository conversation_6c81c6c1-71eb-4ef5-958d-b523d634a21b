import * as THREE from "three"
import { LODLevel } from "./LODTypes"

/**
 * 简化的LOD模型数据
 */
export interface SimpleLODModelData {
	id: string
	name: string
	position: THREE.Vector3
	object: THREE.Object3D
	currentLevel: LODLevel
	isVisible: boolean
	distanceToCamera: number
}

/**
 * 极简LOD管理器 - 只负责基础的显隐控制
 */
export class SimpleLODManager {
	private scene: THREE.Scene
	private models: Map<string, SimpleLODModelData> = new Map()
	private enabled: boolean = true
	private layerManager?: any

	constructor(scene: THREE.Scene) {
		this.scene = scene
	}

	/**
	 * 设置LayerManager引用
	 */
	setLayerManager(layerManager: any): void {
		this.layerManager = layerManager
		console.log("✅ SimpleLOD已连接LayerManager")
	}

	/**
	 * 启用/禁用LOD系统
	 */
	setEnabled(enabled: boolean): void {
		this.enabled = enabled
		if (enabled) {
			console.log("🔛 LOD系统已启用")
		} else {
			console.log("🔴 LOD系统已禁用 - 显示所有模型")
			// 禁用时显示所有模型
			this.models.forEach((model) => {
				this.setModelVisible(model.id, true)
			})
		}
	}

	isEnabled(): boolean {
		return this.enabled
	}

	/**
	 * 添加模型
	 */
	addModel(modelData: {
		id: string
		name: string
		position: THREE.Vector3
		object: THREE.Object3D
	}): string {
		const model: SimpleLODModelData = {
			id: modelData.id,
			name: modelData.name,
			position: modelData.position.clone(),
			object: modelData.object,
			currentLevel: LODLevel.HIGH,
			isVisible: true,
			distanceToCamera: 0,
		}

		this.models.set(modelData.id, model)
		console.log(`✅ 添加模型: ${modelData.id}`)
		return modelData.id
	}

	/**
	 * 移除模型
	 */
	removeModel(id: string): void {
		const model = this.models.get(id)
		if (!model) return

		// 从场景中移除
		if (model.object && model.object.parent) {
			model.object.parent.remove(model.object)
		}

		this.models.delete(id)
		console.log(`🗑️ 移除模型: ${id}`)
	}

	/**
	 * 设置模型可见性
	 */
	setModelVisible(id: string, visible: boolean): void {
		const model = this.models.get(id)
		if (!model) {
			console.warn(`⚠️ 模型不存在: ${id}`)
			return
		}

		// 更新状态
		model.isVisible = visible
		model.currentLevel = visible ? LODLevel.HIGH : LODLevel.HIDDEN

		// 通过LayerManager控制（如果可用）
		if (this.layerManager && typeof this.layerManager.setNodeVisibility === "function") {
			try {
				this.layerManager.setNodeVisibility(id, visible)
				console.log(`📡 LayerManager设置: ${id} → ${visible}`)
			} catch (error) {
				console.warn(`⚠️ LayerManager设置失败: ${id}`, error)
				// 降级到直接控制
				this.setObjectVisible(model.object, visible)
			}
		} else {
			// 直接控制3D对象
			this.setObjectVisible(model.object, visible)
		}

		console.log(`${visible ? "👁️ 显示" : "🙈 隐藏"}模型: ${id}`)
	}

	/**
	 * 直接设置3D对象可见性
	 */
	private setObjectVisible(object: THREE.Object3D, visible: boolean): void {
		object.visible = visible
		// 递归设置所有子对象
		object.traverse((child) => {
			child.visible = visible
		})
	}

	/**
	 * 获取模型可见性
	 */
	isModelVisible(id: string): boolean {
		const model = this.models.get(id)
		return model ? model.isVisible : false
	}

	/**
	 * 显示模型
	 */
	showModel(id: string): void {
		this.setModelVisible(id, true)
	}

	/**
	 * 隐藏模型
	 */
	hideModel(id: string): void {
		this.setModelVisible(id, false)
	}

	/**
	 * 切换模型可见性
	 */
	toggleModel(id: string): void {
		const model = this.models.get(id)
		if (model) {
			this.setModelVisible(id, !model.isVisible)
		}
	}

	/**
	 * 显示所有模型
	 */
	showAllModels(): void {
		console.log("👁️ 显示所有模型")
		this.models.forEach((model) => {
			this.setModelVisible(model.id, true)
		})
	}

	/**
	 * 隐藏所有模型
	 */
	hideAllModels(): void {
		console.log("🙈 隐藏所有模型")
		this.models.forEach((model) => {
			this.setModelVisible(model.id, false)
		})
	}

	/**
	 * 基于距离的简单LOD更新
	 */
	updateLOD(camera: THREE.Camera, maxDistance: number = 100): void {
		if (!this.enabled) return

		const cameraPosition = camera.position

		this.models.forEach((model) => {
			// 计算距离
			const distance = model.position.distanceTo(cameraPosition)
			model.distanceToCamera = distance

			// 简单的距离判断
			const shouldBeVisible = distance <= maxDistance
			
			if (model.isVisible !== shouldBeVisible) {
				this.setModelVisible(model.id, shouldBeVisible)
			}
		})
	}

	/**
	 * 获取模型信息
	 */
	getModel(id: string): SimpleLODModelData | undefined {
		return this.models.get(id)
	}

	/**
	 * 获取所有模型
	 */
	getAllModels(): SimpleLODModelData[] {
		return Array.from(this.models.values())
	}

	/**
	 * 获取可见模型数量
	 */
	getVisibleModelCount(): number {
		return Array.from(this.models.values()).filter(m => m.isVisible).length
	}

	/**
	 * 获取总模型数量
	 */
	getTotalModelCount(): number {
		return this.models.size
	}

	/**
	 * 获取简单统计信息
	 */
	getStats(): {
		total: number
		visible: number
		hidden: number
		enabled: boolean
	} {
		const visible = this.getVisibleModelCount()
		const total = this.getTotalModelCount()
		
		return {
			total,
			visible,
			hidden: total - visible,
			enabled: this.enabled,
		}
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		console.log("🧹 清理SimpleLOD资源...")
		
		// 移除所有模型
		this.models.forEach((model) => {
			if (model.object && model.object.parent) {
				model.object.parent.remove(model.object)
			}
		})
		
		this.models.clear()
		console.log("✅ SimpleLOD清理完成")
	}

	/**
	 * 调试信息
	 */
	debug(): void {
		const stats = this.getStats()
		console.log("🔍 SimpleLOD调试信息:")
		console.log(`  总模型数: ${stats.total}`)
		console.log(`  可见模型: ${stats.visible}`)
		console.log(`  隐藏模型: ${stats.hidden}`)
		console.log(`  系统状态: ${stats.enabled ? "启用" : "禁用"}`)
		
		console.log("📋 模型列表:")
		this.models.forEach((model) => {
			console.log(`  - ${model.id}: ${model.isVisible ? "可见" : "隐藏"} (距离: ${model.distanceToCamera.toFixed(1)}m)`)
		})
	}
}
