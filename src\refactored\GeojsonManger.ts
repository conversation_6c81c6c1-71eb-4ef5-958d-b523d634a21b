import { eventBus } from "./EventBus";

const urlMap = {
  //   临时展厅: "/icon/临时展厅.png",
  临展厅: "/icon/临时展厅.png",
  书画展厅: "/icon/书画展厅.png",
  云梦睡虎地秦简: "/icon/云梦睡虎地秦简.png",
  元青花四爱图梅瓶: "/icon/元青花四爱图梅瓶.png",
  充电宝: "/icon/充电宝.png",
  入口: "/icon/入口.png",
  出口: "/icon/出口.png",
  卫生间: "/icon/卫生间.png",
  咖啡馆: "/icon/咖啡馆.png",
  售票处: "/icon/售票处.png",
  //   "天籁—湖北出土的早期乐器": "/icon/天籁—湖北出土的早期乐器.png",
  天籁厅: "/icon/天籁—湖北出土的早期乐器.png",
  存储柜: "/icon/存储柜.png",
  寄存柜: "/icon/寄存柜.png",
  少儿体验馆: "/icon/少儿体验馆.png",
  就餐区: "/icon/就餐区.png",
  崇阳铜鼓: "/icon/崇阳铜鼓.png",
  "巧夺天工-湖北工艺美术作品展": "/icon/巧夺天工-湖北工艺美术作品展.png",
  常设展厅: "/icon/常设展厅.png",
  车马出行图: "/icon/彩绘人物 车马出行图.png",
  扶梯: "/icon/扶梯.png",
  文创店: "/icon/文创店.png",
  文创机器: "/icon/文创机器.png",
  文创雪糕: "/icon/文创雪糕.png",
  显示屏: "/icon/显示屏.png",
  //   "曾世家-考古揭秘的曾国": "/icon/曾世家-考古揭秘的曾国.png",
  曾世家展厅: "/icon/曾世家-考古揭秘的曾国.png",
  曾侯乙尊盘: "/icon/曾侯乙尊盘.png",
  曾侯乙展厅: "/icon/曾侯乙展厅.png",
  //   "曾侯乙编钟-人类共同的音乐记忆": "/icon/曾侯乙编钟-人类共同的音乐记忆.png",
  编钟演奏厅: "/icon/曾侯乙编钟-人类共同的音乐记忆.png",
  曾侯乙编钟: "/icon/曾侯乙编钟.png",
  "极目楚天-湖北古代文明展": "/icon/极目楚天- 湖北古代文明展.png",
  "极目楚天-湖北现当代英杰展厅": "/icon/极目楚天- 湖北现当代英杰展厅.png",
  湖北近代风云展: "/icon/极目楚天- 湖北近代风云展.png",
  极目楚天展厅: "/icon/极目楚天- 湖北古代文明展.png",
  梁庄王展厅: "/icon/梁庄王珍藏.png",
  楚国八百年展厅: "/icon/楚国八百年展厅.png",
  楼梯: "/icon/楼梯.png",
  永远的三峡: "/icon/永远的三峡.png",
  电梯: "/icon/电梯.png",
  石家河玉人像: "/icon/石家河玉人像.png",
  研学中心: "/icon/研学中心.png",
  "VR展厅（穿越青铜纪）": "/icon/穿越青铜纪：数字文物VR体验.png",
  编钟电子购票机: "/icon/编钟电子购票机.png",
  馆藏历代陶瓷展厅: "/icon/荆楚陶雅·瓷韵江夏-馆藏历代陶瓷展厅.png",
  虎座鸟架鼓: "/icon/虎座鸟架鼓.png",
  讲解器: "/icon/讲解器.png",
  走廊: "/icon/走廊.png",
  越王勾践剑: "/icon/越王勾践剑.png",
  越王勾践厅: "/icon/越王勾践剑特展.png",
  遇见楚庄王表演厅: "/icon/遇见·楚庄王表演厅.png",
  郧县人头骨化石: "/icon/郧县人头骨化石.png",
  随意拍: "/icon/随意拍.png",
  饮料柜: "/icon/饮料柜.png",
  饮水机: "/icon/饮水机.png",
};

export class GeojsonManager {
  private rawData: any;
  private url: string;
  private map: mapboxgl.Map;
  private resourceId: string = "markers";

  constructor(rawDataUrl: string, map: mapboxgl.Map) {
    this.url = rawDataUrl;
    this.map = map;
    this.map.once("load", async () => {
      this.loadImage();
      await this.initData();
      this.addLayer();
      this.filterMaker("", -1);
    });
    eventBus.on("lod-building-entered", ({ buildingId, floor }) => {
      this.filterMaker(buildingId, floor);
    });
    eventBus.on("floor-changed", ({ buildingId, floor }) => {
      this.filterMaker(buildingId, floor);
    });
    eventBus.on("exitLod", () => {
      this.filterMaker("", -1);
    });
  }
  async initData() {
    const res = await fetch(this.url);
    const json = await res.json();

    this.rawData = json;
  }
  async loadImage() {
    for (let name in urlMap) {
      const url = urlMap[name as keyof typeof urlMap];
      // console.log(url);

      this.map.loadImage(`${window.iconUrl}${url}`, (error, image) => {
        if (!this.map.hasImage(name)) {
          this.map.addImage(name, image, {
            pixelRatio: 2,
          });
        }
      });
    }
  }
  async addLayer() {
    this.map.addSource(this.resourceId, {
      type: "geojson",
      data: this.rawData,
    });
    this.map.addLayer({
      id: this.resourceId,
      type: "symbol",
      source: "markers",
      layout: {
        "text-field": ["get", "label"],
        "text-size": 14,
        "text-offset": [0, -0],
        "text-anchor": "left", // 文字锚点靠左
        "icon-image": ["get", "icon"],
        "icon-size": 1,
        "icon-anchor": "right", // 图标锚点靠右，紧贴文字左侧
        "icon-offset": [0, -0],
      },
      paint: {
        "text-color": "#FFFFFF", // 文字颜色：白色
        "text-halo-color": "#000000", // 描边颜色：黑色
        "text-halo-width": 1, // 描边厚度
        "text-halo-blur": 1, // 描边柔化程度,
        "icon-translate": [0, 40],
        "text-translate": [0, 40],
      },
    });
  }
  async filterMaker(buildingId: string, floor: number) {
    console.log(buildingId, floor);

    this.map.setFilter("markers", [
      "all",
      ["==", ["get", "building_id"], buildingId],
      ["==", ["get", "floor"], floor.toString()],
    ]);
  }
}
