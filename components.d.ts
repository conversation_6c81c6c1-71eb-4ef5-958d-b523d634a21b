/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChatDialog: typeof import('./src/components/chatDialog.vue')['default']
    Compass: typeof import('./src/components/Compass/index.vue')['default']
    Debug: typeof import('./src/components/Debug.vue')['default']
    FloorPanel: typeof import('./src/components/FloorPanel/index.vue')['default']
    LoadingOverlay: typeof import('./src/components/LoadingOverlay.vue')['default']
    Locate: typeof import('./src/components/locate/index.vue')['default']
    Passenger: typeof import('./src/components/passenger/index.vue')['default']
    PassengerFlow: typeof import('./src/components/PassengerFlow/index.vue')['default']
    VanImage: typeof import('vant/es')['Image']
  }
}
