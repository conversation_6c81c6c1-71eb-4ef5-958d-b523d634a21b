/**
 * 运动分析器 - Web版本
 * 集成步态检测和航向估计功能，适配Web浏览器环境
 * 移植自微信小程序版本，移除微信特定依赖
 */



export default class MotionAnalyzer {
  constructor(config = {}) {
    this.config = {
      // 步态检测配置 - 降低阈值提高敏感度
      stepDetection: {
        enabled: config.enableStepDetection !== false,
        accPeakMagnitudeLB: 1.2,        // 降低检测阈值，提高敏感度
        accPeakPeriodLB: 200,           // 减小最小步态间隔
        slideWindowLen: 8,              // 减小滑动窗口，更快响应
        sampleFreq: 50,
        adaptiveThreshold: true,
        confirmationRequired: true,      // 保持确认机制
        minConfirmationCount: 1,        // 降低最小确认次数
        maxStepLength: 1.2,             // 最大步长限制
        minStepLength: 0.3,             // 最小步长限制
        ...config.stepDetection
      },
      
      // 航向估计配置 - 优化平滑参数
      headingEstimation: {
        enabled: config.enableHeadingCorrection !== false,
        useCompass: true,
        useGyroscope: true,
        compassWeight: 0.6,             // 平衡罗盘权重
        gyroWeight: 0.4,                // 增加陀螺仪权重
        headingSmoothing: 0.4,          // 减少平滑，提高响应性
        fastResponseMode: true,         // 启用快速响应
        headingLockThreshold: 0.05,     // 增大锁定阈值，减少频繁切换
        stationaryHeadingLock: true,    // 静止时锁定航向
        movingHeadingSmoothing: 0.2,    // 新增：移动时的平滑系数
        stationaryHeadingSmoothing: 0.8, // 新增：静止时的平滑系数
        ...config.headingEstimation
      },
      
      // 步长估计配置
      stepLength: {
        defaultLength: config.calibration?.stepLength || 0.75,
        adaptiveEnabled: true,
        kValue: 0.42,                   // 降低Weinberg模型系数
        personalFactor: 0.85,           // 降低个人化因子
        maxLength: 1.2,                 // 新增：最大步长限制
        minLength: 0.3,                 // 新增：最小步长限制
        consistencyWeight: 0.4,         // 新增：一致性权重
        ...config.stepLength
      }
    };
    
    // 步态检测器
    this.stepDetector = new StepDetector(this.config.stepDetection);

    // 航向估计器 - 使用简化版本
    this.headingEstimator = new HeadingEstimator(this.config.headingEstimation);


    
    // 当前状态
    this.currentState = {
      // 步态相关
      stepCount: 0,
      stepLength: this.config.stepLength.defaultLength,
      stepFrequency: 0,
      walkingState: 'stationary', // stationary, walking, running
      
      // 航向相关
      heading: 0,
      headingConfidence: 0,
      headingQuality: 'unknown',
      
      // 速度相关
      velocity: 0,
      acceleration: 0,
      
      // 时间相关
      lastStepTime: 0,
      lastUpdateTime: 0
    };
    
    // 历史数据
    this.history = {
      steps: [],
      headings: [],
      velocities: []
    };
    
    // 性能统计
    this.statistics = {
      totalSteps: 0,
      averageStepLength: this.config.stepLength.defaultLength,
      averageStepFrequency: 0,
      headingAccuracy: 0,
      processingTime: []
    };
    
    // 回调函数
    this.callbacks = {
      onStepDetected: null,
      onHeadingUpdate: null,
      onStateChange: null
    };
    
    console.log('🚶 Web运动分析器初始化完成');
  }
  
  /**
   * 处理传感器数据
   * @param {Object} sensorData - 传感器数据
   * @returns {Object} 运动分析结果
   */
  analyze(sensorData) {
    const startTime = Date.now();
    
    try {
      const result = {
        stepDetected: false,
        stepData: null,
        headingUpdated: false,
        headingData: null,
        stateChanged: false,
        timestamp: startTime
      };
      
      // 步态检测
      if (this.config.stepDetection.enabled && sensorData.accelerometer) {
        const stepResult = this.stepDetector.process(sensorData.accelerometer, sensorData.timestamp);
        if (stepResult) {
          result.stepDetected = true;
          result.stepData = this.processStepDetection(stepResult);
        }
      }
       console.log('开始航向估计',this.config.headingEstimation.enabled);
      // 航向估计 - 直接使用原始航向估计器（传感器数据已优化）
      if (this.config.headingEstimation.enabled) {
        const headingResult = this.headingEstimator.update(
          sensorData.gyroscope,
          sensorData.magnetometer,
          sensorData.timestamp
        );
        console.log('航向估计器结果', headingResult);
        if (headingResult) {
          result.headingUpdated = true;
          result.headingData = this.processHeadingUpdate(headingResult);
        }
      }
      
      // 更新运动状态
      const stateChange = this.updateMotionState(sensorData);
      if (stateChange) {
        result.stateChanged = true;
      }
      
      // 更新时间戳
      this.currentState.lastUpdateTime = startTime;
      
      // 记录处理时间
      const processingTime = Date.now() - startTime;
      this.statistics.processingTime.push(processingTime);
      if (this.statistics.processingTime.length > 100) {
        this.statistics.processingTime.shift();
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ 运动分析失败:', error);
      return null;
    }
  }
  
  /**
   * 处理步态检测结果
   */
  processStepDetection(stepResult) {
    // 更新步数
    this.currentState.stepCount++;
    this.statistics.totalSteps++;
    
    // 计算步长
    const stepLength = this.calculateStepLength(stepResult);
    this.currentState.stepLength = stepLength;
    
    // 计算步频
    const stepFrequency = this.calculateStepFrequency(stepResult);
    this.currentState.stepFrequency = stepFrequency;
    
    // 更新步态时间
    this.currentState.lastStepTime = stepResult.timestamp;
    
    // 记录步态历史
    const stepRecord = {
      timestamp: stepResult.timestamp,
      stepCount: this.currentState.stepCount,
      stepLength: stepLength,
      magnitude: stepResult.magnitude,
      confidence: stepResult.confidence || 1.0
    };
    
    this.history.steps.push(stepRecord);
    if (this.history.steps.length > 100) {
      this.history.steps.shift();
    }
    
    // 更新统计信息
    this.updateStepStatistics(stepRecord);
    
    // 触发回调
    if (this.callbacks.onStepDetected) {
      this.callbacks.onStepDetected(stepRecord);
    }
    
    return stepRecord;
  }
  
  /**
   * 处理航向更新
   */
  processHeadingUpdate(headingResult) {
    // 更新航向角
    this.currentState.heading = headingResult.heading;
    this.currentState.headingConfidence = headingResult.confidence || 0;
    this.currentState.headingQuality = headingResult.quality || 'unknown';
    
    // 记录航向历史
    const headingRecord = {
      timestamp: headingResult.timestamp,
      heading: headingResult.heading,
      confidence: headingResult.confidence,
      quality: headingResult.quality,
      source: headingResult.source // 'gyro', 'compass', 'fused'
    };
    
    this.history.headings.push(headingRecord);
    if (this.history.headings.length > 200) {
      this.history.headings.shift();
    }
    
    // 更新统计信息
    this.updateHeadingStatistics(headingRecord);
    
    // 触发回调
    if (this.callbacks.onHeadingUpdate) {
      this.callbacks.onHeadingUpdate(headingRecord);
    }
    
    return headingRecord;
  }



  /**
   * 更新运动状态
   */
  updateMotionState(sensorData) {
    const currentTime = Date.now();
    const previousState = this.currentState.walkingState;
    
    // 基于步频判断运动状态
    const timeSinceLastStep = currentTime - this.currentState.lastStepTime;
    const recentSteps = this.history.steps.filter(step => 
      currentTime - step.timestamp < 3000 // 最近3秒
    );
    
    let newState = 'stationary';
    
    if (recentSteps.length >= 2) {
      const avgStepInterval = this.calculateAverageStepInterval(recentSteps);
      
      if (avgStepInterval < 400) { // 步频 > 2.5步/秒
        newState = 'running';
      } else if (avgStepInterval < 800) { // 步频 > 1.25步/秒
        newState = 'walking';
      } else if (timeSinceLastStep < 2000) { // 2秒内有步态
        newState = 'walking';
      }
    }
    
    // 使用加速度幅值辅助判断
    if (sensorData.accelerometer) {
      const accMagnitude = Math.sqrt(
        sensorData.accelerometer.x ** 2 + 
        sensorData.accelerometer.y ** 2 + 
        sensorData.accelerometer.z ** 2
      );
      
      // 如果加速度变化很小，更倾向于静止状态
      if (accMagnitude < 1.2 && newState !== 'stationary') {
        newState = 'walking'; // 轻微运动
      }
    }
    
    this.currentState.walkingState = newState;
    
    // 计算速度
    this.updateVelocity();
    
    // 检查状态是否发生变化
    const stateChanged = previousState !== newState;
    if (stateChanged && this.callbacks.onStateChange) {
      this.callbacks.onStateChange({
        previousState,
        currentState: newState,
        timestamp: currentTime,
        stepFrequency: this.currentState.stepFrequency,
        velocity: this.currentState.velocity
      });
    }
    
    return stateChanged;
  }
  
  /**
   * 计算步长 (改进的Weinberg模型)
   */
  calculateStepLength(stepResult) {
    const magnitude = stepResult.magnitude || 1.0;
    const k = this.config.stepLength.kValue;
    const personalFactor = this.config.stepLength.personalFactor;
    
    // 改进的Weinberg模型，增加死区和非线性校正
    let rawMagnitude = magnitude;
    
    // 应用死区滤波，减少小幅度噪声的影响
    const deadZone = 0.2;
    const threshold = this.stepDetector.dynamicThreshold?.current || this.config.stepDetection.accPeakMagnitudeLB;
    if (rawMagnitude < threshold + deadZone) {
      rawMagnitude = Math.max(0.1, rawMagnitude - deadZone);
    }
    
    // 改进的Weinberg公式: SL = K × (amax - amin - deadzone)^α × personalFactor
    // 使用α=0.3而不是0.25，对小幅度变化更不敏感
    const alpha = 0.3;
    let estimatedLength = k * Math.pow(rawMagnitude, alpha) * personalFactor;
    
    // 基于运动状态的步长调整
    const stateModifier = this.getStepLengthModifier();
    estimatedLength *= stateModifier;
    
    // 基于置信度的调整
    const confidence = stepResult.confidence || 0.5;
    if (confidence < 0.7) {
      // 低置信度时，倾向于使用更保守的步长
      estimatedLength *= 0.85;
    }
    
    // 应用自适应调整
    if (this.config.stepLength.adaptiveEnabled && this.history.steps.length > 8) {
      const recentSteps = this.history.steps.slice(-8);
      const validSteps = recentSteps.filter(s => s.confidence > 0.6);
      
      if (validSteps.length >= 3) {
        const recentLengths = validSteps.map(s => s.stepLength);
        const avgLength = recentLengths.reduce((a, b) => a + b, 0) / recentLengths.length;
        const stdDev = Math.sqrt(recentLengths.reduce((sum, len) => 
          sum + Math.pow(len - avgLength, 2), 0) / recentLengths.length);
        
        // 如果变化不大，加强平滑；如果变化较大，保持敏感性
        const smoothingFactor = stdDev < 0.15 ? 0.6 : 0.3;
        estimatedLength = (1 - smoothingFactor) * estimatedLength + smoothingFactor * avgLength;
      }
    }
    
    // 动态范围限制，基于个人因子调整
    const minLength = 0.25 * personalFactor;
    const maxLength = 1.2 * personalFactor;
    estimatedLength = Math.max(minLength, Math.min(maxLength, estimatedLength));
    
    // 连续步态的稳定性检查
    if (this.history.steps.length > 0) {
      const lastStepLength = this.history.steps[this.history.steps.length - 1].stepLength;
      const lengthChange = Math.abs(estimatedLength - lastStepLength) / lastStepLength;
      
      // 如果步长变化过大（超过50%），进行修正
      if (lengthChange > 0.5 && this.stepState.consecutiveSteps > 2) {
        const maxChange = lastStepLength * 0.3; // 最大允许变化30%
        if (estimatedLength > lastStepLength) {
          estimatedLength = Math.min(estimatedLength, lastStepLength + maxChange);
        } else {
          estimatedLength = Math.max(estimatedLength, lastStepLength - maxChange);
        }
      }
    }
    
    return Number(estimatedLength.toFixed(3));
  }
  
  /**
   * 获取基于运动状态的步长修正因子
   */
  getStepLengthModifier() {
    switch (this.currentState.walkingState) {
      case 'stationary':
        return 0.5; // 静止状态的微小移动
      case 'walking':
        return 1.0; // 正常步行
      case 'running':
        return 1.3; // 跑步时步长增加
      default:
        return 1.0;
    }
  }
  
  /**
   * 计算步频
   */
  calculateStepFrequency(stepResult) {
    if (this.history.steps.length < 2) {
      return 0;
    }
    
    const recentSteps = this.history.steps.slice(-5); // 最近5步
    if (recentSteps.length < 2) {
      return 0;
    }
    
    // 计算平均步态间隔
    const intervals = [];
    for (let i = 1; i < recentSteps.length; i++) {
      intervals.push(recentSteps[i].timestamp - recentSteps[i-1].timestamp);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    return avgInterval > 0 ? 1000 / avgInterval : 0; // 步/秒
  }
  
  /**
   * 计算平均步态间隔
   */
  calculateAverageStepInterval(steps) {
    if (steps.length < 2) return Infinity;
    
    const intervals = [];
    for (let i = 1; i < steps.length; i++) {
      intervals.push(steps[i].timestamp - steps[i-1].timestamp);
    }
    
    return intervals.reduce((a, b) => a + b, 0) / intervals.length;
  }
  
  /**
   * 更新速度
   */
  updateVelocity() {
    const state = this.currentState.walkingState;
    
    if (state === 'stationary') {
      this.currentState.velocity = 0;
      this.currentState.acceleration = 0;
    } else {
      // 基于步频和步长计算速度
      const velocity = this.currentState.stepFrequency * this.currentState.stepLength;
      
      // 平滑处理
      const smoothing = 0.3;
      this.currentState.velocity = smoothing * velocity + (1 - smoothing) * this.currentState.velocity;
      
      // 计算加速度（简单差分）
      const previousVelocity = this.history.velocities.length > 0 ? 
        this.history.velocities[this.history.velocities.length - 1].velocity : 0;
      this.currentState.acceleration = this.currentState.velocity - previousVelocity;
    }
    
    // 记录速度历史
    this.history.velocities.push({
      timestamp: Date.now(),
      velocity: this.currentState.velocity,
      acceleration: this.currentState.acceleration,
      state: state
    });
    
    if (this.history.velocities.length > 50) {
      this.history.velocities.shift();
    }
  }
  
  /**
   * 更新步态统计
   */
  updateStepStatistics(stepRecord) {
    // 更新平均步长
    const recentSteps = this.history.steps.slice(-20); // 最近20步
    if (recentSteps.length > 0) {
      this.statistics.averageStepLength = recentSteps.reduce((sum, step) => 
        sum + step.stepLength, 0) / recentSteps.length;
    }
    
    // 更新平均步频
    if (this.currentState.stepFrequency > 0) {
      const alpha = 0.1;
      this.statistics.averageStepFrequency = alpha * this.currentState.stepFrequency + 
        (1 - alpha) * this.statistics.averageStepFrequency;
    }
  }
  
  /**
   * 更新航向统计
   */
  updateHeadingStatistics(headingRecord) {
    // 计算航向精度（基于置信度）
    const recentHeadings = this.history.headings.slice(-10);
    if (recentHeadings.length > 0) {
      this.statistics.headingAccuracy = recentHeadings.reduce((sum, h) => 
        sum + (h.confidence || 0), 0) / recentHeadings.length;
    }
  }
  
  /**
   * 获取当前运动状态
   */
  getCurrentState() {
    return {
      ...this.currentState,
      statistics: { ...this.statistics },
      isMoving: this.currentState.walkingState !== 'stationary',
      movementType: this.currentState.walkingState,
      confidence: Math.min(
        this.currentState.headingConfidence,
        this.currentState.stepFrequency > 0 ? 1.0 : 0.5
      )
    };
  }
  
  /**
   * 获取运动历史
   */
  getMotionHistory(type = 'all', limit = null) {
    let history = {};
    
    if (type === 'all' || type === 'steps') {
      history.steps = limit ? this.history.steps.slice(-limit) : [...this.history.steps];
    }
    
    if (type === 'all' || type === 'headings') {
      history.headings = limit ? this.history.headings.slice(-limit) : [...this.history.headings];
    }
    
    if (type === 'all' || type === 'velocities') {
      history.velocities = limit ? this.history.velocities.slice(-limit) : [...this.history.velocities];
    }
    
    return history;
  }
  
  /**
   * 校准运动参数
   */
  calibrate(calibrationData) {
    console.log('🔧 校准运动分析器...');
    
    try {
      // 步长校准
      if (calibrationData.stepLength) {
        this.config.stepLength.defaultLength = calibrationData.stepLength;
        this.currentState.stepLength = calibrationData.stepLength;
      }
      
      // 个人化因子校准
      if (calibrationData.personalFactor) {
        this.config.stepLength.personalFactor = calibrationData.personalFactor;
      }
      
      // 航向校准
      if (calibrationData.magneticDeclination !== undefined) {
        this.headingEstimator.setMagneticDeclination(calibrationData.magneticDeclination);
      }
      
      // 传感器校准
      if (calibrationData.sensorCalibration) {
        this.stepDetector.calibrate(calibrationData.sensorCalibration);
        this.headingEstimator.calibrate(calibrationData.sensorCalibration);
      }
      
      console.log('✅ 运动分析器校准完成');
      return { success: true };
      
    } catch (error) {
      console.error('❌ 运动分析器校准失败:', error);
      return { success: false, error: error.message };
    }
  }
  
  /**
   * 重置运动状态
   */
  reset() {
    // 重置状态
    this.currentState = {
      stepCount: 0,
      stepLength: this.config.stepLength.defaultLength,
      stepFrequency: 0,
      walkingState: 'stationary',
      heading: 0,
      headingConfidence: 0,
      headingQuality: 'unknown',
      velocity: 0,
      acceleration: 0,
      lastStepTime: 0,
      lastUpdateTime: 0
    };
    
    // 清空历史
    this.history.steps = [];
    this.history.headings = [];
    this.history.velocities = [];
    
    // 重置统计
    this.statistics.totalSteps = 0;
    this.statistics.averageStepLength = this.config.stepLength.defaultLength;
    this.statistics.averageStepFrequency = 0;
    this.statistics.headingAccuracy = 0;
    this.statistics.processingTime = [];
    
    // 重置子模块
    this.stepDetector.reset();
    this.headingEstimator.reset();
    
    console.log('🔄 运动分析器已重置');
  }
  
  /**
   * 设置回调函数
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }
  
  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    
    // 更新子模块配置
    if (config.stepDetection) {
      this.stepDetector.updateConfig(config.stepDetection);
    }
    
    if (config.headingEstimation) {
      this.headingEstimator.updateConfig(config.headingEstimation);
    }
    
    console.log('⚙️ 运动分析器配置已更新');
  }
  
  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const avgProcessingTime = this.statistics.processingTime.length > 0 ?
      this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length : 0;
    
    return {
      averageProcessingTime: avgProcessingTime,
      totalSteps: this.statistics.totalSteps,
      averageStepLength: this.statistics.averageStepLength,
      averageStepFrequency: this.statistics.averageStepFrequency,
      headingAccuracy: this.statistics.headingAccuracy,
      currentState: this.currentState.walkingState,
      dataPoints: {
        steps: this.history.steps.length,
        headings: this.history.headings.length,
        velocities: this.history.velocities.length
      }
    };
  }
}

/**
 * 步态检测器 - Web版本
 * 基于加速度峰值检测算法，增强版带确认机制
 */
class StepDetector {
  constructor(config) {
    this.config = config;
    this.slideWindow = [];
    this.stepState = {
      stepCount: 0,
      lastPeakTime: 0,
      threshold: config.accPeakMagnitudeLB,
      pendingStep: null,          // 待确认的步态
      confirmationWindow: [],     // 确认窗口
      consecutiveSteps: 0,        // 连续步态计数
      lastValidMagnitude: 0       // 最后一次有效峰值幅度
    };
    
    // 动态阈值参数
    this.dynamicThreshold = {
      base: config.accPeakMagnitudeLB,
      current: config.accPeakMagnitudeLB,
      adaptRate: 0.05,
      minThreshold: config.accPeakMagnitudeLB * 0.8,
      maxThreshold: config.accPeakMagnitudeLB * 2.0
    };
  }
  
  process(accData, timestamp) {
    // 计算加速度幅值
    const magnitude = Math.sqrt(accData.x ** 2 + accData.y ** 2 + accData.z ** 2);
    
    // 添加到滑动窗口
    this.slideWindow.push({ magnitude, timestamp });
    
    // 维持窗口大小
    if (this.slideWindow.length > this.config.slideWindowLen) {
      this.slideWindow.shift();
    }
    
    // 检查是否有足够数据
    if (this.slideWindow.length < this.config.slideWindowLen) {
      return null;
    }
    
    // 更新动态阈值
    this.updateDynamicThreshold(magnitude);
    
    // 增强峰值检测逻辑
    const stepResult = this.detectStepWithConfirmation(magnitude, timestamp);
    
    return stepResult;
  }
  
  /**
   * 带确认机制的步态检测（简化版）
   */
  detectStepWithConfirmation(magnitude, timestamp) {
    const current = { magnitude, timestamp };
    const windowSize = this.slideWindow.length;
    
    // 检查时间间隔限制
    const timeSinceLastStep = timestamp - this.stepState.lastPeakTime;
    if (timeSinceLastStep < this.config.accPeakPeriodLB) {
      return null;
    }
    
    // 多重检测条件
    const conditions = this.checkStepConditions(current, windowSize);
    
    // 需要满足所有条件才认为是有效步态
    if (conditions.isPeak && conditions.meetsThreshold && conditions.hasPattern) {
      
      // 额外的确认逻辑：检查最近几个数据点的趋势
      const confirmationScore = this.calculateConfirmationScore(current, windowSize);
      
      if (confirmationScore > 0.4) { // 降低确认阈值，提高敏感度
        this.stepState.stepCount++;
        this.stepState.lastPeakTime = timestamp;
        this.stepState.lastValidMagnitude = magnitude;
        this.stepState.consecutiveSteps++;

        // 调整动态阈值
        this.adaptThreshold(magnitude, true);

        const confidence = this.calculateConfidence(current, conditions);

        return {
          timestamp,
          magnitude,
          stepCount: this.stepState.stepCount,
          confidence,
          confirmed: true,
          confirmationScore
        };
      } else {
        // 疑似步态但确认度不够，轻微调整阈值
        this.adaptThreshold(magnitude, false);
        this.stepState.consecutiveSteps = Math.max(0, this.stepState.consecutiveSteps - 1);
      }
    }
    
    return null;
  }
  
  /**
   * 计算确认分数
   */
  calculateConfirmationScore(current, windowSize) {
    let score = 0.0;
    
    // 基础分数：基于幅度
    const magnitudeRatio = current.magnitude / this.dynamicThreshold.current;
    if (magnitudeRatio > 2.0) score += 0.4;
    else if (magnitudeRatio > 1.5) score += 0.3;
    else if (magnitudeRatio > 1.2) score += 0.2;
    else score += 0.1;
    
    // 峰值质量分数：检查峰值的明显程度
    if (windowSize >= 5) {
      const centerIndex = Math.floor(windowSize / 2);
      const center = this.slideWindow[centerIndex];
      const neighbors = this.slideWindow.slice(centerIndex - 2, centerIndex + 3);
      
      let peakQuality = 0;
      neighbors.forEach((neighbor, index) => {
        if (index !== 2 && current.magnitude > neighbor.magnitude * 1.1) {
          peakQuality += 0.05;
        }
      });
      score += Math.min(0.2, peakQuality);
    }
    
    // 连续性分数：连续步态给予加分
    if (this.stepState.consecutiveSteps > 0) {
      score += Math.min(0.2, this.stepState.consecutiveSteps * 0.05);
    }
    
    // 稳定性分数：避免抖动
    if (windowSize >= 3) {
      const recent = this.slideWindow.slice(-3);
      const variation = Math.max(...recent.map(d => d.magnitude)) - Math.min(...recent.map(d => d.magnitude));
      const avgMagnitude = recent.reduce((sum, d) => sum + d.magnitude, 0) / recent.length;
      
      if (variation / avgMagnitude < 0.5) { // 变化不太剧烈，较稳定
        score += 0.1;
      }
    }
    
    // 时间间隔分数：合理的步态间隔
    const timeSinceLastStep = current.timestamp - this.stepState.lastPeakTime;
    if (timeSinceLastStep >= this.config.accPeakPeriodLB && 
        timeSinceLastStep <= this.config.accPeakPeriodUB) {
      score += 0.1;
    }
    
    return Math.min(1.0, score);
  }
  
  /**
   * 检查步态检测的多重条件
   */
  checkStepConditions(current, windowSize) {
    const conditions = {
      isPeak: false,
      meetsThreshold: false,
      hasPattern: false,
      isStable: false
    };
    
    // 条件1: 峰值检测 - 检查前后数据点
    const checkRadius = Math.min(3, Math.floor(windowSize / 3));
    let isPeakPoint = true;
    
    for (let i = 1; i <= checkRadius && i < windowSize; i++) {
      const before = this.slideWindow[windowSize - 1 - i];
      const after = i < checkRadius ? this.slideWindow[windowSize - 1 + i] : null;
      
      if (before && current.magnitude <= before.magnitude) {
        isPeakPoint = false;
        break;
      }
      if (after && current.magnitude <= after.magnitude) {
        isPeakPoint = false;
        break;
      }
    }
    conditions.isPeak = isPeakPoint;
    
    // 条件2: 阈值检测 - 使用动态阈值
    conditions.meetsThreshold = current.magnitude > this.dynamicThreshold.current;
    
    // 条件3: 模式检测 - 检查是否符合步态模式
    if (windowSize >= 5) {
      const recentData = this.slideWindow.slice(-5);
      const avgMagnitude = recentData.reduce((sum, d) => sum + d.magnitude, 0) / recentData.length;
      const stdDev = Math.sqrt(recentData.reduce((sum, d) => sum + Math.pow(d.magnitude - avgMagnitude, 2), 0) / recentData.length);
      
      // 步态应该有明显的幅度变化
      conditions.hasPattern = stdDev > 0.3 && current.magnitude > avgMagnitude + stdDev * 0.5;
    }
    
    // 条件4: 稳定性检测 - 避免抖动
    if (windowSize >= 3) {
      const last3 = this.slideWindow.slice(-3);
      const trend = last3[2].magnitude - last3[0].magnitude;
      conditions.isStable = Math.abs(trend) < current.magnitude * 0.3;
    }
    
    return conditions;
  }
  
  
  /**
   * 计算步态置信度 - 降低基础阈值
   */
  calculateConfidence(current, conditions) {
    let confidence = 0.4; // 提高基础置信度

    // 基于幅度的置信度 - 降低要求
    if (current.magnitude > this.dynamicThreshold.current * 1.3) confidence += 0.3;
    else if (current.magnitude > this.dynamicThreshold.current * 1.1) confidence += 0.2;
    else confidence += 0.15; // 提高最低幅度的置信度

    // 基于条件满足情况的置信度
    if (conditions.isPeak) confidence += 0.15;
    if (conditions.hasPattern) confidence += 0.15;
    if (conditions.isStable) confidence += 0.1;

    // 连续步态的置信度加成 - 降低要求
    if (this.stepState.consecutiveSteps > 1) confidence += 0.1; // 从2步降低到1步

    return Math.min(1.0, confidence);
  }
  
  /**
   * 更新动态阈值
   */
  updateDynamicThreshold(magnitude) {
    const current = this.dynamicThreshold.current;
    const target = magnitude > current ? current * 1.05 : current * 0.98;
    
    this.dynamicThreshold.current = Math.max(
      this.dynamicThreshold.minThreshold,
      Math.min(this.dynamicThreshold.maxThreshold, 
        current + (target - current) * this.dynamicThreshold.adaptRate)
    );
  }
  
  /**
   * 适应性阈值调整
   */
  adaptThreshold(magnitude, wasValidStep) {
    if (wasValidStep) {
      // 如果是有效步态，略微降低阈值以便后续检测
      this.dynamicThreshold.current = Math.max(
        this.dynamicThreshold.minThreshold,
        this.dynamicThreshold.current * 0.95
      );
    } else {
      // 如果是误检，提高阈值
      this.dynamicThreshold.current = Math.min(
        this.dynamicThreshold.maxThreshold,
        this.dynamicThreshold.current * 1.05
      );
    }
  }
  
  calibrate(calibrationData) {
    if (calibrationData.stepThreshold) {
      this.stepState.threshold = calibrationData.stepThreshold;
    }
  }
  
  reset() {
    this.slideWindow = [];
    this.stepState.stepCount = 0;
    this.stepState.lastPeakTime = 0;
  }
  
  updateConfig(config) {
    this.config = { ...this.config, ...config };
  }
}

/**
 * 航向估计器 - Web增强版本
 * 融合陀螺仪和磁力计数据，包含航向锁定和智能平滑
 */
class HeadingEstimator {
  constructor(config) {
    this.config = config;
    this.state = {
      heading: 0,
      lastUpdate: 0,
      initialized: false,
      isLocked: false,          // 航向锁定状态
      lockStartTime: 0,         // 锁定开始时间
      lockThreshold: 2.0,       // 锁定阈值（度）
      lockDuration: 2000        // 锁定持续时间（毫秒）
    };
    
    this.magneticDeclination = 0;
    
    // 航向历史记录，用于稳定性分析
    this.headingHistory = [];
    this.maxHistoryLength = 20;
    
    // 动态平滑参数 - 优化参数范围
    this.smoothingParams = {
      base: config.headingSmoothing || 0.4,
      current: config.headingSmoothing || 0.4,
      adaptive: true,
      minSmoothing: 0.1,  // 降低最小平滑，提高响应性
      maxSmoothing: 0.7,  // 降低最大平滑，避免过度延迟
      movingSmoothing: config.movingHeadingSmoothing || 0.2,
      stationarySmoothing: config.stationaryHeadingSmoothing || 0.8
    };
    
    // 异常检测
    this.anomalyDetector = {
      enabled: true,
      threshold: 30.0,         // 异常变化阈值（度）
      consecutiveCount: 0,      // 连续异常计数
      maxConsecutive: 3        // 最大连续异常次数
    };
  }
  
  update(gyroData, compassData, timestamp) {
    const dt = this.state.lastUpdate > 0 ? (timestamp - this.state.lastUpdate) / 1000 : 0;

    let rawHeading = this.state.heading;
    let confidence = 0;
    let quality = 'unknown';
    let source = 'none';

    // 1. 获取罗盘数据（简化处理，因为传感器数据已经很准确）
    if (this.config.useCompass && compassData && compassData.direction !== undefined) {
      const compassHeading = compassData.direction;

      // 检查数据源类型，决定处理策略
      const isAbsoluteData = compassData.isAbsolute ||
                            compassData.dataSource === 'webkit_compass' ||
                            compassData.dataSource === 'deviceorientationabsolute';

      console.log('🧭 航向数据处理:', {
        接收到的航向: compassHeading.toFixed(2) + '°',
        数据源: compassData.dataSource || 'unknown',
        是否绝对方向: isAbsoluteData,
        精度: compassData.accuracy,
        完整数据: compassData
      });

      if (!this.state.initialized) {
        // 初始化：直接使用罗盘数据
        rawHeading = compassHeading;
        this.state.initialized = true;
        console.log('✅ 航向估计器初始化完成，初始航向:', rawHeading.toFixed(2) + '°');
      } else {
        // 已初始化：根据数据质量决定处理方式
        if (isAbsoluteData) {
          // 绝对方向数据：已在源头补偿，应该直接使用或使用极高权重
          // 检查数据变化幅度，决定是否直接使用
          const angleDiff = Math.abs(((compassHeading - this.state.heading + 540) % 360) - 180);

          if (angleDiff < 5) {
            // 小幅变化：直接使用新数据，避免融合算法的延迟
            rawHeading = compassHeading;
            console.log('🧭 绝对方向直接使用:', {
              旧航向: this.state.heading.toFixed(2) + '°',
              新航向: compassHeading.toFixed(2) + '°',
              角度差: angleDiff.toFixed(2) + '°',
              处理: '直接使用（小幅变化）'
            });
          } else {
            // 大幅变化：使用极高权重融合，避免突变
            const weight = 0.95; // 极高权重，保护补偿值
            rawHeading = this.blendAngles(this.state.heading, compassHeading, weight);
            console.log('🧭 绝对方向高权重融合:', {
              旧航向: this.state.heading.toFixed(2) + '°',
              新航向: compassHeading.toFixed(2) + '°',
              角度差: angleDiff.toFixed(2) + '°',
              融合权重: weight,
              结果: rawHeading.toFixed(2) + '°'
            });
          }
        } else {
          // 相对方向数据：检测异常后融合
          if (!this.isCompassDataAnomalous(compassHeading)) {
            const alpha = this.calculateCompassWeight(compassData);
            rawHeading = this.blendAngles(this.state.heading, compassHeading, alpha);
          } else {
            console.warn('⚠️ 检测到罗盘数据异常，跳过更新');
          }
        }
      }

      confidence = isAbsoluteData ? 0.9 : this.calculateCompassConfidence(compassData);
      quality = confidence > 0.8 ? 'good' : confidence > 0.5 ? 'fair' : 'poor';
      source = 'compass';
    }

    // 2. 使用陀螺仪数据进行预测（仅在没有罗盘数据时）
    if (this.config.useGyroscope && gyroData && dt > 0 && dt < 0.1 && source === 'none') {
      const gyroHeading = this.processGyroData(gyroData.z, dt);
      rawHeading = (rawHeading + gyroHeading + 360) % 360;

      source = 'gyro';
      confidence = 0.7;
      quality = 'fair';
    }

    // 3. 简化的平滑处理（仅对非绝对数据进行轻微平滑）
    let finalHeading = rawHeading;
    let appliedSmoothingFactor = 0;

    const isAbsoluteData = compassData?.isAbsolute ||
                          compassData?.dataSource === 'webkit_compass' ||
                          compassData?.dataSource === 'deviceorientationabsolute';

    if (this.state.initialized && !isAbsoluteData) {
      // 只对相对方向数据进行轻微平滑
      appliedSmoothingFactor = 0.2; // 轻微平滑
      finalHeading = this.blendAngles(this.state.heading, rawHeading, 1 - appliedSmoothingFactor);
    }

    // 4. 更新历史记录
    this.updateHeadingHistory(finalHeading, confidence, timestamp);

    // 5. 更新状态
    this.state.heading = finalHeading;
    this.state.lastUpdate = timestamp;
    this.state.isLocked = false; // 简化：不使用锁定机制

    return {
      heading: finalHeading,
      confidence: confidence,
      quality: confidence > 0.8 ? 'good' : confidence > 0.5 ? 'fair' : 'poor',
      source,
      timestamp,
      isLocked: this.state.isLocked,
      smoothingFactor: appliedSmoothingFactor
    };
  }
  
  /**
   * 检测罗盘数据异常
   */
  isCompassDataAnomalous(compassHeading) {
    if (this.headingHistory.length < 3) return false;
    
    const recentHeadings = this.headingHistory.slice(-3).map(h => h.heading);
    const avgRecent = this.calculateCircularMean(recentHeadings);
    const headingDiff = this.getAngularDifference(compassHeading, avgRecent);
    
    if (Math.abs(headingDiff) > this.anomalyDetector.threshold) {
      this.anomalyDetector.consecutiveCount++;
      
      // 连续异常超过阈值时，暂时忽略罗盘数据
      if (this.anomalyDetector.consecutiveCount >= this.anomalyDetector.maxConsecutive) {
        return true;
      }
    } else {
      this.anomalyDetector.consecutiveCount = Math.max(0, this.anomalyDetector.consecutiveCount - 1);
    }
    
    return false;
  }
  
  /**
   * 计算罗盘权重
   */
  calculateCompassWeight(compassData) {
    let weight = this.config.compassWeight || 0.3;
    
    // 基于精度调整权重
    const accuracy = compassData.accuracy || 3;
    const accuracyFactor = Math.max(0.3, Math.min(1.0, accuracy / 3));
    weight *= accuracyFactor;
    
    // 基于稳定性调整权重
    if (this.headingHistory.length >= 5) {
      const stability = this.calculateHeadingStability();
      if (stability > 0.8) { // 航向很稳定时，减少罗盘影响
        weight *= 0.7;
      } else if (stability < 0.4) { // 航向不稳定时，增加罗盘权重
        weight *= 1.3;
      }
    }
    
    return Math.max(0.1, Math.min(0.8, weight));
  }
  
  /**
   * 处理陀螺仪数据
   */
  processGyroData(gyroZ, dt) {
    // 死区滤波，忽略极小的旋转
    const deadZone = 0.1; // 度/秒
    const angularVelocity = gyroZ * 180 / Math.PI;
    
    if (Math.abs(angularVelocity) < deadZone) {
      return 0;
    }
    
    // 限制最大角速度，防止异常数据
    const maxAngularVelocity = 200; // 度/秒
    const clampedVelocity = Math.max(-maxAngularVelocity, Math.min(maxAngularVelocity, angularVelocity));
    
    return clampedVelocity * dt;
  }
  
  /**
   * 检查航向锁定
   */
  checkHeadingLock(rawHeading, timestamp) {
    const headingChange = Math.abs(this.getAngularDifference(rawHeading, this.state.heading));
    
    // 检查是否应该开始锁定
    if (!this.state.isLocked) {
      if (headingChange < this.state.lockThreshold) {
        if (this.state.lockStartTime === 0) {
          this.state.lockStartTime = timestamp;
        } else if (timestamp - this.state.lockStartTime > 500) { // 500ms稳定后锁定
          this.state.isLocked = true;
        }
      } else {
        this.state.lockStartTime = 0;
      }
    } else {
      // 检查是否应该解除锁定
      if (headingChange > this.state.lockThreshold * 2) {
        this.state.isLocked = false;
        this.state.lockStartTime = 0;
      }
    }
    
    // 如果锁定，减小航向变化
    let finalHeading = rawHeading;
    if (this.state.isLocked) {
      const lockingFactor = 0.1; // 锁定时只允许10%的变化
      finalHeading = this.blendAngles(this.state.heading, rawHeading, lockingFactor);
    }
    
    return {
      heading: finalHeading,
      isLocked: this.state.isLocked
    };
  }
  
  /**
   * 应用自适应平滑
   */
  applyAdaptiveSmoothing(rawHeading, timestamp) {
    // 动态调整平滑因子
    this.updateSmoothingParams();
    
    const smoothing = this.smoothingParams.current;
    return this.blendAngles(this.state.heading, rawHeading, 1 - smoothing);
  }
  
  /**
   * 更新平滑参数 - 基于运动状态优化
   */
  updateSmoothingParams() {
    if (!this.smoothingParams.adaptive) return;

    let targetSmoothing = this.smoothingParams.base;

    // 基于运动状态调整平滑参数
    const isMoving = this.isUserMoving();
    if (isMoving) {
      // 移动时使用较小的平滑系数，提高响应性
      targetSmoothing = this.smoothingParams.movingSmoothing;
    } else {
      // 静止时使用较大的平滑系数，提高稳定性
      targetSmoothing = this.smoothingParams.stationarySmoothing;
    }

    // 基于航向稳定性微调
    if (this.headingHistory.length >= 5) {
      const stability = this.calculateHeadingStability();

      if (stability > 0.9) {
        // 非常稳定时略微增加平滑
        targetSmoothing = Math.min(this.smoothingParams.maxSmoothing, targetSmoothing * 1.1);
      } else if (stability < 0.3) {
        // 非常不稳定时减少平滑
        targetSmoothing = Math.max(this.smoothingParams.minSmoothing, targetSmoothing * 0.9);
      }
    }

    // 快速调整到目标值
    const adjustRate = 0.3; // 增加调整速度
    this.smoothingParams.current += (targetSmoothing - this.smoothingParams.current) * adjustRate;
  }

  /**
   * 判断用户是否在移动
   */
  isUserMoving() {
    // 可以通过加速度、步态检测等判断
    // 这里简化为基于最近的航向变化
    if (this.headingHistory.length < 3) return false;

    const recentHeadings = this.headingHistory.slice(-3);
    let totalChange = 0;
    for (let i = 1; i < recentHeadings.length; i++) {
      const diff = Math.abs(this.getAngularDifference(
        recentHeadings[i].heading,
        recentHeadings[i-1].heading
      ));
      totalChange += diff;
    }

    return totalChange > 2.0; // 如果最近3次航向变化超过2度，认为在移动
  }
  
  blendAngles(angle1, angle2, weight) {
    const diff = ((angle2 - angle1 + 540) % 360) - 180;
    return (angle1 + diff * weight + 360) % 360;
  }
  
  /**
   * 计算角度差（-180到180度）
   */
  getAngularDifference(angle1, angle2) {
    const diff = angle1 - angle2;
    return ((diff + 540) % 360) - 180;
  }
  
  /**
   * 计算圆形平均值
   */
  calculateCircularMean(angles) {
    let x = 0, y = 0;
    for (const angle of angles) {
      const rad = angle * Math.PI / 180;
      x += Math.cos(rad);
      y += Math.sin(rad);
    }
    return (Math.atan2(y / angles.length, x / angles.length) * 180 / Math.PI + 360) % 360;
  }
  
  /**
   * 计算航向稳定性（0-1）
   */
  calculateHeadingStability() {
    if (this.headingHistory.length < 5) return 0.5;
    
    const recentHeadings = this.headingHistory.slice(-10).map(h => h.heading);
    const mean = this.calculateCircularMean(recentHeadings);
    
    // 计算标准偏差
    let sumSquaredDiff = 0;
    for (const heading of recentHeadings) {
      const diff = this.getAngularDifference(heading, mean);
      sumSquaredDiff += diff * diff;
    }
    
    const stdDev = Math.sqrt(sumSquaredDiff / recentHeadings.length);
    
    // 将标准偏差转换为稳定性分数（0-1）
    return Math.max(0, Math.min(1, 1 - stdDev / 30));
  }
  
  /**
   * 计算罗盘置信度
   */
  calculateCompassConfidence(compassData) {
    const accuracy = compassData.accuracy || 3;
    let confidence = Math.max(0.3, Math.min(1.0, accuracy / 3));
    
    // 基于历史稳定性调整置信度
    if (this.headingHistory.length >= 3) {
      const stability = this.calculateHeadingStability();
      confidence = confidence * 0.7 + stability * 0.3;
    }
    
    return confidence;
  }
  
  /**
   * 计算最终置信度
   */
  calculateFinalConfidence(rawConfidence, isLocked) {
    let finalConfidence = rawConfidence;
    
    // 锁定状态下置信度更高
    if (isLocked) {
      finalConfidence = Math.min(1.0, finalConfidence * 1.2);
    }
    
    // 基于航向历史稳定性调整
    if (this.headingHistory.length >= 5) {
      const stability = this.calculateHeadingStability();
      finalConfidence = finalConfidence * 0.8 + stability * 0.2;
    }
    
    return Math.max(0.1, Math.min(1.0, finalConfidence));
  }
  
  /**
   * 评估航向质量
   */
  assessHeadingQuality(confidence, isLocked) {
    let qualityScore = confidence;
    
    if (isLocked) qualityScore += 0.2;
    
    if (this.headingHistory.length >= 5) {
      const stability = this.calculateHeadingStability();
      qualityScore = qualityScore * 0.7 + stability * 0.3;
    }
    
    if (qualityScore > 0.8) return 'excellent';
    if (qualityScore > 0.6) return 'good';
    if (qualityScore > 0.4) return 'fair';
    return 'poor';
  }
  
  /**
   * 更新航向历史记录
   */
  updateHeadingHistory(heading, confidence, timestamp) {
    this.headingHistory.push({
      heading,
      confidence,
      timestamp
    });
    
    // 限制历史长度
    if (this.headingHistory.length > this.maxHistoryLength) {
      this.headingHistory.shift();
    }
  }
  
  setMagneticDeclination(declination) {
    this.magneticDeclination = declination;
  }
  
  calibrate(calibrationData) {
    if (calibrationData.magneticDeclination !== undefined) {
      this.setMagneticDeclination(calibrationData.magneticDeclination);
    }
  }
  
  reset() {
    this.state.heading = 0;
    this.state.lastUpdate = 0;
    this.state.initialized = false;
  }
  
  updateConfig(config) {
    this.config = { ...this.config, ...config };
  }
}