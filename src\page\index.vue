<template>
  <div class="map-viewer">
    <!-- 地图容器 -->
    <div ref="mapContainer" class="map-container"></div>

    <!-- 加载进度 -->
    <LoadingOverlay :visible="isLoading" :text="loadingText" :progress="loadingProgress" />

    <div class="floating-ui" v-if="!isLoading">
      <!-- 指南针 -->
      <Compass></Compass>

      <!-- 楼层选择器 - 完整LOD集成 -->
      <FloorPanel :auto-sync="true" :show-lod-status="true" :building-current-floors="buildingCurrentFloors"
        @lod-floor-change="onLODFloorChange" @floor-switch="onFloorSwitch"
        @exploded-mode-toggle="onExplodedModeToggle" />
    </div>
    <Locate @click="locateCurrentLocation" :isShow="isLockView"></Locate>
    <Passenger @click="showOrHidePassengerHandle" :isShow="isShowPassenger"></Passenger>
    <van-image :src="TipUrl" class="tip" v-if="isShowPassenger"></van-image>

    <div v-if="isOpenDebugger"
      style="position: absolute; left:80px; top: 20px; pointer-events: auto; display: flex; flex-direction: column; align-items: start">
      <div id="loginfo" v-if="false" style="font-size: 12px; font-weight: bolder;width: 300px;height: 300px;
    overflow-y: scroll;
    position: absolute;
    overflow-x: hidden;
    word-wrap: break-word;
    word-break:break-all;
    top: 20px;
">logInfo: <br /></div>
      <div id="loginfo2" style="font-size: 12px; font-weight: bolder;width: 300px;height: 300px;
    overflow-y: scroll;
    position: absolute;
    overflow-x: hidden;
    word-wrap: break-word;
    word-break:break-all;
    top: 20px;
">logInfo: <br /></div>
    </div>

    <ChatDialog v-if="platform == 1" :curChatMessage="curChatMessage" />
    <!-- 紧凑控制面板 -->
    <!-- <CompactControlPanel v-if="!isLoading" :models="modelList" :buildings="buildingList" :stats="stats"
            :lodManager="lodManager" @load-all="loadAllModels" @show-all="showAllModels" @hide-all="hideAllModels"
            @reset-view="resetView" @load-model="loadModel" @toggle-visibility="toggleVisibility"
            @switch-floor="switchFloor" /> -->

    <!-- 点击信息显示 -->
    <!-- <ClickInfo :click-data="clickInfo" @close="closeClickInfo" /> -->

    <!-- 室内建筑信息面板 -->
    <!-- <InteriorBuildingInfo :lodManager="lodManager" @floorChange="handleFloorChange"
            @configChange="handleLODConfigChange" /> -->

    <!-- 3D标注面板 -->
    <!-- <Annotation3DPanel v-if="!isLoading && annotationManager" :annotationManager="annotationManager" /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, provide, computed } from "vue";
import { useRefactoredMapViewer } from "@/hooks/useRefactoredMapViewer";

// 导入组件
import LoadingOverlay from "@/components/LoadingOverlay.vue";
import Compass from "@/components/Compass/index.vue";
import FloorPanel from "@/components/FloorPanel/index.vue";
import Locate from "@/components/locate/index.vue";
import Passenger from "@/components/passenger/index.vue";
import { eventBus } from "@/refactored/EventBus";
import TipUrl from "@/assets/image/tip.png";
import ChatDialog from "@/components/chatDialog.vue";
import InertialNavigationWeb from "@/lib/IMU/InertialNavigationWeb.js";
import { MLAIntegrationService } from "@/services/MLAIntegrationService.js";
import { showConfirmDialog } from "vant";

const mapContainer = ref<HTMLElement>();
const mlaService = ref(null);
const currFloor = ref('1');
const currBuildingId = ref('sw');
// 使用封装的 hook
const {
  // 响应式状态
  isLoading,
  pdrInitPos,
  loadingText,
  loadingProgress,
  clickInfo,

  // 计算属性
  modelList,
  buildingList,
  stats,

  // 管理器实例
  lodManager,

  // 楼层状态
  buildingCurrentFloors,
  initTestBeaconLocation,
  updateUserLocation,
  // 方法
  switchFloor,
  handleFloorChange,
  resetView,
  initializeApp,
  setupEventListeners,
  isLockView,
  locateCurrentLocation,
  showOrHidePassengerHandle,
  isShowPassenger,
} = useRefactoredMapViewer({
  defaultCenter: [114.35962345673624, 30.564406727759334],
  defaultZoom: 18,
});

const isOpenDebugger = computed(() => {
  return true;//window.isOpenDebugger;
});

// LOD楼层变更处理
const onLODFloorChange = (data: { buildingId: string; floor: number }) => {
  console.log("🏢 页面接收到LOD楼层变更:", data);
  // 调用hook中的楼层切换方法
  handleFloorChange(data.buildingId, data.floor);
};

// 楼层切换处理
const onFloorSwitch = (data: { buildingId: string; floor: number }) => {
  console.log("🎮 页面接收到楼层切换请求:", data);
  // 调用hook中的楼层切换方法
  switchFloor(data.buildingId, data.floor);
};

// 拆楼模式切换处理
const onExplodedModeToggle = (data: { buildingId: string; enabled: boolean }) => {
  console.log("🏗️ 页面接收到拆楼模式切换:", data);
  // 调用hook中的拆楼模式切换方法
  if (lodManager.value && typeof (lodManager.value as any).setExplodedMode === "function") {
    (lodManager.value as any).setExplodedMode(data.buildingId, data.enabled);
  }
};

// 提供LOD管理器给子组件
provide("lodManager", lodManager);

// 生命周期
onMounted(async () => {
  (window as any).writeLog = (text) => {
    return;
    document.getElementById("loginfo").innerHTML = document.getElementById("loginfo").innerHTML + (text + "<br/>");
  }
  (window as any).isLockView = false;
  (window as any).eventBus  = eventBus ;
  setupEventListeners();
  const isAndroid = /Android/.test(navigator.userAgent);
  platform.value = isAndroid ? 1 : 0
  // initMobileDebug(); // 初始化移动端调试
  await nextTick();
  // 等待容器元素准备好，然后初始化
  if (mapContainer.value) {
    await initializeApp(mapContainer.value);
    mlaService.value = new MLAIntegrationService();
    window.addEventListener("hashchange", hashChange);
    eventBus.on("loadComplete", () => {
      if (!isAuthorize.value) {
        showConfirmDialog({
          title: "权限请求",
          message: "为了体验更好的定位导航服务，请进行动作与方向授权。",
          async beforeClose(action) {
            if (action == "confirm") {
              isAuthorize.value = await requestPermissions();
              if (currentPosition.value.x && currentPosition.value.y) {
                initPDRWithPosition(currentPosition.value);
              }
              return true;
            } else {
              return false;
            }
          },
        });
      }
    });

    // 初始化获取socketId
    if (window.location.hash.split("?")?.[1]) {
      let queryString = window.location.hash.split("?")?.[1];
      const params = new URLSearchParams(queryString);
      eventBus.emit("updateSocketId", params.get("socketId"));
    }
  }
});

const isAuthorize = ref(false);
const requestPermissions = async () => {
  try {
    window.writeLog('开始验证传感器权限...');
    // 检查是否在HTTPS环境或localhost
    if (location.protocol !== "https:" && location.hostname !== "localhost" && location.hostname !== "127.0.0.1") {
      throw new Error("传感器访问需要HTTPS环境或localhost");
    }
    window.writeLog('开始验证设备运动权限...');
    // 请求设备运动权限（iOS 13+需要）
    if (typeof DeviceMotionEvent !== "undefined" && typeof DeviceMotionEvent.requestPermission === "function") {
      const motionPermission = await DeviceMotionEvent.requestPermission();
      if (motionPermission !== 'granted') {
        throw new Error('设备运动权限被拒绝');
      } else {
        return true;
      }
    }
    // 请求设备方向权限（iOS 13+需要）
    if (typeof DeviceOrientationEvent !== 'undefined' && typeof DeviceOrientationEvent.requestPermission === 'function') {
      const orientationPermission = await DeviceOrientationEvent.requestPermission();
      if (orientationPermission !== 'granted') {
        return false;
      } else {
        return true;
      }
    } else {
      console.log('🔍 非iOS设备或旧版本，无需请求权限');
    }
    return true;
  } catch (error) {
    console.error("❌ 传感器权限请求失败:", error);
    return false;
  }
};

let nav = null;
let pdrInitialized = ref(false);

// 🆕 接收app传来的初始位置来初始化PDR
const initPDRWithPosition = async (initialPosition: any) => {
  if (pdrInitialized.value) {
    window.writeLog("PDR已初始化，跳过重复初始化");
    return;
  }
   window.writeLog("PDR开始初始化，参数："+JSON.stringify(initialPosition));
  pdrInitPos.value = { x: initialPosition.x, y: initialPosition.y, z: initialPosition.z || 0 };
  nav = new InertialNavigationWeb({
    // 🆕 使用app传来的真实初始位置
    initialPosition: {
      x: initialPosition.x,
      y: initialPosition.y,
      z: initialPosition.z || 0,
    },
    // 可选：运行模式
    mode: "standard", // 'lite' | 'standard' | 'precise'
    // 可选：采样率
    sampleRate: 20,
    enableMLA: false, // 🆕 启用MLA
    fusion: {
      enabled: false,
      pdrWeight: 0.3,
      mlaWeight: 0.4, // 蓝牙环境下较高MLA权重
      adaptiveWeighting: true,
      maxCorrection: 8.0,
      confidenceThreshold: 0.3,
    },
    // 🆕 添加初始位置元信息
    metadata: {
      buildingId: initialPosition.buildingId,
      floor: initialPosition.floor,
      source: initialPosition.source || "beacon",
      confidence: initialPosition.confidence || 0.8,
      initTimestamp: Date.now(),
    },
  });

  // 🆕 绑定MLA服务
  mlaService.value.setNavigation(nav);

  nav.onPositionUpdate = (location: any) => {
    test1_userPos.value = location.position;
    // 优先使用PDR返回的楼层/建筑，退化到当前选择/初始值
    const emitFloorRaw = (location.floor ?? currFloor.value ?? initialPosition.floor ?? 1);
    const emitFloor = typeof emitFloorRaw === "string" ? parseInt(emitFloorRaw as any, 10) : emitFloorRaw;
    const emitBuildingId = (location.buildingId ?? currBuildingId.value ?? initialPosition.buildingId ?? "sw");

    eventBus.emit("updateUserPosInfo", {
      x: location.position.x,
      y: location.position.y,
      rotate: location.heading,
      floor: emitFloor,
      buildingId: emitBuildingId,
      initPdrPos: pdrInitPos.value,
    });
  };

  try {
    nav.calibrate({
      stepLength: 0.65,
      magneticDeclination: 0,
    });
    const success = await nav.start();
    if (success) {
      pdrInitialized.value = true;
      window.writeLog("PDR惯导定位启动成功，初始位置:"+JSON.stringify(initialPosition));
    } else {
      window.writeLog("PDR惯导定位启动失败");
    }
  } catch (error: any) {
       window.writeLog("PDR启动异常");
  }
};

// 🆕 兼容的手动PDR初始化（用于测试或备用）
const initPDR = async () => {
  const defaultPosition = {
    x: 114.361111,
    y: 30.563889,
    z: 0,
    buildingId: "sw",
    floor: "1",
    source: "manual",
    confidence: 0.5,
  };

  await initPDRWithPosition(defaultPosition);
};
const curChatMessage = ref({});
const platform = ref(0); // 0 为ios 1为android
const test_userPos = ref({});
const test1_userPos = ref({});
const currentPosition = ref({});

// URL变化处理函数
const hashChange = async () => {
  let queryString = window.location.hash.split("?")[1];
  console.log(queryString);
  //当前禁止hash操作
  if (window.isForbidHash) {
    return;
  }

  const params = new URLSearchParams(queryString);
  // 接受导航功能参数
  if (params.get("navInfo")?.length > 0) {
    const str = params.get("navInfo").replace(/(\w+):/g, '"$1":'); // 给 key 加双引号
    const navInfo = JSON.parse(decodeURIComponent(str));
    console.log("导航参数：", navInfo);
    if (navInfo.code == 0) {
      // 退出导航
      eventBus.emit("exitNavigateMode");
    } else if (navInfo.code == 1) {
      // 启动导航
      eventBus.emit("enterNavigateMode", navInfo.navId);
    }
  }
  // 接受用户实时位置
  if (params.get("userpos")?.length > 0) {
    const str = params.get("userpos").replace(/(\w+):/g, '"$1":'); // 给 key 加双引号
    const userpos = JSON.parse(decodeURIComponent(str));
    test_userPos.value = userpos;
    window.writeLog("接收到坐标:" + JSON.stringify(userpos));
    window.writeLog("权限验证结果" + isAuthorize.value);
    if (currentPosition.value?.timestamp != userpos.timestamp && isAuthorize.value) {//根据时间戳判断点位更新
      window.writeLog("坐标通过！");
      currentPosition.value = userpos;
      currBuildingId.value = userpos.buildingId;
      currFloor.value = userpos.floor;
      if (pdrInitialized.value && mlaService.value) {//根据pdr服务初始化状态判断是否执行mla
        window.writeLog("执行mla！");
        // mlaService.value.handleMiniProgramMLA({
        //   ...userpos,
        //   position: { x: userpos.x, y: userpos.y, z: 0 },
        // });
      } else {
        if (!userpos.x && !userpos.y) return;
        // 调用后端接口的位置纠偏接口
        // window.writeLog("调用吸附接口！");
        // const searchParams = new URLSearchParams();
        // searchParams.append("buildingId", userpos.buildingId);
        // searchParams.append("floor", userpos.floor + "F");
        // searchParams.append("x", userpos.x);
        // searchParams.append("y", userpos.y);
        // const response = await fetch((window as any).apiURL + `navigation/nearest-node?${searchParams.toString()}`);
        // const result = await response.json();
        // if (result?.node?.x_coord) {
        //   userpos.x = result.node.x_coord;
        //   userpos.y = result.node.y_coord;
        // }
        initPDRWithPosition({
          x: userpos.x,
          y: userpos.y,
          z: -5,
          buildingId: userpos.buildingId || "sw",
          floor: userpos.floor || "1",
          source: "manual",
          confidence: 0.5,
        });
      }
    }
  }

  // 对话
  if (params.get("curChatMessage")?.length > 0) {
    const info = JSON.parse(decodeURIComponent(params.get("curChatMessage")));
    if (curChatMessage.value && curChatMessage.value.content !== info.content) {
      curChatMessage.value = info;
    }
  }

  // 当前使用设备平台
  console.log(`params.get("platform")`, params.get("platform"));
  if (params.get("platform")?.length > 0) {
    platform.value = params.get("platform");
  }

  // 当前socket唯一标识
  if (params.get("socketId")?.length > 0) {
    eventBus.emit("updateSocketId", params.get("socketId"));
  }
};

onUnmounted(() => {
  // 清理资源
  lodManager.value?.dispose();
});
</script>

<style scoped>
.map-viewer {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.map-container {
  width: 100%;
  height: 100%;
}

.floating-ui {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 9;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  pointer-events: none;
  /* 允许点击穿透到地图 */
}

.floating-ui>* {
  pointer-events: auto;
  /* 恢复子元素的点击事件 */
}

.tip {
  position: absolute;
  bottom: 280px;
  z-index: 999;
  right: 10px;
  width: 50px;
  height: 127px;
}
</style>
