import mapboxgl from "mapbox-gl"
import "mapbox-gl/dist/mapbox-gl.css"
import * as THREE from "three"
import { FBXLoader } from "three/examples/jsm/Addons.js"
import { eventBus } from "@/refactored/EventBus"
import { LayerManager } from "./LayerManager"
import { DefaultModelTreeManager } from "./ModelTreeManager"
import type { ModelTreeManager } from "./ModelTypes"
import { gsap } from "gsap"

import tiandituConfig from "./tiandituConfig"
import { SceneConfig } from "./SceneConfig"
import Stats from "three/examples/jsm/libs/stats.module.js"
import { WaterSurface } from "./WaterSurface"
import { AccurateCoordinateConverter } from "./MapboxCoordinateConverter"
import { GeojsonManager } from "./GeojsonManger"
import { PassengerFlowManager } from "./PassengerManager"
import { TreeLoader } from "./TreeLoader"

import GeoJSONUrl from "/markers.geojson?url"
import { NavigationManager } from "./NavigationManager"
import { CameraController } from "./cameraControler"
import { BuildingLabelManager } from "./BuildingLabelManager"

//mapbox key
const MAPBOXKEY =
	"pk.eyJ1Ijoic3ZjLW9rdGEtbWFwYm94LXN0YWZmLWFjY2VzcyIsImEiOiJjbG5sMnExa3kxNTJtMmtsODJld24yNGJlIn0.RQ4CHchAYPJQZSiUJ0O3VQ"
//🔧 统一：使用场景配置中的统一参数
const mapCenter: [number, number] = SceneConfig.getMapCenter()
const modelAltitude = SceneConfig.getModelAltitude()
const modelRotate = SceneConfig.getModelRotation()
const modelAsMercatorCoordinate = mapboxgl.MercatorCoordinate.fromLngLat(
	mapCenter,
	modelAltitude
)

const modelTransform = {
	translateX: modelAsMercatorCoordinate.x,
	translateY: modelAsMercatorCoordinate.y,
	translateZ: modelAsMercatorCoordinate.z,
	rotateX: modelRotate[0],
	rotateY: modelRotate[1],
	rotateZ: modelRotate[2],
	/* Since the 3D model is in real world meters, a scale transform needs to be
	 * applied since the CustomLayerInterface expects units in MercatorCoordinates.
	 */
	scale: modelAsMercatorCoordinate.meterInMercatorCoordinateUnits(),
}

//配置mapboxtoken
mapboxgl.accessToken = MAPBOXKEY

//基础配置
export interface ApplicationConfig {
	//地图中心点
	defaultCenter?: [number, number]
	//地图缩放
	defaultZoom?: number
	//地图容器ID / 元素
	container: string | HTMLElement
}

//程序基底 采用Mapboxgl + threejs 实现导航+3D模型的功能
export default class MapApplication {
	//地图
	public map: mapboxgl.Map
	//three 场景
	public scene?: THREE.Scene
	//相机
	public camera?: THREE.PerspectiveCamera
	//渲染器
	public renderer?: THREE.WebGLRenderer
	//射线
	public raycaster?: THREE.Raycaster
	//鼠标
	public mouse?: THREE.Vector2
	//地图加载Promise
	public mapLoadPromise: Promise<void>
	//three加载Promise
	public threeLoadPromise?: Promise<void>
	private treeManager: ModelTreeManager
	private layerManager?: LayerManager
	private geojsonManager?: GeojsonManager
	private waterSurface?: WaterSurface
	private cameraController?: CameraController
	private buildingLabelManager?: BuildingLabelManager
	private clickCallbacks: Array<
		(event: {
			x: number
			y: number
			originalEvent: PointerEvent
			world: THREE.Vector3 | null
		}) => void
	> = []
	private mouseMoveCallbacks: Array<
		(event: {
			x: number
			y: number
			originalEvent: MouseEvent
			world: THREE.Vector3 | null
		}) => void
	> = []
	private renderCallbacks: Array<(camera: THREE.Camera) => void> = []
	public threeLayerReadyPromise!: Promise<void>
	private _threeLayerReadyResolve!: () => void
	public NavigationManager?: NavigationManager;
	public PassengerFlowManager?: PassengerFlowManager;
	public TreeLoader?: TreeLoader;

	// 相机位置保存
	private savedCameraPosition: THREE.Vector3 | null = null

	private isRotating: boolean = false
	private startPos:any = null
	private startBearing: number = 0
	private sensitivity = 0.3 // 旋转灵敏度

	private clearHandler: (() => void) | null = null

	constructor(config: ApplicationConfig) {
		// 初始化树管理器
		this.treeManager = new DefaultModelTreeManager()

		this.map = new mapboxgl.Map({
			container: config.container || "map",
			// style: tiandituConfig,
			style: "mapbox://styles/mapbox/streets-v11",
			center: (config.defaultCenter || mapCenter) as [number, number],
			zoom: config.defaultZoom || 18,
			maxZoom: 22,
			minPitch: 0,
			maxPitch: 80,
		})

		const { resolve, promise } = Promise.withResolvers()

		this.mapLoadPromise = promise
		this.geojsonManager = new GeojsonManager(GeoJSONUrl, this.map)
		this.cameraController = new CameraController(this.map)
		// Mapbox底图加载
		this.map.once("load", () => {
			eventBus.emit("scene-progress", 0.2)
			resolve()
		})

		this.map.on("rotate", (event) => {
			eventBus.emit("rotate", event)
		})

		eventBus.on("reset-rotate", () => {
			this.map.rotateTo(0)
		})

		eventBus.on("repaint", () => {
			//! 启用全局地图刷新
			// this.map.triggerRepaint()
		})

		const staticView = {
			center: {
				lng: 114.3595079907542,
				lat: 30.564486768980316,
			},
			zoom: 17,
			bearing: 150.36084002511257,
			pitch: 59.484507855679446,
		}
		eventBus.on("loadComplete", () => {
			this.map.flyTo({
				center: staticView.center,
				zoom: staticView.zoom,
				bearing: staticView.bearing,
				pitch: staticView.pitch,
				speed: 0.2, // 速度
			})
		})

		eventBus.on("reset-view", () => {
			this.map.flyTo({
				center: staticView.center,
				zoom: staticView.zoom,
				bearing: staticView.bearing,
				pitch: staticView.pitch,
				speed: 1.2, // 速度
			})
		})

		// 模型lod完毕
		eventBus.on("modelLoaded", () => {
			// 添加客流量面信息
			this.PassengerFlowManager = new PassengerFlowManager(this.scene)
		})
	}

	//获取地图实例
	public getMap(): mapboxgl.Map {
		return this.map
	}

	/**
	 * 设置灯光和阴影系统
	 */
	private setupLightingAndShadows(): void {
		// 环境光
		const ambientLight = new THREE.AmbientLight(0xffffff, 2.4)
		this.scene!.add(ambientLight)

		// 主方向光 - 投射阴影
		const dirLight = new THREE.DirectionalLight(0xffffff, 9.0)
		dirLight.position.set(-30, 100, -100)
		dirLight.castShadow = true
		const d2 = 1000
		const r2 = 2
		const mapSize2 = 8192
		// 阴影设置 - 优化以减少摩尔纹
		dirLight.castShadow = true
		dirLight.shadow.radius = r2
		dirLight.shadow.mapSize.width = mapSize2
		dirLight.shadow.mapSize.height = mapSize2
		dirLight.shadow.camera.top = dirLight.shadow.camera.right = d2
		dirLight.shadow.camera.bottom = dirLight.shadow.camera.left = -d2
		dirLight.shadow.camera.near = 1
		dirLight.shadow.camera.visible = true
		dirLight.shadow.camera.far = 400000000

		// 减少阴影偏移和摩尔纹
		// dirLight.shadow.bias = -0.0005
		// dirLight.shadow.normalBias = 0.02
		dirLight.updateMatrixWorld()
		this.scene!.add(dirLight)
	}

	/**
	 * 设置阴影渲染器
	 */
	private setupShadowRenderer(): void {
		if (!this.renderer) return

		// 启用阴影
		this.renderer.shadowMap.enabled = true
		// this.renderer.shadowMap.type = THREE.PCFSoftShadowMap

		// 减少摩尔纹的设置
		this.renderer.shadowMap.autoUpdate = true
		this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))

		// 启用更好的深度测试
		// this.renderer.sortObjects = true
	}

	//获取树管理器
	public getTreeManager(): ModelTreeManager {
		return this.treeManager
	}

	//获取图层管理器
	public getLayerManager(): LayerManager | undefined {
		return this.layerManager
	}

	//加载three图层
	public addThreeJsLayer() {
		this.threeLayerReadyPromise = new Promise<void>((resolve) => {
			this._threeLayerReadyResolve = resolve
		})

		let threeLoadResolve: () => void
		this.threeLoadPromise = new Promise<void>((res) => {
			threeLoadResolve = res
		})
		// const stats = new Stats()
		const customLayer = {
			id: "threejs-layer",
			type: "custom" as const,
			renderingMode: "3d" as const,
			onAdd: (map: mapboxgl.Map, gl: WebGLRenderingContext) => {
				this.scene = new THREE.Scene()
				this.layerManager = new LayerManager(
					this.scene,
					this.treeManager
				)
				this.camera = new THREE.PerspectiveCamera()
				this.raycaster = new THREE.Raycaster()
				this.mouse = new THREE.Vector2()
				this.NavigationManager = new NavigationManager(this.scene, map)
				// stats.showPanel(0)
				// document.body.appendChild(stats.dom)
				// 点击事件
				map.getCanvas().addEventListener(
					"pointerdown",
					(event: PointerEvent) => {
						this.raycast(event)
					}
				)

				this.startAnimationLoop()

				// 鼠标移动事件
				// map.getCanvas().addEventListener(
				// 	"mousemove",
				// 	(event: MouseEvent) => {
				// 		this.mousemove(event)
				// 	}
				// )

				// 添加灯光和阴影系统
				this.setupLightingAndShadows()
				this.setupShadowRenderer()
				this.renderer = new THREE.WebGLRenderer({
					canvas: map.getCanvas(),
					context: gl,
					antialias: true,
				})
				this.renderer.autoClear = false
				// 启用高质量阴影
				// this.setupShadowRenderer()

				this.buildingLabelManager = new BuildingLabelManager(
					this.scene,
					this.camera
				)

				// 在场景中存储渲染器引用，供水面使用
				if (this.scene) {
					this.scene.userData.renderer = this.renderer
				}

				// 创建水面
				this.createWaterSurface()


				// this.TreeLoader = new TreeLoader(this.scene)
				this._threeLayerReadyResolve() // 初始化完成
				threeLoadResolve!()
			},
			render: (_gl: WebGLRenderingContext, matrix: number[]) => {
				// stats.begin()
				const rotationX = new THREE.Matrix4().makeRotationAxis(
					new THREE.Vector3(1, 0, 0),
					modelTransform.rotateX
				)
				const rotationY = new THREE.Matrix4().makeRotationAxis(
					new THREE.Vector3(0, 1, 0),
					modelTransform.rotateY
				)
				const rotationZ = new THREE.Matrix4().makeRotationAxis(
					new THREE.Vector3(0, 0, 1),
					modelTransform.rotateZ
				)

				const m = new THREE.Matrix4().fromArray(matrix)
				const l = new THREE.Matrix4()
					.makeTranslation(
						modelTransform.translateX,
						modelTransform.translateY,
						modelTransform.translateZ
					)
					.scale(
						new THREE.Vector3(
							modelTransform.scale,
							-modelTransform.scale,
							modelTransform.scale
						)
					)
					.multiply(rotationZ)
					.multiply(rotationX)
					.multiply(rotationY)
				this.camera!.projectionMatrix = m.multiply(l)
				this.renderer!.resetState()

				// 更新水面动画
				if (this.waterSurface && this.camera) {
					this.waterSurface.update(this.camera)
				}
				this.renderer!.render(this.scene!, this.camera!)
				this.buildingLabelManager!.render()
				this.NavigationManager!.updateScale(this.camera!)
				// 调用渲染回调
				this.renderCallbacks.forEach((callback) => {
					if (this.camera) {
						callback(this.camera)
					}
				})

				// stats.end()
			},
		}

		this.map.addLayer(customLayer) //添加图层
		console.log("添加三维图层完成")
	}

	/** 注册地图点击回调 */
	public addClickCallback(
		cb: (event: {
			x: number
			y: number
			originalEvent: PointerEvent
			world: THREE.Vector3 | null
		}) => void
	) {
		this.clickCallbacks.push(cb)
	}

	/** 注销地图点击回调 */
	public removeClickCallback(
		cb: (event: {
			x: number
			y: number
			originalEvent: PointerEvent
			world: THREE.Vector3 | null
		}) => void
	) {
		this.clickCallbacks = this.clickCallbacks.filter((fn) => fn !== cb)
	}

	/** 注册鼠标移动回调 */
	public addMouseMoveCallback(
		cb: (event: {
			x: number
			y: number
			originalEvent: MouseEvent
			world: THREE.Vector3 | null
		}) => void
	) {
		this.mouseMoveCallbacks.push(cb)
	}

	/** 注册渲染回调 */
	public addRenderCallback(callback: (camera: THREE.Camera) => void) {
		this.renderCallbacks.push(callback)
	}

	/** 注销鼠标移动回调 */
	public removeMouseMoveCallback(
		cb: (event: {
			x: number
			y: number
			originalEvent: MouseEvent
			world: THREE.Vector3 | null
		}) => void
	) {
		this.mouseMoveCallbacks = this.mouseMoveCallbacks.filter(
			(fn) => fn !== cb
		)
	}

	/**
	 * 🎮 启动独立动画循环
	 * 确保水面和鱼群动画持续运行，不依赖地图交互
	 */
	private startAnimationLoop(): void {
		if (this.isAnimationRunning) return

		this.isAnimationRunning = true

		const animate = () => {
			if (!this.isAnimationRunning) return

			try {
				// 🌊 执行水面和鱼群动画更新
				if (this.waterSurface && this.camera) {
					this.waterSurface.update(this.camera)
				}

				// 🎮 执行 RenderManager 注册的渲染回调
				// if (this.camera) {
				// 	this.renderManager["renderCallbacks"]?.forEach?.(
				// 		(callback: (camera: THREE.Camera) => void) => {
				// 			try {
				// 				callback(this.camera!)
				// 			} catch (error) {
				// 				console.warn("动画回调执行失败:", error)
				// 			}
				// 		}
				// 	)
				// }

				// 🔄 触发Mapbox重绘以显示动画
				this.map.triggerRepaint()
			} catch (error) {
				console.warn("动画循环执行失败:", error)
			}

			// 继续下一帧
			this.animationId = requestAnimationFrame(animate)
		}

		// 启动动画循环
		this.animationId = requestAnimationFrame(animate)
		console.log("🎮 独立动画循环已启动")
	}

	//销毁
	public destroy() {
		this.layerManager?.dispose()
		this.renderer?.dispose()
		this.scene?.clear()
		this.camera?.clear()
		this.map.remove()
	}

	raycast(event: PointerEvent) {
		var mouse = new THREE.Vector2()
		// scale mouse pixel position to a percentage of the screen's width and height
		const rect = this.map.getCanvas().getBoundingClientRect()
		const x = event.clientX - rect.left
		const y = event.clientY - rect.top
		mouse.x = (x / this.map.transform.width) * 2 - 1
		mouse.y = 1 - (y / this.map.transform.height) * 2

		const camInverseProjection = this.camera?.projectionMatrix
			.clone()
			.invert()
		const cameraPosition = new THREE.Vector3().applyMatrix4(
			camInverseProjection!
		)
		const mousePosition = new THREE.Vector3(
			mouse.x,
			mouse.y,
			1
		).applyMatrix4(camInverseProjection!)
		const viewDirection = mousePosition
			.clone()
			.sub(cameraPosition)
			.normalize()

		this.raycaster!.set(cameraPosition, viewDirection)

		// calculate objects intersecting the picking ray
		let pickObjects = this.scene!.children
		var intersects = this.raycaster!.intersectObjects(pickObjects, true)
		if (intersects.length) {
			console.log("点击位置-去掉外立面之前:", intersects)
			// 过滤掉所有外立面
			intersects = intersects.filter(
				(o: any) => !o.object.parent.name.includes("exterior")
			)
			const target = intersects[0].point
			console.log("点击位置-去掉外立面之后:", intersects)

			// // 创建几何（宽, 高, 深）
			// const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
			// // 创建材质
			// const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
			// // 创建 Mesh
			// const cube = new THREE.Mesh(geometry, material);
			// cube.position.set(target.x, target.y, target.z); // X, Y, Z
			// // 添加到场景
			// this.scene!.add(cube);

			//
			const convert = new AccurateCoordinateConverter(
				SceneConfig.getMapCenter(),
				SceneConfig.getModelRotation(),
				SceneConfig.getBaseAltitude()
			)

			const coord = convert.worldToGeo(target)
			console.log(coord)

			this.clickCallbacks.forEach((cb) =>
				cb({
					x: target.x,
					y: target.y,
					originalEvent: event,
					world: target,
				})
			)
		}
	}

	mousemove(event: MouseEvent) {
		var mouse = new THREE.Vector2()
		// scale mouse pixel position to a percentage of the screen's width and height
		const rect = this.map.getCanvas().getBoundingClientRect()
		const x = event.clientX - rect.left
		const y = event.clientY - rect.top
		mouse.x = (x / this.map.transform.width) * 2 - 1
		mouse.y = 1 - (y / this.map.transform.height) * 2

		const camInverseProjection = this.camera?.projectionMatrix
			.clone()
			.invert()
		const cameraPosition = new THREE.Vector3().applyMatrix4(
			camInverseProjection!
		)
		const mousePosition = new THREE.Vector3(
			mouse.x,
			mouse.y,
			1
		).applyMatrix4(camInverseProjection!)
		const viewDirection = mousePosition
			.clone()
			.sub(cameraPosition)
			.normalize()

		this.raycaster!.set(cameraPosition, viewDirection)

		// calculate objects intersecting the picking ray
		let pickObjects = this.scene!.children
		var intersects = this.raycaster!.intersectObjects(pickObjects, true)
		if (intersects.length) {
			const target = intersects[0].point
			this.mouseMoveCallbacks.forEach((cb) =>
				cb({
					x: target.x,
					y: target.y,
					originalEvent: event,
					world: target,
				})
			)
		}
	}

	/**
	 * 创建水面
	 */
	private createWaterSurface(): void {
		if (!this.scene) {
			console.warn("⚠️ 场景未初始化，无法创建水面")
			return
		}

		try {
			this.waterSurface = new WaterSurface(this.scene)
			console.log("🌊 水面创建成功")
		} catch (error) {
			console.error("❌ 水面创建失败:", error)
		}
	}

	/**
	 * 获取水面实例
	 */
	public getWaterSurface(): WaterSurface | undefined {
		return this.waterSurface
	}

	/**
	 * 设置水面可见性
	 */
	public setWaterVisible(visible: boolean): void {
		if (this.waterSurface) {
			this.waterSurface.setVisible(visible)
		}
	}

	/**
	 * 设置水面透明度
	 */
	public setWaterOpacity(opacity: number): void {
		if (this.waterSurface) {
			this.waterSurface.setOpacity(opacity)
		}
	}

	/**
	 * 设置水面噪波强度
	 */
	public setWaterDistortion(scale: number): void {
		if (this.waterSurface) {
			this.waterSurface.setDistortionScale(scale)
		}
	}

	/**
	 * 关闭水面噪波
	 */
	public disableWaterDistortion(): void {
		if (this.waterSurface) {
			this.waterSurface.disableDistortion()
		}
	}

	public repaint() {
		//! 启用全局地图刷新
		// // 强制重新渲染地图和Three.js场景
		// this.map.triggerRepaint()
		// // 如果场景和渲染器存在，强制重新渲染
		// if (this.scene && this.camera && this.renderer) {
		// 	this.renderer.render(this.scene, this.camera)
		// }
	}

	/**
	 * 设置Mapbox底图可见性
	 */
	public setMapboxVisible(visible: boolean): void {
		try {
			if (visible) {
				// 显示底图：移除所有图层的隐藏样式
				const style = this.map.getStyle()
				if (style && style.layers) {
					style.layers.forEach((layer: any) => {
						if (
							layer.layout &&
							layer.layout.visibility === "none"
						) {
							this.map.setLayoutProperty(
								layer.id,
								"visibility",
								"visible"
							)
						}
					})
				}
				console.log(`🗺️ Mapbox底图显示`)
			} else {
				// 隐藏底图：隐藏所有图层但保持Three.js场景
				const style = this.map.getStyle()
				if (style && style.layers) {
					style.layers.forEach((layer: any) => {
						this.map.setLayoutProperty(
							layer.id,
							"visibility",
							"none"
						)
					})
				}
				console.log(`🗺️ Mapbox底图隐藏`)
			}
		} catch (error) {
			console.warn("⚠️ 设置Mapbox可见性失败:", error)
			// 降级方案：使用容器透明度，但只针对地图容器
			const container = this.map.getContainer()
			if (container) {
				const mapContainer = container.querySelector(
					".mapboxgl-map"
				) as HTMLElement
				if (mapContainer) {
					mapContainer.style.opacity = visible ? "1" : "0"
				}
			}
		}
	}

	/**
	 * 设置拆楼模式相机控制
	 */
	public setExplodedCameraMode(enabled: boolean, buildingId?: string): void {
		try {
			if (enabled && buildingId) {
				// 启用拆楼模式：保留旋转，禁用平移和缩放
				console.log(`🎥 启用拆楼模式相机控制: ${buildingId}`)

				// 禁用地图的平移和缩放，但保留旋转
				this.map.dragPan.disable() // 禁用拖拽平移
				this.map.scrollZoom.disable() // 禁用滚轮缩放
				this.map.boxZoom.disable() // 禁用框选缩放
				this.map.keyboard.disable() // 禁用键盘控制
				this.map.doubleClickZoom.disable() // 禁用双击缩放
				this.map.touchZoomRotate.disable() // 禁用触摸缩放

				// 保留旋转功能
				this.map.dragRotate.enable() // 保持拖拽旋转
				console.log(`🔒 拆楼模式：已禁用平移和缩放，保留旋转功能`)
			} else {
				// 禁用拆楼模式：恢复正常相机控制
				console.log(`🎥 禁用拆楼模式相机控制`)

				// 恢复地图的所有交互
				this.map.dragPan.enable()
				this.map.scrollZoom.enable()
				this.map.boxZoom.enable()
				this.map.dragRotate.enable()
				this.map.keyboard.enable()
				this.map.doubleClickZoom.enable()
				this.map.touchZoomRotate.enable()
				this.map.off("touchstart", this.touchstart)
				this.map.off("touchmove", this.touchmove)
				this.clearHandler && this.clearHandler()
				this.clearHandler = null
				console.log(`🔓 拆楼模式退出：已恢复所有地图交互`)
			}
		} catch (error) {
			console.warn("⚠️ 设置拆楼模式相机控制失败:", error)
		}
	}

	/**
	 * 拆楼模式下调整相机高度到指定楼层
	 */
	public adjustCameraHeightInExplodedMode(
		targetHeight: number,
		targetFloor: number
	): void {
		try {
			const camera = this.camera
			if (!camera) {
				console.warn("⚠️ 相机不可用，无法调整高度")
				return
			}

			console.log(
				`🎥 调整相机高度到楼层 ${targetFloor}F, 目标高度: ${targetHeight}`
			)

			// 使用GSAP平滑移动相机到目标高度
			const currentPosition = camera.position.clone()
			const targetPosition = currentPosition.clone()
			targetPosition.y = targetHeight // 在楼层上方20单位的高度


			// 创建相机移动动画
			gsap.to(camera.position, {
				y: targetPosition.y,
				duration: 0.5, // 0.5秒的平滑移动
				ease: "power2.inOut",
				onUpdate: () => {
					// 每帧更新相机矩阵
					camera.updateMatrixWorld()
					this.repaint()
				},
				onComplete: () => {
					console.log(
						`✅ 相机已移动到楼层 ${targetFloor}F 高度: ${targetPosition.y}`
					)
				},
			})
		} catch (error) {
			console.warn("⚠️ 调整拆楼模式相机高度失败:", error)
		}
	}

	/**
	 * 保存当前相机位置
	 */
	public saveCameraPosition(): void {
		try {
			const camera = this.camera
			if (!camera) {
				console.warn("⚠️ 相机不可用，无法保存位置")
				return
			}

			this.savedCameraPosition = camera.position.clone()
			console.log(`💾 已保存相机位置:`, {
				x: this.savedCameraPosition.x,
				y: this.savedCameraPosition.y,
				z: this.savedCameraPosition.z,
			})
		} catch (error) {
			console.warn("⚠️ 保存相机位置失败:", error)
		}
	}

	/**
	 * 恢复相机位置到保存的状态
	 */
	public restoreCameraPosition(): void {
		try {
			const camera = this.camera
			if (!camera) {
				console.warn("⚠️ 相机不可用，无法恢复位置")
				return
			}

			if (!this.savedCameraPosition) {
				console.warn("⚠️ 没有保存的相机位置")
				return
			}

			console.log(`🔄 恢复相机位置:`, {
				from: {
					x: camera.position.x,
					y: camera.position.y,
					z: camera.position.z,
				},
				to: {
					x: this.savedCameraPosition.x,
					y: this.savedCameraPosition.y,
					z: this.savedCameraPosition.z,
				},
			})

			// 使用GSAP平滑恢复相机位置
			gsap.to(camera.position, {
				x: this.savedCameraPosition.x,
				y: this.savedCameraPosition.y,
				z: this.savedCameraPosition.z,
				duration: 0.8, // 0.8秒的平滑移动
				ease: "power2.inOut",
				onUpdate: () => {
					// 每帧更新相机矩阵
					camera.updateMatrixWorld()
					this.repaint()
				},
				onComplete: () => {
					console.log(`✅ 相机位置已恢复`)
					// 清除保存的位置
					this.savedCameraPosition = null
				},
			})
		} catch (error) {
			console.warn("⚠️ 恢复相机位置失败:", error)
		}
	}

	public touchstart(e:any){
		if (e.points.length === 1) {
                // **关键改动**: 移除这里的 e.preventDefault()
                this.isRotating = true;
                this.startPos = e.points[0];
                this.startBearing = this.map.getBearing();
				this.startCameraPosition = this.camera?.position.clone() || null;
            } else {
                this.isRotating = false;
            }
	}

	public touchmove(e:any) {
		if (!this.isRotating || e.points.length !== 1) return;

            // **关键改动**: 将 e.preventDefault() 移动到这里！
            // 只有当用户确实在进行单指旋转操作时，才阻止默认行为（如页面滚动）。
            e.preventDefault();

            const currentPos = e.points[0];
            const dx = currentPos.x - this.startPos.x;
			const dy = currentPos.y - this.startPos.y;
            const newBearing = this.startBearing + dx * this.sensitivity;
            //  this.camera!.position.y = this.startCameraPosition?.y  + dy * this.sensitivity || 0;
			// console.log(this.camera!.position.y);
			// if( dy>10) {
			// 	eventBus.emit("selectFloor",)
			// }
			if(dy>130){
				eventBus.emit("upFloor")
			}else if(dy<-130){
				eventBus.emit("downFloor")
			}
			
            this.map.setBearing(newBearing);
	}
}
