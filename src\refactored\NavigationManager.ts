import * as THREE from "three";
import { NavigatorPathMeshGroup } from "./NavigatorPathMeshGroup";
// import arrow from "@/assets/image/arrow.png?url"
import locationIcon from "@/assets/image/location.svg?url";
import endImg from "@/assets/image/endPoint.svg?url";
import gsap from "gsap";
import { eventBus } from "./EventBus";
import pathUrl from "/path.json?url";
import { AccurateCoordinateConverter } from "./MapboxCoordinateConverter";
import { SceneConfig } from "./SceneConfig";
const speed = 10;

export class NavigationManager {
	private scene: THREE.Scene;
	private map: mapboxgl.Map;
	// private group?: NavigatorPathMeshGroup
	private originData: any;
	public userPosInfo?: any;
	public userPosInfo_origin?: any;
	private buildingInfo?: any = {};
	private userArrow?: THREE.Mesh;
	private endMeshBillboard?: any;
	private userPosBillboard?: any;
	private progress: number = 0;
	private groupList: NavigatorPathMeshGroup[] = [];
	private currentGroup?: NavigatorPathMeshGroup;
	private totalDistance?: number;
	private currentStep?: number = 0;
	private nowNavId?: any = null;
	private socketId?: any = null;

	constructor(scene: THREE.Scene, map: mapboxgl.Map) {
		this.scene = scene;
		this.map = map;
		// 导航位置图标
const userGeometry = new THREE.CircleGeometry(1, 32);
		const userMaterial = new THREE.MeshBasicMaterial({
			transparent: true,
			map: new THREE.TextureLoader().load(locationIcon),
			side: THREE.DoubleSide,
			depthTest: false,
		});
		this.userArrow = new THREE.Mesh(userGeometry, userMaterial);
		this.userArrow.rotateX(-Math.PI / 2);
		this.userArrow.position.set(1, 0.2, 0);
		this.userArrow.renderOrder = 999;
		this.scene.add(this.userArrow);
		this.userArrow.visible = false;
		this.addUserPos();
		this.userPosBillboard.visible = false;
		this.initBuildInfo();
		eventBus.on("exitNavigateMode", () => this.exitNavigateMode()); // 退出导航
		eventBus.on("enterNavigateMode", (navId) => this.enterNavigateMode(navId)); // 导航预览
		// eventBus.on("previewNavigateMode", () => this.previewNavigation()) // 实时导航
		eventBus.on("updateUserPosInfo", (userpos) => this.updateUserPos(userpos)); // 实时更新用户位置
		eventBus.on("updateSocketId", (socketId) => this.socketId = socketId); // 实时更新socketId
		// setInterval(async () => {
		// 	// 此处调用更新用户位置接口
		// 	if (this.socketId && this.userPosInfo) {
		// 		try {
		// 			fetch(
		// 				(window as any).apiURL + "navigation/location/update",
		// 				{
		// 					method: "POST",
		// 					body: JSON.stringify({
		// 						device_id: this.socketId,
		// 						location: {
		// 							accuracy: 3.0,
		// 							building_id: this.userPosInfo.buildingId,
		// 							buildingId: this.userPosInfo.buildingId,
		// 							floor: this.userPosInfo.floor + 'F',
		// 							x: this.userPosInfo.x,
		// 							y: this.userPosInfo.y,
		// 							rotate: this.userPosInfo.rotate,
		// 						}
		// 					}), // 请求体，传递的参数
		// 				}
		// 			);
		// 		} catch (error) {
		// 			console.error('location/update', error);
		// 		}
		// 	}
		// }, (window as any).updateUserPosServerTimer * 1000);
	}

	async initBuildInfo() {
		const response = await fetch(
			"https://fm-demo.daspatial.com/demo/web-indoor-navigation/dist/fbxModelList.json"
		);
		const rawData: any[] = await response.json();
		this.buildingInfo = {};
		rawData.forEach((item) => {
			let buildingId = item.id.split("-")[0];
			if (!this.buildingInfo[buildingId]) {
				this.buildingInfo[buildingId] = [];
			}
			let floor = item.floor;
			if (floor) {
				this.buildingInfo[buildingId].push(floor);
			}
		});
	}

	addUserPos() {
		// 1. 原用户圆标
const userGeometry = new THREE.CircleGeometry(1, 50);
		const userMaterial = new THREE.MeshBasicMaterial({
			transparent: true,
			map: new THREE.TextureLoader().load(locationIcon),
			side: THREE.DoubleSide,
			depthTest: false,
		});
		this.userPosBillboard = new THREE.Mesh(userGeometry, userMaterial);
		this.userPosBillboard.rotateX(-Math.PI / 2);
		this.userPosBillboard.renderOrder = 999;
		this.scene.add(this.userPosBillboard);
	}
	// 添加终点位置
	addEndMesh(group: any, endP: any) {
		// 1. 原用户圆标
		const userGeometry = new THREE.CircleGeometry(1, 50);
		const userMaterial = new THREE.MeshBasicMaterial({
			transparent: true,
			map: new THREE.TextureLoader().load(endImg),
			side: THREE.DoubleSide,
			depthTest: false,
		});
		this.endMeshBillboard = new THREE.Mesh(userGeometry, userMaterial);
		this.endMeshBillboard.rotateX(-Math.PI / 2);
		this.endMeshBillboard.renderOrder = 999;
		this.endMeshBillboard?.position.set(endP!.x, -6.8, endP!.z);
		group.add(this.endMeshBillboard);
		// 2. 外圈发光圈
		const ringGeometry = new THREE.RingGeometry(1.1, 1.2, 32);
		const ringMaterial = new THREE.MeshBasicMaterial({
			color: 0xD68260,
			transparent: true,
			opacity: 0.5,
			side: THREE.DoubleSide,
			depthTest: false
		});
		const pulseRing = new THREE.Mesh(ringGeometry, ringMaterial);
		// pulseRing.rotateX(-Math.PI / 2);
		pulseRing.renderOrder = 998;
		this.endMeshBillboard.add(pulseRing);
		const _self = this;
		// 3. 动画更新函数（放到渲染循环里）
		let scaleDir = 1;
		function animatePulse() {
			// 脉冲缩放
			pulseRing.scale.x += 0.01 * scaleDir;
			pulseRing.scale.y += 0.01 * scaleDir;

			if (pulseRing.scale.x > 1.5 || pulseRing.scale.x < 1) scaleDir *= -1;

			// 透明度变化
			ringMaterial.opacity = 0.5 + 0.5 * Math.sin(Date.now() * 0.005);

			requestAnimationFrame(animatePulse);
		}
		animatePulse();
	}

	addPath(pathData: any) {
		this.groupList = pathData.map((path: any, index: number) => {
			const group = new NavigatorPathMeshGroup(path.points);
			group.userData.pathId = path.pathId;
			group.userData.buildingId = path.buildingId;
			group.userData.floor = path.floor;
			group.userData.progress = 0;
			group.userData.distance = group.distance();
			const parent =
				this.scene.children.find(
					(child) => child.name === `${path.buildingId}-floor${path.floor}`
				) || this.scene;
			if (parent) {
				parent.add(group);
			} else {
				this.scene.add(group);
			}
			// 添加终点mesh
			if (index == pathData.length - 1) {
				this.addEndMesh(group, path.points[path.points.length - 1]);
			}
			return group;
		});
		// //添加起点终点
		// this.addStartEndPoints()

	}

	updateScale(camera: THREE.Camera) {
		//根据矩阵判断距离
		const position = this.getObserverPosition(camera);
		// //根据距离判断缩放
		// const distance = position.distanceTo(new THREE.Vector3(0, 0, 0))
		// let ratio = Math.max(1, distance / 150);
		// ratio = Math.min(ratio, 3.5);
		//根据高度判断缩放
		let ratio = Math.max(1, Math.abs(position.y) / 100);

		this.groupList?.forEach((group) => {
			group.updateWidth(ratio);
		});
		this.userArrow?.scale.set(ratio * 2, ratio * 2, ratio * 2);
		this.userPosBillboard?.scale.set(ratio * 2, ratio * 2, ratio * 2);
		this.endMeshBillboard?.scale.set(ratio * 2, ratio * 2, ratio * 2);
	}

	updateDirection(angle) {
		this.userArrow?.rotation.set(-Math.PI / 2, 0, angle);
	}

	async enterNavigateMode(navId: any) {
		if (this.currentStep == 2) return; // 已经是step2了
		this.currentStep = 2;
		this.resetNavigation();
		//todo 关闭LOD
		eventBus.emit("LODControlEnabled", false);
		eventBus.emit("isNavigation", true);

		//todo 显示对应楼层室内热力Polygon
		this.nowNavId = navId;
		// // 根据接收到的navId获取导航path信息
		// const response = await fetch(
		// 	(window as any).apiURL + "navigation/" + navId
		// );
		// const originData = await response.json();
		const pathData = await fetch(pathUrl);
		const originData = await pathData.json();
		this.startNavigation(originData);
	}
	startNavigation(originData: any) {
		originData.forEach((path: any, index: any) => {
			path.pathId = "NavigationPath_" + index;
			path.points = path.points.map((p: any) => {
				return { x: p.x, y: -5, z: p.y };
			});
		});
		// for (let i = 0; i < originData.length - 1; i++) {
		// 	originData[i].points.push(originData[i + 1].points[0]);
		// }
		// // 合并不在建筑内的到最近的楼层建筑中
		// this.originData = this.mergeNullBuildingPolygons(originData);
		this.originData = originData;
		// //! 测试代码
		// await this.getPathNavigation()

		//todo 添加路径
		this.addPath(this.originData);
		//todo 获取路径里程信息
		this.updateDistance();
		//todo 显示所有对应室内
		this.originData
			.reduce((acc: any, path: any) => {
				if (!acc.find((item: any) => item.buildingId === path.buildingId)) {
					acc.push(path);
				}
				return acc;
			}, [])
			.map((item: any) => {
				console.log(item);
				if (!item.buildingId || item.buildingId == "sw") return;
				eventBus.emit("floor-change-navigate", {
					buildingId: item.buildingId,
					floor: item.floor,
				}); // 先切换楼层
				if (this.buildingInfo[item.buildingId]?.length > 0) {
					this.buildingInfo[item.buildingId].forEach((mtfloor: any) => {
						if (item.floor != mtfloor) {
							eventBus.emit(
								"set-node-visibility",
								`${item.buildingId}-floor${mtfloor}`,
								false
							);
						}
					});
				}
				eventBus.emit(
					"set-node-visibility",
					`${item.buildingId}-exterior`,
					false
				);
				eventBus.emit(
					"set-node-visibility",
					`${item.buildingId}-floor${item.floor}`,
					true
				);
			});
		//todo 显示用户位置 并更新
		this.userArrow!.visible = true;
		const { pos, dir } = this.groupList[0].getPointAndTangentAt(0);
		this.userArrow?.position.copy(pos);
		// this.userArrow?.lookAt(pos.clone().add(dir))
		// this.userArrow?.rotateX(Math.PI / 2)
		// 触发重绘
		eventBus.emit("repaint");
		this.previewNavigation(); // 马上开始导航
		(window as any).isForbidHash = false;
	}
	previewNavigation() {
		// gsap.to(this, {
		// 	progress: 1,
		// 	duration: this.totalDistance! / speed,
		// 	ease: "none",
		// 	onUpdate: () => {
		// 		// console.log(`当前进度: ${this.progress}`)
		// 		this.updateProgress(this.progress)
		// 	},
		// });
		// 重置
		this.navigationAnalysisOBJ = {
			lastUserpos: null, // 用户上一次坐标信息
			nowIndex: 0, // 当前点集中的序号
			passingLength: 0, // 已经走过的长度
			gsapTween: null,
			lastDate: null,
			lastPathIndex: 0,
			limitTimeStart: null,
		};
		// // 隐藏所有楼层
		// this.scene.children.forEach((mesh) => {
		// 	if (mesh.name?.includes('-floor') && mesh.visible) {
		// 		// 隐藏楼层
		// 		eventBus.emit(
		// 			"set-node-visibility",
		// 			mesh.userData.nodeId,
		// 			false
		// 		)
		// 	}
		// });
		this.userPosBillboard.visible = false;
		this.updateProgress(0, true);
		(window as any).eventBus = eventBus;
	}
	// 添加图标
	addIconByVector3(position: any, iconUrl: any) {
		// 加载纹理
		const texture = new THREE.TextureLoader().load(iconUrl);

		// 创建精灵材质
		const spriteMaterial = new THREE.SpriteMaterial({
			map: texture,
			transparent: true,
		});

		// 创建精灵对象
		const sprite = new THREE.Sprite(spriteMaterial);

		// 设置位置（直接用 Vector3）
		sprite.position.copy(position);

		// 设置大小（X、Y 方向的像素比例）
		sprite.scale.set(10, 10, 1);

		return sprite;
	}

	private navigationAnalysisOBJ: any = {
		lastUserpos: null, // 用户上一次坐标信息
		nowIndex: 0, // 当前点集中的序号
		passingLength: 0, // 已经走过的长度
		gsapTween: null,
		lastDate: null,
		lastPathIndex: 0,
		limitTimeStart: null,
	};
	// 实时更新用户坐标 - 优化平滑处理
public async updateUserPos(userpos: any) {
		let { buildingId, floor, x, y, rotate } = userpos;
		// 角度安全转换
		const rotateNum = Number(rotate);
		const safeRotate = isFinite(rotateNum) ? rotateNum : 0;
		// 位置平滑处理（抗抖动，避免吞掉小位移）
		if (this.userPosInfo_origin) {
			const deltaX = x - this.userPosInfo_origin.x;
			const deltaY = y - this.userPosInfo_origin.y;
			const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

			// 忽略极小抖动
			if (totalDelta < 0.03) {
				x = this.userPosInfo_origin.x;
				y = this.userPosInfo_origin.y;
			} else if (totalDelta < 0.2) {
				// 轻量平滑，保留大部分增量
				const alpha = 0.6;
				x = this.userPosInfo_origin.x + deltaX * alpha;
				y = this.userPosInfo_origin.y + deltaY * alpha;
			}
			// 更新纠正后的位置
			userpos.x = x;
			userpos.y = y;
		}
		// 坐标系转换：惯导坐标系 -> Three.js坐标系
		// 惯导：X向东，Y向北 -> Three.js：X向东，Z向南（场景中Z轴正方向为南），Y向上
		// 因此需要翻转Y坐标：惯导+Y(北) -> Three.js-Z(北)
		let user_pos: THREE.Vector3 | null = new THREE.Vector3(x, -5, y);
		if (
			buildingId &&
			buildingId != "sw" &&
			(this.userPosInfo?.buildingId != buildingId ||
				this.userPosInfo?.floor != floor)
		) {
			eventBus.emit("floor-change-navigate", { buildingId, floor }); // 先切换楼层
			if (this.buildingInfo[buildingId]?.length > 0) {
				this.buildingInfo[buildingId].forEach((mtfloor: any) => {
					if (floor != mtfloor) {
						eventBus.emit(
							"set-node-visibility",
							`${buildingId}-floor${mtfloor}`,
							false
						);
					}
				});
			}
			eventBus.emit("set-node-visibility", `${buildingId}-exterior`, false);
			eventBus.emit("set-node-visibility", `${buildingId}-floor${floor}`, true);
		}
		this.userPosInfo_origin = userpos!;
		// 位置偏转
		user_pos = this.syncCoor(user_pos!.x, user_pos!.z, userpos.initPdrPos);
		
		if (document.getElementById("loginfo2")) {
			(document.getElementById("loginfo2") as any).innerHTML = (['当前传入:',buildingId, floor+'F', Math.round(x), Math.round(y)].join(',') + "<br/>");
			(document.getElementById("loginfo2") as any).innerHTML += (['上次处理后:',this.userPosInfo?.buildingId,this.userPosInfo?.floor+'F', Math.round(this.userPosInfo?.x || 0), Math.round(this.userPosInfo?.y || 0)].join(',') + "<br/>");
			(document.getElementById("loginfo2") as any).innerHTML += (['initPdrPos:', Math.round(userpos?.initPdrPos?.x || 0), Math.round(userpos?.initPdrPos?.z || 0)].join(',') + "<br/>");
			(document.getElementById("loginfo2") as any).innerHTML += (['位置偏转后:', Math.round(user_pos?.x || 0), Math.round(user_pos?.y || 0)].join(',') + "<br/>");
		}
		this.userPosInfo = userpos!;
		this.userPosInfo.x = user_pos.x;
		this.userPosInfo.y = user_pos.z;
		if (this.currentStep != 2) {
			// 未开启导航
			this.userPosBillboard.visible = true;
			// if (document.getElementById("rotate_x")) {
			// 	(document.getElementById("rotate_x") as any).innerHTML = user_pos!.x.toString();
			// 	(document.getElementById("rotate_y") as any).innerHTML = user_pos!.z.toString();
			// }
			this.userPosBillboard?.position.set(user_pos!.x, -6.8, user_pos!.z);
this.userPosBillboard.rotation.z = THREE.MathUtils.degToRad(
			-safeRotate - 28.37
		);
		if ((window as any).isLockView) {
				// this.map.setBearing(rotate - 28.37);
				const geo = new AccurateCoordinateConverter(
					SceneConfig.getMapCenter(),
					SceneConfig.getModelRotation(),
					SceneConfig.getBaseAltitude()
				).worldToGeo(user_pos!);
				this.map.jumpTo({
					center: [geo.longitude, geo.latitude],
					zoom: 19,
					pitch: 0,
					bearing: rotate - 28.37,
				});
			}
			return;
		}
this.userArrow!.rotation!.z = THREE.MathUtils.degToRad(
			-safeRotate - 28.37
		);
		this.userPosBillboard.visible = false;
		let nowPathIndex = this.groupList.findIndex(
			(o: any) =>
				o.userData.buildingId == buildingId && o.userData.floor == floor
		);
if (buildingId == "sw") {
			const sliceStart = Math.max(0, this.navigationAnalysisOBJ.lastPathIndex - 1);
			const relIdx = this.groupList
				.slice(
					sliceStart,
					this.groupList.length + 100
				)
				.findIndex((o: any) => o.userData.buildingId == "sw");
			if (relIdx !== -1) {
				nowPathIndex = sliceStart + relIdx;
			}
		}
		// 找不到，则使用上一次的index
		if (nowPathIndex == -1) {
			nowPathIndex = this.navigationAnalysisOBJ.lastPathIndex;
		}
		const nowPath: any = this.groupList[nowPathIndex];
if (!nowPath) {
			// 回退策略：显示用户圆点在实际位置，隐藏箭头
			this.userPosBillboard.visible = true;
			this.userArrow!.visible = false;
			this.userPosBillboard?.position.set(user_pos!.x, -6.8, user_pos!.z);
			this.userPosBillboard.rotation.z = THREE.MathUtils.degToRad(-safeRotate - 28.37);
			eventBus.emit("repaint");
			console.log("找不到路径");
			return;
		}
		let isForward = true; // 是否前进方向
		// 说明用户已切换楼层
		if (
			this.navigationAnalysisOBJ.lastUserpos?.buildingId != buildingId ||
			this.navigationAnalysisOBJ.lastUserpos?.floor != floor
		) {
			if (this.navigationAnalysisOBJ.lastPathIndex <= nowPathIndex) {
				// 说明用户是在前进
				this.navigationAnalysisOBJ.nowIndex = 0;
				isForward = true;
			} else {
				// 说明是在后退
				this.navigationAnalysisOBJ.nowIndex =
					nowPath.traveledPathPointList.array.length - 1;
				isForward = false;
			}
		}
		// 找到前5、后5个点集合（计算用户距离最近的垂点）
		const subPoints = nowPath.traveledPathPointList.array
			.slice(
				Math.max(this.navigationAnalysisOBJ.nowIndex - 25, 0),
				this.navigationAnalysisOBJ.nowIndex + 50
			)
			.map((o: any) => {
				return o.pos;
			});
		const obj: any = this.findNearestLineAndPoint(subPoints, user_pos);
		if (
			obj.distance > (window as any).NavigationLimitDistance &&
			(window as any).isOpenNavigationRestart
		) {
			if (!this.navigationAnalysisOBJ.limitTimeStart) {
				this.navigationAnalysisOBJ.limitTimeStart = new Date().getTime();
			}
			let noLimitDistance =
				(new Date().getTime() - this.navigationAnalysisOBJ.limitTimeStart) /
				1000;
			if (noLimitDistance > (window as any).NavigationLimitTime) {
				(window as any).isForbidHash = true;
				const response = await fetch(
					(window as any).apiURL + "navigation/" + this.nowNavId,
					{
						method: "POST",
						body: JSON.stringify(user_pos), // 请求体，传递的参数
					}
				);
				const originData = await response.json();
				// const pathData = await fetch(pathUrl);
				// const originData = await pathData.json();
				this.resetNavigation();
				this.startNavigation(originData);
				this.navigationAnalysisOBJ.limitTimeStart = null;
				console.log("超出范围");
				return;
			}
		} else {
			this.navigationAnalysisOBJ.limitTimeStart = null;
		}
		this.userPosInfo.x = obj.nearestPoint.x;
		this.userPosInfo.y = obj.nearestPoint.z;
		this.navigationAnalysisOBJ.nowIndex =
			Math.max(this.navigationAnalysisOBJ.nowIndex - 25, 0) + obj.nearestIndex;
		// 开始计算已经走过的所有长度（有可能倒退）
		this.navigationAnalysisOBJ.passingLength = 0;
		this.groupList.forEach((group, i) => {
			if (i < nowPathIndex) {
				this.navigationAnalysisOBJ.passingLength +=
					this.groupList[i].userData.distance;
			}
			// if (i == nowPathIndex) {
			// 	this.groupList[i].visible = true;
			// } else {
			// 	this.groupList[i].visible = false;
			// }
		});
		// 2.添加当前层的之前的结点长度
		this.navigationAnalysisOBJ.passingLength +=
			nowPath.traveledPathPointList.array[
				this.navigationAnalysisOBJ.nowIndex
			].dist;
		// 3.再添加结点到当前垂点的长度
		this.navigationAnalysisOBJ.passingLength += obj.nearestSegment.A.distanceTo(
			obj.nearestPoint
		);
		// 更新总进度
		let nowProgress =
			this.navigationAnalysisOBJ.passingLength / (this.totalDistance || 1);
		let duration = 2;
		if (this.navigationAnalysisOBJ.lastDate) {
			duration =
				(new Date().getTime() - this.navigationAnalysisOBJ.lastDate) / 1000;
		}
		if (this.navigationAnalysisOBJ.gsapTween) {
			this.navigationAnalysisOBJ.gsapTween.kill(); // 移除之前的动画
		}
		// this.navigationAnalysisOBJ.gsapTween = gsap.to(this, {
		// 	progress: nowProgress,
		// 	duration: Math.min(2, duration),
		// 	ease: "none",
		// 	onUpdate: () => {
		// 		this.updateProgress(this.progress)
		// 	},
		// });
		this.updateProgress(nowProgress, false, isForward);
		this.navigationAnalysisOBJ.lastDate = new Date().getTime();
		this.navigationAnalysisOBJ.lastPathIndex = nowPathIndex;
		this.navigationAnalysisOBJ.lastUserpos = {
			buildingId: nowPath.userData.buildingId,
			floor: nowPath.userData.floor,
			user_pos,
		};
	}

	public syncCoor(
		x: number | undefined,
		y: number | undefined,
		originPos: any
	) {
		// 🔧 修正：x,y已经是Three.js坐标系下的位置
		// x = Three.js X轴, y = Three.js Z轴

		if (x === undefined || y === undefined || !originPos) {
			console.warn('⚠️ syncCoor: 参数不完整', { x, y, originPos });
			return new THREE.Vector3(0, -5, 0);
		}

		// 1. 构建当前位置向量（Three.js坐标系）
		const currentPos = new THREE.Vector3(x, -5, y);

		// 2. 构建初始位置向量（originPos已经是场景坐标）
		const initialPos = new THREE.Vector3(
			originPos.x || 0,     // 场景X坐标
			-5,                   // Y轴高度
			originPos.y || 0      // 场景Z坐标（不需要取负）
		);

		// 3. 计算相对位移
		const deltaPos = currentPos.clone().sub(initialPos);

		// 4. 应用场景旋转（28.37度补偿）
		const sceneRotationAngle = (90 - 28.37) * Math.PI / 180;
		const rotationQuaternion = new THREE.Quaternion();
		rotationQuaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), sceneRotationAngle);

		// 旋转相对位移
		deltaPos.applyQuaternion(rotationQuaternion);

		// 5. 以初始PDR位置为基准，添加旋转后的位移
		const finalPos = initialPos.clone().add(deltaPos);

		console.log('🎯 syncCoor转换:', {
			input: { x, y },
			initial: { x: originPos.x, y: originPos.y },
			delta: { x: deltaPos.x.toFixed(2), z: deltaPos.z.toFixed(2) },
			final: { x: finalPos.x.toFixed(2), z: finalPos.z.toFixed(2) }
		});

		return finalPos;
	}

	private addStartEndPoints() {
		const firstGroup = this.groupList[0];
		const lastGroup = this.groupList[this.groupList.length - 1];
		if (firstGroup && lastGroup) {
			const startPoint = firstGroup.getPointAndTangentAt(0).pos;
			const endPoint = lastGroup.getPointAndTangentAt(1).pos;
			const startLatLng = new AccurateCoordinateConverter(
				SceneConfig.getMapCenter(),
				SceneConfig.getModelRotation(),
				SceneConfig.getBaseAltitude()
			).worldToGeo(startPoint);

			const endLatLng = new AccurateCoordinateConverter(
				SceneConfig.getMapCenter(),
				SceneConfig.getModelRotation(),
				SceneConfig.getBaseAltitude()
			).worldToGeo(endPoint);
			console.log(startLatLng, endLatLng);

			this.map.addSource("start-end", {
				type: "geojson",
				data: {
					type: "FeatureCollection",
					features: [
						{
							type: "Feature",
							properties: { icon: "/public/icon/start.png" },
							geometry: {
								type: "Point",
								coordinates: [startLatLng.longitude, startLatLng.latitude], // 起点
							},
						},
						{
							type: "Feature",
							properties: { icon: "/public/icon/start.png" },
							geometry: {
								type: "Point",
								coordinates: [endLatLng.longitude, endLatLng.latitude], // 终点
							},
						},
					],
				},
			});

			this.map.addLayer({
				id: "start-end-layer",
				type: "symbol",
				source: "start-end",
				layout: {
					"icon-image": ["get", "icon"],
					"icon-size": 0.2,
					"icon-allow-overlap": true,
					"icon-offset": [0, -150],
				},
			});
		}
	}

	private updateDistance() {
		this.progress = 0;
		this.totalDistance = this.groupList.reduce((total, group) => {
			return group.userData.distance + total;
		}, 0);
		this.groupList.forEach((group) => {
			group.userData.ratio = group.userData.distance / this.totalDistance!;
		});
	}

	private updateProgress(
		totalProgress: number,
		isFlyTo: boolean = false,
		isForward = true
	) {
		this.progress = totalProgress;
		let accProgress = 0;
		const prevGroup = this.currentGroup;
		for (const group of this.groupList) {
			accProgress += group.userData.ratio || 0;
			if (accProgress >= this.progress) {
				this.currentGroup = group;
				break;
			}
		}
		// // console.log(`当前组: ${this.currentGroup?.userData.pathId}`)
		// if (
		// 	this.currentGroup !== prevGroup &&
		// 	this.currentGroup?.userData.buildingId
		// ) {
		// 	if (this.currentGroup?.userData.buildingId && this.currentGroup?.userData.buildingId != 'sw') {
		// 		//todo 切换楼层
		// 		eventBus.emit("floor-change-navigate", {
		// 			buildingId: this.currentGroup?.userData.buildingId,
		// 			floor: this.currentGroup?.userData.floor,
		// 		})
		// 	}
		// 	// 如果当初层与上一个层不在一个同一个建筑物内，则显示上一个顶盖，隐藏上一个楼层
		// 	if (
		// 		prevGroup?.userData.buildingId &&
		// 		prevGroup?.userData.buildingId !=
		// 		this.currentGroup?.userData.buildingId
		// 	) {

		// 		if (prevGroup?.userData.buildingId && prevGroup?.userData.buildingId != 'sw' && this.currentGroup?.userData.buildingId && this.currentGroup?.userData.buildingId != 'sw') {
		// 			// 显示顶盖
		// 			// eventBus.emit(
		// 			// 	"set-node-visibility",
		// 			// 	`${prevGroup?.userData.buildingId}-exterior`,
		// 			// 	true
		// 			// )
		// 			// 隐藏楼层
		// 			eventBus.emit(
		// 				"set-node-visibility",
		// 				`${prevGroup?.userData.buildingId}-floor${prevGroup?.userData.floor}`,
		// 				false
		// 			)
		// 		}
		// 	}
		// 	if (this.currentGroup?.userData.buildingId && this.currentGroup?.userData.buildingId != 'sw') {
		// 		// 影藏当前顶盖
		// 		eventBus.emit(
		// 			"set-node-visibility",
		// 			`${this.currentGroup?.userData.buildingId}-exterior`,
		// 			false
		// 		)
		// 		// 显示当前楼层
		// 		eventBus.emit(
		// 			"set-node-visibility",
		// 			`${this.currentGroup?.userData.buildingId}-floor${this.currentGroup?.userData.floor}`,
		// 			true
		// 		)
		// 	}
		// }

		const currentProgress =
			(this.progress - (accProgress - this.currentGroup!.userData.ratio)) /
			this.currentGroup!.userData.ratio;
		if (isForward) {
			// 前进
			prevGroup?.updateProgress(1);
		} else {
			// 后退
			prevGroup?.updateProgress(0);
		}
		this.currentGroup?.updateProgress(currentProgress);
		const { pos, dir } = this.currentGroup?.getPointAndTangentAt(
			currentProgress
		) || { pos: new THREE.Vector3(), dir: new THREE.Vector3() };
		pos.y += 0.1; // 确保用户位置在地面上方
		this.userArrow?.position.copy(pos);
		// this.userArrow?.lookAt(pos.clone().add(dir));
		// this.userArrow?.rotateX(Math.PI / 2);

		// 沿着朝向反向偏移
		const distance = 40;
		const observerX = pos.x - dir.x * distance;
		const observerY = 100;
		const observerZ = pos.z - dir.z * distance;
		// console.log(observerX, observerY, observerZ)

		if (isFlyTo || (window as any).isLockView) {
			// eventBus.emit(
			// 	"setCamera",
			// 	new THREE.Vector3(observerX, observerY, observerZ),
			// 	pos
			// )
			const geo = new AccurateCoordinateConverter(
				SceneConfig.getMapCenter(),
				SceneConfig.getModelRotation(),
				SceneConfig.getBaseAltitude()
			).worldToGeo(pos);
			this.map.jumpTo({
				center: [geo.longitude, geo.latitude],
				zoom: 19,
				pitch: 0,
				bearing: 0,
			});
		}
		eventBus.emit("repaint");
	}

	private exitNavigateMode() {
		if (this.currentStep == 1) return; // 已经是step1了
		this.currentStep = 1;
		(window as any).isLockView = false;
		//todo 打开LOD
		eventBus.emit("LODControlEnabled", true)
		eventBus.emit("isNavigation", false)
		this.resetNavigation();
		this.nowNavId = undefined;
		this.currentStep = 0;
		this.nowNavId = undefined;
		(window as any).isForbidHash = false;
	}

	resetNavigation() {
		//清除轨迹
		this.groupList.forEach((group: any) => {
			group.parent.remove(group);
		});
		//隐藏导航图标
		this.userArrow!.visible = false;

		//清除当前动画
		gsap.killTweensOf(this);

		// //todo 回到最初视角
		// eventBus.emit("reset-view")

		if (this.navigationAnalysisOBJ.gsapTween) {
			this.navigationAnalysisOBJ.gsapTween.kill(); // 移除之前的动画
		}
		(window as any).eventBus = null;

		// 重置
		this.navigationAnalysisOBJ = {
			lastUserpos: null, // 用户上一次坐标信息
			nowIndex: 0, // 当前点集中的序号
			passingLength: 0, // 已经走过的长度
			gsapTween: null,
			lastDate: null,
			lastPathIndex: 0,
			limitTimeStart: null,
		};

		//清空当前路径
		this.groupList = [];
		this.currentGroup = undefined;
		//清除起点终点
		this.map.getLayer("start-end-layer") &&
			this.map.removeLayer("start-end-layer");
		this.map.getLayer("start-end") && this.map.removeLayer("start-end");
	}

	/**
	 * 获取真实的observer位置（适用于Mapbox环境）
	 */
	private getObserverPosition(camera: THREE.Camera): THREE.Vector3 {
		try {
			// 方法1：通过投影矩阵逆变换获取observer位置
			const camInverseProjection = camera.projectionMatrix.clone().invert();
			const observerPosition = new THREE.Vector3().applyMatrix4(
				camInverseProjection
			);

			// console.log(observerPosition)
			// 验证结果是否合理
			if (observerPosition.length() > 0 && isFinite(observerPosition.x)) {
				return observerPosition;
			}
		} catch (error) {
			console.warn("⚠️ 投影矩阵逆变换失败:", error);
		}

		// 降级方案：使用相机position
		console.warn("⚠️ 使用降级方案：相机position");
		return camera.position.clone();
	}

	//! 测试
	private async getPathNavigation() {
		const pathData = await fetch(pathUrl);
		const json = await pathData.json();
		this.originData = json;
		eventBus.on("loadComplete", () => {
			// this.addPath(this.originData)
			// this.enterNavigateMode()
			// this.previewNavigation()
			if (!this.map.hasImage("start-marker"))
				this.map.loadImage("/public/icon/start.png", (error, image) => {
					if (error) throw error;
					this.map.addImage("start-marker", image);
				});
			if (!this.map.hasImage("end-marker"))
				this.map.loadImage("/public/icon/end.png", (error, image) => {
					if (error) throw error;
					this.map.addImage("end-marker", image);
				});
		});
	}
	private closestPointOnLineSegment(A: any, B: any, P: any) {
		const AP = new THREE.Vector3().subVectors(P, A);
		const AB = new THREE.Vector3().subVectors(B, A);

		const ab2 = AB.dot(AB); // |AB|^2
		const ap_ab = AP.dot(AB); // AP · AB
		let t = ap_ab / ab2;

		// 限制 t 在 [0,1] 之间
		if (t < 0) t = 0;
		else if (t > 1) t = 1;

		const closest = new THREE.Vector3().copy(A).addScaledVector(AB, t);
		const distance = P.distanceTo(closest);

		return { closest, distance, t };
	}
	// 找到点到多线段的最近线段和垂点坐标
	private findNearestLineAndPoint(points: any, target: any) {
		let minDist = Infinity;
		let nearestSegment = null;
		let nearestPoint = null;
		let nearestIndex = null;

		for (let i = 0; i < points.length - 1; i++) {
			const A = points[i];
			const B = points[i + 1];
			const { closest, distance } = this.closestPointOnLineSegment(
				A,
				B,
				target
			);

			if (distance < minDist) {
				minDist = distance;
				nearestSegment = { A, B };
				nearestPoint = closest;
				nearestIndex = i;
			}
		}

		return { nearestSegment, nearestPoint, distance: minDist, nearestIndex };
	}
	// 如果遇到 buildingId === null 的条目，就找它前一个或后一个有 buildingId 的条目,优先向前合并，如果前面没有有 buildingId 的，就向后合并
	private mergeNullBuildingPolygons(data) {
		let result = [...data]; // 浅拷贝，避免改原数据
		for (let i = 0; i < result.length; i++) {
			let item = result[i];
			if (item.buildingId == "ground") {
				// 优先找前面的有 buildingId 的
				let targetIndex = -1;
				let direction = ""; // "forward" 向前合并，"backward" 向后合并

				for (let j = i - 1; j >= 0; j--) {
					if (result[j].buildingId != "ground") {
						targetIndex = j;
						direction = "forward";
						break;
					}
				}

				// 如果前面没有，找后面的
				if (targetIndex === -1) {
					for (let j = i + 1; j < result.length; j++) {
						if (result[j].buildingId != "ground") {
							targetIndex = j;
							direction = "backward";
							break;
						}
					}
				}

				// 如果找到合并目标
				if (targetIndex !== -1) {
					if (direction === "forward") {
						// 向前添加 -> 放到目标 points 的末尾
						result[targetIndex].points.push(...item.points);
					} else {
						// 向后添加 -> 放到目标 points 的开头
						result[targetIndex].points.unshift(...item.points);
					}
					// 删除当前 null 项
					result.splice(i, 1);
					i--; // 删除后回退索引
				}
			}
		}
		return result;
	}
}
