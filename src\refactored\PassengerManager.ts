import * as THREE from "three"
//渲染客流Polygon

interface PassengerFlow {
	title: string
	inTotal: number
	outTotal: number
	comeinTotal: number
	points: { x: number; y: number; z: number }[]
	buildingId: string
	floor: number,
	area: number,
}
const staticInfo = [
	// {
	// 	"title": "湖北省博物馆出入口",
	// 	points: [
	// 		{
	// 			"x": -7.303107918081032,
	// 			"y": -7.1,
	// 			"z": -121.41186992186056
	// 		},
	// 		{
	// 			"x": -38.07833169533332,
	// 			"y": -7.1,
	// 			"z": -121.58023864327848
	// 		},
	// 		{
	// 			"x": -38.32504864936148,
	// 			"y": -7.1,
	// 			"z": -132.343101705576
	// 		},
	// 		{
	// 			"x": -7.294412226463805,
	// 			"y": -7.1,
	// 			"z": -131.885728197876
	// 		}
	// 	],
	// 	buildingId: null,
	// 	floor: null,
	// },
	{
		"title": "天籁展厅",
		points: [
			{
				"x": -10.702000697428776,
				"y": -6.8,
				"z": 181.432753423836
			},
			{
				"x": -35.869038776352625,
				"y": -6.8,
				"z": 181.42038475425784
			},
			{
				"x": -36.25827012710059,
				"y": -6.8,
				"z": 156.94271607061444
			},
			{
				"x": -10.905018269578948,
				"y": -6.8,
				"z": 156.66906131716135
			}
		],
		// area: 874,
		buildingId: 'SouthMuseum',
		floor: -1,
	},
	{
		"title": "当代英杰一展厅",
		points: [
			{
				"x": -9.528532246321426,
				"y": -6.8,
				"z": 115.34636344712794
			},
			{
				"x": -34.002372438946054,
				"y": -6.8,
				"z": 115.31254654346483
			},
			{
				"x": -34.279588796983326,
				"y": -6.8,
				"z": 101.35113864329605
			},
			{
				"x": -9.167785182758779,
				"y": -6.8,
				"z": 101.38405049459121
			}
		],
		// area: 3927 / 4,
		buildingId: 'NorthMuseum',
		floor: 4,
	},
	{
		"title": "当代英杰二展厅",
		points: [
			{
				"x": -40.35531187943639,
				"y": -6.8,
				"z": 106.42673043658166
			},
			{
				"x": -49.3900281261416,
				"y": -6.8,
				"z": 106.61179431781451
			},
			{
				"x": -49.635466008233855,
				"y": -6.8,
				"z": 81.94336920774886
			},
			{
				"x": -40.48910822070029,
				"y": -6.8,
				"z": 82.22893406786291
			}
		],
		// area: 3927 / 4,
		buildingId: 'NorthMuseum',
		floor: 4,
	},
	{
		"title": "当代英杰三展厅",
		points: [
			{
				"x": -9.43125298885511,
				"y": -6.8,
				"z": 81.43789882731126
			},
			{
				"x": -33.973024837286644,
				"y": -6.8,
				"z": 81.40069906897098
			},
			{
				"x": -33.77669906616211,
				"y": -6.8,
				"z": 73.52247291976674
			},
			{
				"x": -9.452133190046126,
				"y": -6.8,
				"z": 73.50947494630579
			}
		],
		// area: 3927 / 4,
		buildingId: 'NorthMuseum',
		floor: 4,
	},
	{
		"title": "当代英杰四展厅",
		points: [
			{
				"x": 5.922288642575623,
				"y": -6.8,
				"z": 106.30511087093076
			},
			{
				"x": -3.0836186317253755,
				"y": -6.8,
				"z": 106.46184388064809
			},
			{
				"x": -3.1866935261921867,
				"y": -6.8,
				"z": 81.76865660977018
			},
			{
				"x": 6.106197712881926,
				"y": -6.8,
				"z": 81.85660003406986
			}
		],
		// area: 3927 / 4,
		buildingId: 'NorthMuseum',
		floor: 4,
	},
	{
		"title": "越王勾践剑展厅",
		points: [
			{
				"x": -34.181110173158245,
				"y": -6.8,
				"z": 136.96613266083773
			},
			{
				"x": -8.41353333785878,
				"y": -6.8,
				"z": 136.98326782479992
			},
			{
				"x": -8.391021123949606,
				"y": -6.8,
				"z": 151.32092430732047
			},
			{
				"x": -34.20895156099619,
				"y": -6.8,
				"z": 151.40007478071752
			}
		],
		// area: 635,
		buildingId: 'SouthMuseum',
		floor: 2,
	},
	{
		"title": "演奏厅",
		points: [
			{
				"x": -52.765626328031175,
				"y": -6.8,
				"z": 145.37868873459377
			},
			{
				"x": -52.79948366772315,
				"y": -6.8,
				"z": 156.74229519444063
			},
			{
				"x": -59.610750283254575,
				"y": -6.8,
				"z": 156.94653392976588
			},
			{
				"x": -59.096422107558304,
				"y": -6.8,
				"z": 159.2828122182607
			},
			{
				"x": -57.62742665052732,
				"y": -6.8,
				"z": 162.10195314216207
			},
			{
				"x": -56.25721531847343,
				"y": -6.8,
				"z": 165.5904669619447
			},
			{
				"x": -55.88391914492806,
				"y": -6.8,
				"z": 167.69876374009397
			},
			{
				"x": -56.21001700803828,
				"y": -6.8,
				"z": 170.52688531668903
			},
			{
				"x": -56.74914023865297,
				"y": -6.8,
				"z": 173.04967386565968
			},
			{
				"x": -58.253647054451505,
				"y": -6.8,
				"z": 175.38828954448914
			},
			{
				"x": -60.01732015132335,
				"y": -6.8,
				"z": 178.00265647808558
			},
			{
				"x": -61.898835464032985,
				"y": -6.8,
				"z": 179.44566609470218
			},
			{
				"x": -64.39326055068021,
				"y": -6.8,
				"z": 180.29473835302002
			},
			{
				"x": -68.0578528458059,
				"y": -6.8,
				"z": 179.82172498957527
			},
			{
				"x": -69.99397816809773,
				"y": -6.8,
				"z": 179.60272413192337
			},
			{
				"x": -72.54238916155182,
				"y": -6.8,
				"z": 179.55463527086408
			},
			{
				"x": -74.04738092123249,
				"y": -6.8,
				"z": 177.96192612767564
			},
			{
				"x": -75.3405824216368,
				"y": -6.8,
				"z": 176.49492637534973
			},
			{
				"x": -76.41780382598012,
				"y": -6.8,
				"z": 175.25998117485815
			},
			{
				"x": -77.57739321389744,
				"y": -6.8,
				"z": 172.1528667895425
			},
			{
				"x": -78.09172138959399,
				"y": -6.8,
				"z": 169.81658850104765
			},
			{
				"x": -78.38739039326677,
				"y": -6.8,
				"z": 167.6376923230713
			},
			{
				"x": -77.856304390539,
				"y": -6.8,
				"z": 165.338921098048
			},
			{
				"x": -77.11191736771244,
				"y": -6.8,
				"z": 163.34687686617698
			},
			{
				"x": -76.4636353939002,
				"y": -6.8,
				"z": 161.94953308887145
			},
			{
				"x": -75.89002586140404,
				"y": -6.8,
				"z": 160.54951023560284
			},
			{
				"x": -75.22834850778047,
				"y": -6.8,
				"z": 158.77880425171725
			},
			{
				"x": -74.99595227031668,
				"y": -6.8,
				"z": 156.46931672284512
			},
			{
				"x": -77.08142247524063,
				"y": -6.8,
				"z": 156.24495771326824
			},
			{
				"x": -82.8338795325361,
				"y": -6.8,
				"z": 156.11334130548607
			},
			{
				"x": -83.50057335245569,
				"y": -6.8,
				"z": 155.93988473919262
			},
			{
				"x": -83.04011397256643,
				"y": -6.8,
				"z": 145.18973226564924
			}
		],
		buildingId: 'SouthMuseum',
		floor: -1,
	},
	{
		"title": "曾侯乙一层展厅",
		points: [
			{
				"x": 4.7254688782655485,
				"y": -6.8,
				"z": 151.3795339928348
			},
			{
				"x": 8.627357509398664,
				"y": -6.8,
				"z": 151.14556110096726
			},
			{
				"x": 8.629265011309258,
				"y": -6.8,
				"z": 142.037149902251
			},
			{
				"x": 45.74821566726858,
				"y": -6.8,
				"z": 142.10825495017008
			},
			{
				"x": 45.76411146696884,
				"y": -6.8,
				"z": 181.76048471144298
			},
			{
				"x": 41.379855822880074,
				"y": -6.8,
				"z": 181.5870782597562
			},
			{
				"x": 41.34649738848057,
				"y": -6.8,
				"z": 186.04174060517974
			},
			{
				"x": 8.470252712045546,
				"y": -6.8,
				"z": 185.86819371401242
			},
			{
				"x": 8.97645669251866,
				"y": -6.8,
				"z": 170.7979719081415
			},
			{
				"x": 4.866123173230662,
				"y": -6.8,
				"z": 170.6225632600855
			}
		],
		// area: 2364,
		buildingId: 'SouthMuseum',
		floor: 1,
	},
	{
		"title": "曾侯乙二层展厅",
		points: [
			{
				"x": 4.523688619918627,
				"y": -6.8,
				"z": 152.46842361116376
			},
			{
				"x": 14.258819671605004,
				"y": -6.8,
				"z": 152.45382012945612
			},
			{
				"x": 14.084385571213701,
				"y": -6.8,
				"z": 143.6742176527776
			},
			{
				"x": 47.2692409007509,
				"y": -6.8,
				"z": 144.13005022539986
			},
			{
				"x": 47.35129618699253,
				"y": -6.8,
				"z": 191.85262738970303
			},
			{
				"x": 8.422338704924194,
				"y": -6.8,
				"z": 190.7997426227976
			},
			{
				"x": 8.118465975174953,
				"y": -6.8,
				"z": 159.2293179601501
			},
			{
				"x": 4.433428927082801,
				"y": -6.8,
				"z": 158.95708030739405
			}
		],
		// area: 3090,
		buildingId: 'SouthMuseum',
		floor: 2,
	},
	{
		"title": "研学中心",
		points: [
			{
				"x": -34.2371863053877,
				"y": -6.8,
				"z": 135.45678974933813
			},
			{
				"x": -8.577502913606931,
				"y": -6.8,
				"z": 135.39295548240904
			},
			{
				"x": -8.661620165562972,
				"y": -6.8,
				"z": 150.40377495192246
			},
			{
				"x": -34.17825228303878,
				"y": -6.8,
				"z": 150.5072465333764
			},
		],
		buildingId: 'SouthMuseum',
		floor: 1,
	},
	{
		"title": "东馆儿童展厅",
		points: [
			{
				"x": 54.21689815931203,
				"y": -6.8,
				"z": -6.999579113368134
			},
			{
				"x": 25.184403917171817,
				"y": -6.8,
				"z": -7.038586187842902
			},
			{
				"x": 25.186299033563127,
				"y": -6.8,
				"z": -35.944009003546675
			},
			{
				"x": 53.71263998730296,
				"y": -6.8,
				"z": -35.470119949390146
			}
		],
		// area: 1136,
		buildingId: 'EastMuseum',
		floor: 2,
	},
	{
		"title": "曾世家展厅",
		points: [
			{
				"x": -88.17356539497204,
				"y": -6.8,
				"z": 144.0009628699318
			},
			{
				"x": -50.48896321036845,
				"y": -6.8,
				"z": 143.9998934379459
			},
			{
				"x": -50.356362453406014,
				"y": -6.8,
				"z": 155.10726551232005
			},
			{
				"x": -46.95371650788478,
				"y": -6.8,
				"z": 155.0806303060937
			},
			{
				"x": -46.471955254633414,
				"y": -6.8,
				"z": 187.63686131522127
			},
			{
				"x": -50.77845111026576,
				"y": -6.8,
				"z": 187.78822008266258
			},
			{
				"x": -50.89201117050902,
				"y": -6.8,
				"z": 191.3275365956933
			},
			{
				"x": -87.94583373581327,
				"y": -6.8,
				"z": 190.84747964034293
			},
		],
		// area: 2833,
		buildingId: 'SouthMuseum',
		floor: 2,
	},
	{
		"title": "湖北近代风云展厅",
		points: [
			{
				"x": -80.93182551878637,
				"y": -6.8,
				"z": 80.33341484165994
			},
			{
				"x": -56.43658886661383,
				"y": -6.8,
				"z": 80.15116806125484
			},
			{
				"x": -56.39250865291038,
				"y": -6.8,
				"z": 64.46336413361982
			},
			{
				"x": -40.970688186057366,
				"y": -6.8,
				"z": 64.46240234375
			},
			{
				"x": -40.9976506391167,
				"y": -6.8,
				"z": 107.11020185913952
			},
			{
				"x": -80.85267605163331,
				"y": -6.8,
				"z": 106.80246051227498
			}
		],
		// area: 3877,
		buildingId: 'NorthMuseum',
		floor: 3,
	},
	{
		"title": "西馆一层展厅",
		points: [
			{
				"x": -77.63021292805006,
				"y": -6.8,
				"z": -1.610899215137994
			},
			{
				"x": -103.79751674694148,
				"y": -6.8,
				"z": -1.83060620963295
			},
			{
				"x": -103.70538698719474,
				"y": -6.8,
				"z": -41.201422333769116
			},
			{
				"x": -77.18010502262693,
				"y": -6.8,
				"z": -41.27770223340802
			},
		],
		buildingId: 'WestMuseum',
		floor: 1,
	},
	{
		"title": "西馆二层展厅",
		points: [
			{
				"x": -77.84451405219531,
				"y": -6.8,
				"z": -7.065262601680247
			},
			{
				"x": -97.60839950268635,
				"y": -6.8,
				"z": -7.126178056032252
			},
			{
				"x": -97.68420106532413,
				"y": -6.8,
				"z": -35.70318221014438
			},
			{
				"x": -77.8695503695067,
				"y": -6.8,
				"z": -35.614210356801976
			},
		],
		buildingId: 'WestMuseum',
		floor: 2,
	},
	{
		"title": "楚国八百年一展厅",
		points: [
			{
				"x": -85.72224892960698,
				"y": -6.8,
				"z": 144.3618390217916
			},
			{
				"x": -57.87312103377847,
				"y": -6.8,
				"z": 144.2277308946093
			},
			{
				"x": -58.356326407724,
				"y": -6.8,
				"z": 153.04769210995138
			},
			{
				"x": -44.225491740200376,
				"y": -6.8,
				"z": 152.7433972065548
			},
			{
				"x": -44.30101283516764,
				"y": -6.8,
				"z": 174.49065175706912
			},
			{
				"x": -52.969353849501154,
				"y": -6.8,
				"z": 174.41177283796216
			},
			{
				"x": -52.70212271329311,
				"y": -6.8,
				"z": 190.62897298195145
			},
			{
				"x": -86.00160898598844,
				"y": -6.8,
				"z": 190.3384259092764
			},
		],
		// area: 1270,
		buildingId: 'SouthMuseum',
		floor: 3,
	},
	{
		"title": "楚国八百年二展厅",
		points: [
			{
				"x": -34.13429379903036,
				"y": -6.8,
				"z": 144.56026727189456
			},
			{
				"x": -10.812930401656915,
				"y": -6.8,
				"z": 144.54577936638114
			},
			{
				"x": -11.23575432706693,
				"y": -6.8,
				"z": 175.54280643585253
			},
			{
				"x": -34.499642720302376,
				"y": -6.8,
				"z": 175.61526915695973
			},
		],
		// area: 2777,
		buildingId: 'SouthMuseum',
		floor: 3,
	},
	{
		"title": "梁庄王展厅",
		points: [
			{
				"x": -1.3870308611497606,
				"y": -6.8,
				"z": 152.76672931647525
			},
			{
				"x": 12.395027785658627,
				"y": -6.8,
				"z": 152.71164529598948
			},
			{
				"x": 12.356478707100877,
				"y": -6.8,
				"z": 143.47377334826587
			},
			{
				"x": 41.674893373338776,
				"y": -6.8,
				"z": 143.9329111499059
			},
			{
				"x": 41.23404090336115,
				"y": -6.8,
				"z": 166.88808750424676
			},
			{
				"x": 13.811162223383278,
				"y": -6.8,
				"z": 166.39009567768517
			},
			{
				"x": 13.745311011256717,
				"y": -6.8,
				"z": 174.267552162174
			},
			{
				"x": -1.1768984287576885,
				"y": -6.8,
				"z": 174.11880932102645
			},
		],
		// area: 2834,
		buildingId: 'SouthMuseum',
		floor: 3,
	},
	{
		"title": "馆藏历代陶瓷展厅",
		points: [
			{
				"x": 40.91830010663459,
				"y": -6.8,
				"z": 106.4622723654304
			},
			{
				"x": 0.6867679062771792,
				"y": -6.8,
				"z": 106.20003790183468
			},
			{
				"x": 0.3675003051757777,
				"y": -6.8,
				"z": 64.47781541508868
			},
			{
				"x": 15.846067071831143,
				"y": -6.8,
				"z": 64.62057318620914
			},
			{
				"x": 15.755083379512563,
				"y": -6.8,
				"z": 79.17534469292639
			},
			{
				"x": 40.71368714828627,
				"y": -6.8,
				"z": 79.33876071936396
			}
		],
		buildingId: 'NorthMuseum',
		floor: 3,
	},
	{
		"title": "北馆二层临展厅",
		points: [
			{
				"x": -1.3281993865966797,
				"y": -6.8,
				"z": 80.19198058274674
			},
			{
				"x": 21.37177060656129,
				"y": -6.8,
				"z": 80.03146335186239
			},
			{
				"x": 21.374309833994985,
				"y": -6.8,
				"z": 72.15589015205336
			},
			{
				"x": 47.32240104675293,
				"y": -6.8,
				"z": 71.8433434969787
			},
			{
				"x": 47.07419178233556,
				"y": -6.8,
				"z": 116.11313124425072
			},
			{
				"x": -1.4705423251111363,
				"y": -6.8,
				"z": 116.15381228800207
			}
		],
		// area: 2556,
		buildingId: 'NorthMuseum',
		floor: 2,
	},
	{
		"title": "三峡展厅",
		points: [
			{
				"x": 60.24328002646712,
				"y": -6.8,
				"z": -7.431141955557254
			},
			{
				"x": 37.101381475886676,
				"y": -6.8,
				"z": -7.315606721755638
			},
			{
				"x": 37.491164421670526,
				"y": -6.8,
				"z": -41.810008912099406
			},
			{
				"x": 60.25554247980784,
				"y": -6.8,
				"z": -41.8826805685457
			}
		],
		// area: 1278,
		buildingId: 'EastMuseum',
		floor: 1,
	},
	{
		"title": "湖北古代文明展厅",
		points: [
			{
				"x": -43.09015590193489,
				"y": -6.8,
				"z": 107.44178914148613
			},
			{
				"x": -50.850780646685024,
				"y": -6.8,
				"z": 107.13343014598013
			},
			{
				"x": -50.27235363542623,
				"y": -6.8,
				"z": 115.36685410231823
			},
			{
				"x": -90.7721868569921,
				"y": -6.8,
				"z": 115.37122273451256
			},
			{
				"x": -90.6073278616629,
				"y": -6.8,
				"z": 72.61365948058406
			},
			{
				"x": -51.66877736791655,
				"y": -6.8,
				"z": 72.72605499394923
			},
			{
				"x": -51.475077551573094,
				"y": -6.8,
				"z": 80.43789672851562
			},
			{
				"x": -42.974735753300095,
				"y": -6.8,
				"z": 80.87338270496224
			}
		],
		// area: 2840,
		buildingId: 'NorthMuseum',
		floor: 2,
	},
	{
		"title": "特展厅",
		points: [
			{
				"x": 40.94722895516337,
				"y": -2.3,
				"z": 190.99428166641266
			},
			{
				"x": 8.010389461179699,
				"y": -2.3,
				"z": 191.0352656151012
			},
			{
				"x": 8.384631432725651,
				"y": -2.3,
				"z": 173.90886696092875
			},
			{
				"x": 13.921477075661507,
				"y": -2.3,
				"z": 174.03519872740003
			},
			{
				"x": 14.311543499264772,
				"y": -2.3,
				"z": 167.23872687511545
			},
			{
				"x": 40.95176230709585,
				"y": -2.3,
				"z": 167.2492968628625
			},
		],
		// area: 1272,
		buildingId: 'SouthMuseum',
		floor: 3,
	},
];
export class PassengerFlowManager {
	private scene: THREE.Scene | any
	private raw: any[] = []
	public meshList: THREE.Mesh[] = []
	constructor(scene: THREE.Scene | undefined) {
		this.scene = scene;
		// 计算多边形面积
		staticInfo.forEach((p: any) => {
			if (p.points?.length > 0 && !p.area) {
				p.area = polygonArea2D(p.points);
			}
		});
		this.initData();
	}

	async initData() {
		const response = await fetch((window as any).FlowDataUrl)
		const rawJSON = (await response.json()).data
		this.raw = staticInfo.map((item) => {
			const flow = rawJSON.find((flow: any) => flow.title === item.title)
			return {
				...item,
				inTotal: flow?.inTotal || 0,
				outTotal: flow?.outTotal || 0,
				comeinTotal: flow?.comeinTotal || 0,
			}
		})
		this.render();
	}

	render() {
		this.raw.forEach((flow) => {
			if (!flow.points) return;
			const polygon = drawPolygon(flow.points, {
				color: getColorByDensity(flow),
			});
			polygon.userData = flow;
			const parent = this.scene.children.find(
				(child: any) =>
					(child.name == `${flow.buildingId}-floor${flow.floor}`)
			) || this.scene.children.find(child=>child.name==='ground');
			parent.add(polygon);
			this.meshList.push(polygon);
		})
		this.updateColors();
	}

	// 动态更新颜色
	updateColors() {
		setTimeout(() => {
			this.meshList.forEach((mesh: any) => {
				const color = getColorByDensity(mesh.userData);
				mesh.material.color.set(color); // 红色
			});
		}, (window as any).FlowDataTimer * 1000);
	}
}

//通过客流密度获取颜色
const getColorByDensity = (flowInfo: PassengerFlow) => {
	const { comeinTotal, area } = flowInfo;
	const density = comeinTotal / area * 10;
	if (density < 1) return 0x00B14D
	if (density < 2) return 0x04B0F1
	if (density < 3) return 0xFEFF00
	if (density >= 3) return 0xC10002

	return 0x00B14D
}

//计算2d面积
const polygonArea2D = (points: any) => {
	let area = 0;
	const n = points.length;
	for (let i = 0; i < n; i++) {
		const j = (i + 1) % n;
		area += points[i].x * points[j].z - points[j].x * points[i].z;
	}
	return Math.abs(area) / 2;
}

/**
 * 绘制 Polygon
 * @param {THREE.Vector3[] | number[][]} points - 顶点数组，可以是二维 [x,z] 或三维 [x,y,z] / Vector3
 * @param {Object} options - 配置项
 * @param {THREE.Color | number | string} [options.color=0xff0000] - 颜色
 * @param {boolean} [options.yUp=true] - 是否 Y 轴向上
 * @param {boolean} [options.doubleSide=true] - 是否双面
 * @returns {THREE.Mesh} 返回创建的 mesh
 */
export function drawPolygon(
	points: any,
	{ color = 0xff0000, yUp = true, doubleSide = true } = {}
) {
	let minH = Infinity;
	// 转换成 Vector3 数组
	let vec3Points = points.map((p: any) => {
		minH = Math.min(minH, p.y);
		return new THREE.Vector3(p.x, p.y, p.z)
	})
	// 1. 投影 XZ 平面到二维 Vector2
	const points2D = vec3Points.map((p: any) => new THREE.Vector2(p.x, p.z));

	// 2. 创建 Shape
	const shape = new THREE.Shape(points2D);

	// 3. 创建几何
	const geometry = new THREE.ShapeGeometry(shape);

	// 4. 创建材质（支持透明）
	const material = new THREE.MeshBasicMaterial({
		color: color,
		side: THREE.DoubleSide,
		transparent: true,
		opacity: 0.7
	});

	// 5. 创建 Mesh
	const polygon = new THREE.Mesh(geometry, material);

	// 6. 旋转到 XZ 平面
	polygon.rotation.x = Math.PI / 2; // 将 XY 面旋转到 XZ

	// 7. 如果希望位置在原始 Y，高度可以用平均值或者固定值
	polygon.position.y = minH;
	return polygon;
}
