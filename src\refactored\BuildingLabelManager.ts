import { CSS2DObject, CSS2<PERSON>enderer } from "three/examples/jsm/Addons.js"
import * as THREE from "three"
import EastUrl from "@/assets/icon/east.svg"
import WestUrl from "@/assets/icon/west.svg"
import NorthUrl from "@/assets/icon/north.svg"
import SouthUrl from "@/assets/icon/south.svg"
import { eventBus } from "./EventBus"

const buildingList = [
	{
		id: "EastMuseum",
		label: "东馆·少儿体验馆",
		position: {
			x: 39.586931975563466,
			y: 17.102200222015389,
			z: 5.204634219611117,
		},
		icon: EastUrl,
	},
	{
		id: "WestMuseum",
		label: "西馆",
		position: {
			x: -83.07501813219615,
			y: 17.099499893188478,
			z: 4.88726724684781,
		},
		icon: WestUrl,
	},
	{
		id: "NorthMuseum",
		label: "北馆",
		position: {
			x: -22.293158871520262,
			y: 37.115100097656251,
			z: 95.02719632696099,
		},
		icon: NorthUrl,
	},
	{
		id: "SouthMuseum",
		label: "南馆",
		position: {
			x: -20.376959195352544,
			y: 26.749999999999963,
			z: 168.13195695322628,
		},
		icon: SouthUrl,
	},
]

export class BuildingLabelManager {
	private css2DRenderer: CSS2DRenderer
	private scene: THREE.Scene
	private camera: THREE.Camera
	private group = new THREE.Group()
	constructor(scene: THREE.Scene, camera: THREE.Camera) {
		this.scene = scene
		this.camera = camera
		console.log(this.scene)

		// this.css2DRenderer.domElement.style.position = "absolute"
		// this.css2DRenderer.domElement.style.top = "0px"
		// this.css2DRenderer.domElement.style.pointerEvents = "none"
		this.css2DRenderer = new CSS2DRenderer()
		this.css2DRenderer.setSize(window.innerWidth, window.innerHeight)
		this.css2DRenderer.domElement.style.position = "absolute"
		this.css2DRenderer.domElement.style.top = "0px"
		this.css2DRenderer.domElement.style.pointerEvents = "none"
		document.body.appendChild(this.css2DRenderer.domElement)
		this.group.name = "building-label-group"
		this.scene.add(this.group)
		this.loadLabel()

		eventBus.on("lod-building-entered", () => {
			this.group.visible = false
		})
		eventBus.on("lod-building-exited", () => {
			this.group.visible = true
		})
	}

	loadLabel() {
		buildingList.map((item) => {
			const div = document.createElement("img")
			div.className = "building-label"
			div.src = item.icon
			const label = new CSS2DObject(div)
			label.position.set(
				item.position.x,
				item.position.y,
				item.position.z
			)
			this.group.add(label)
			return label
		})
	}

	render() {
		this.css2DRenderer.render(this.scene, this.camera)
	}
}
