<template>
  <div>
    <van-image v-show="props.isShow" :src="locateUrl_selected" class="locate"></van-image>
    <van-image v-show="!props.isShow" :src="locateUrl" class="locate"></van-image>
  </div>
</template>

<script setup lang="ts">
import locateUrl from "@/assets/image/locate.svg"
import locateUrl_selected from "@/assets/image/locate_selected.svg"
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});
</script>

<style scoped lang="scss">
.locate{
    width: 44px;
    height: 35px;
    position: absolute;
    left: 17px;
    bottom: 330px;
    z-index: 999;
}

</style>