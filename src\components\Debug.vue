<template>
    <div class="debug-panel">
        <van-cell-group inset>
            <van-field v-model="lng" label="经度" placeholder="输入经度" />
            <van-field v-model="lat" label="纬度" placeholder="输入纬度" />
            <van-field v-model="alt" label="高度" placeholder="输入高度"></van-field>
            <!-- 选择楼层建筑 -->
            <van-field v-model="building" is-link readonly text="picker" label="选择器" placeholder="点击选择城市"
                @click="showPicker = true" />
            <van-popup v-model:show="showPicker" destroy-on-close position="bottom">
                <van-picker :columns="columns" :model-value="pickerValue" @cancel="showPicker = false"
                    @confirm="onConfirm" />
            </van-popup>
            <div style="margin: 16px;">
                <van-button round block type="primary" native-type="submit" @click="confirm">
                    confirm
                </van-button>
            </div>
        </van-cell-group>
    </div>
</template>

<script setup lang="ts">
import { ref,onMounted } from 'vue';
const emit = defineEmits(["focusPoint"])

const lat = ref()
const lng = ref()
const alt = ref()
const building = ref()
const showPicker = ref(false)
const pickerValue = ref([]);
const buildingInfo = ref({
    building: '',
    floor: -1
})

const onConfirm = ({ selectedValues, selectedOptions }) => {
    building.value = selectedOptions.map(item => item.text).join(',');
    pickerValue.value = selectedValues;
    buildingInfo.value = {
        building: selectedOptions[0].value,
        floor: selectedOptions[1].floor
    }

    showPicker.value = false;

};

const columns = [
    {
        "value": "EastMuseum-exterior",
        "text": "东馆",
        "children": [
            {
                "value": "EastMuseum-floor1",
                "text": "东馆一层",
                "floor": 1,
                "visible": true,
                "isSelected": false
            },
            {
                "value": "EastMuseum-floor2",
                "text": "东馆二层",
                "floor": 2,
                "visible": false,
                "isSelected": false
            }
        ]
    },
    {
        "value": "WestMuseum-exterior",
        "text": "西馆",
        "children": [
            {
                "value": "WestMuseum-floor1",
                "text": "西馆一层",
                "floor": 1,
                "visible": true,
                "isSelected": false
            },
            {
                "value": "WestMuseum-floor2",
                "text": "西馆二层",
                "floor": 2,
                "visible": false,
                "isSelected": false
            }
        ]
    },
    {
        "value": "NorthMuseum-exterior",
        "text": "北馆",
        "children": [
            {
                "value": "NorthMuseum-floor2",
                "text": "北馆二层",
                "floor": 2,
                "visible": false,
                "isSelected": false
            },
            {
                "value": "NorthMuseum-floor3",
                "text": "北馆三层",
                "floor": 3,
                "visible": false,
                "isSelected": false
            },
            {
                "value": "NorthMuseum-floor4",
                "text": "北馆四层",
                "floor": 4,
                "visible": false,
                "isSelected": false
            }
        ]
    },
    {
        "value": "SouthMuseum-exterior",
        "text": "南馆",
        "children": [
            {
                "value": "SouthMuseum-floor-1",
                "text": "南馆负一层",
                "floor": -1,
                "visible": false,
                "isSelected": false
            },
            {
                "value": "SouthMuseum-floor1",
                "text": "南馆一层",
                "floor": 1,
                "visible": true,
                "isSelected": false
            },
            {
                "value": "SouthMuseum-floor2",
                "text": "南馆二层",
                "floor": 2,
                "visible": false,
                "isSelected": false
            },
            {
                "value": "SouthMuseum-floor3",
                "text": "南馆三层",
                "floor": 3,
                "visible": false,
                "isSelected": false
            },
            {
                "value": "SouthMuseum-floor4",
                "text": "南馆四层",
                "floor": 4,
                "visible": false,
                "isSelected": false
            }
        ]
    },
    {
        "value": "SvalueeMuseum-exterior",
        "text": "东侧小馆",
        "children": []
    }
]

const confirm = () => {
    const target = {
        coordinates: [lng.value, lat.value],
        zoom: 18,
        buildingId:"WestMuseum-exterior",// buildingInfo.value.building,
        floor:1,// buildingInfo.value.floor,
        alt: alt.value
    }
    console.log(confirm);

    emit('focusPoint', target)
}

const points=[114.359037407	30.56436866	3.58713001
114.35900288	30.564353781	3.58613008
114.358965373	30.56433597	3.58613008
114.358931263	30.564344726	3.58513016
114.35895647	30.564303685	3.57613033
114.358981986	30.564263865	3.57018696
114.359015612	30.564255108	3.57018696
114.358994191	30.564290967	3.5841869
114.359032178	30.564307864	3.59613031
114.35906568	30.564324558	3.58912987
114.359117092	30.564101273	3.66618668
114.359095964	30.564135138	3.37818705
114.359071663	30.564172694	3.86618696
114.359046515	30.564203192	3.15999984];
onMounted(()=>{

});

</script>

<style scoped>
.debug-panel {
    position: fixed;
    top: 100px;
    right: 10px;
    z-index: 999;
}
</style>