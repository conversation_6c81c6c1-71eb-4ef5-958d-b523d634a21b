<template>
  <div class="floor-panel" v-if="shouldShow">
    <!-- 单楼层显示 -->

    <!-- 多楼层滚动选择器 -->
    <div class="scroll-panel" :class="{ isNaving }">
      <div class="explode-floor" :class="{close: isExplodedMode}" @click="toggleExplodedMode"></div>
      <!-- 选中指示器 -->
      <div class="picker-indicator"></div>

      <!-- 滚动容器 -->
      <div class="scroll-container" ref="scrollContainer" @touchstart="onTouchStart" @touchmove="onTouchMove"
        @touchend="onTouchEnd" @wheel.prevent="onWheel">
        <div class="scroll-content" ref="scrollContent"
          :style="{ transform: `translateY(${touchState.currentHeight}px)` }">
          <!-- 上方占位 -->
          <div class="placeholder-top"></div>

          <!-- 楼层选项 -->
          <div class="floor-item" v-for="(item, index) in floorList" :key="item.value"
            :class="{ active: index === activeIndex }" @click="onItemClick(index)">
            {{ item.text }}
          </div>

          <!-- 下方占位 -->
          <div class="placeholder-bottom"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, inject, onUnmounted } from "vue";
import { eventBus } from "@/refactored/EventBus";

interface FloorItem {
  text: string;
  value: number;
}

interface Props {
  // 手动控制模式
  floors?: number[];
  currentFloor?: number;
  disabled?: boolean;

  // LOD集成模式
  autoSync?: boolean; // 是否自动同步LOD状态
  showLODStatus?: boolean; // 是否显示LOD状态信息
  buildingCurrentFloors?: Record<string, number>; // 建筑当前楼层状态
}

interface Emits {
  (e: "change", floor: number): void;
  (e: "update:currentFloor", floor: number): void;
  (e: "lod-floor-change", data: { buildingId: string; floor: number }): void;
  (e: "floor-switch", data: { buildingId: string; floor: number }): void;
  (e: "exploded-mode-toggle", data: { buildingId: string; enabled: boolean }): void;
}

const props = withDefaults(defineProps<Props>(), {
  floors: () => [],
  currentFloor: 1,
  disabled: false,
  autoSync: true,
  showLODStatus: true,
});

const emit = defineEmits<Emits>();

// 尝试注入LOD管理器（如果存在）
const lodManager = inject<any>("lodManager", null);

// 响应式数据
const scrollContainer = ref<HTMLElement>();
const scrollContent = ref<HTMLElement>();
const activeIndex = ref(0);
const isScrolling = ref(false);
const isNaving = ref(false); // 是否正在导航界面（导航页则不允许控制楼层切换）
const vw = document.documentElement.clientWidth / 100
const itemHeight = 7.47 * vw; // 每个楼层项的高度
// LOD状态
const lodBuildingInfo = ref<{
  id: string;
  name: string;
  floor: number;
  distance: number;
} | null>(null);

// 拆楼模式状态
const isExplodedMode = ref(false);

// 节流相关
const throttleTimer = ref<ReturnType<typeof setTimeout> | null>(null);
const THROTTLE_DELAY = 100; // 100ms节流延迟

// LOD事件处理器引用
const lodEventHandlers = ref<Record<string, (...args: any[]) => void> | null>(null);

// 触摸相关
const touchState = ref({
  startY: 0,
  startScrollTop: 0,
  scrollY: 0,
  isDragging: false,
  startTime: 0,
  hasMoved: false,
  clickThreshold: 8,
  clickTimeThreshold: 300,
  sensitivity: 1.5,
  lastY: 0,
  velocity: 0,
})

// 节流函数
const throttle = (func: Function, delay: number) => {
  return (...args: any[]) => {
    if (throttleTimer.value) {
      clearTimeout(throttleTimer.value);
    }
    throttleTimer.value = setTimeout(() => {
      func.apply(null, args);
      throttleTimer.value = null;
    }, delay);
  };
};

// 计算楼层列表
const floorList = computed<FloorItem[]>(() => {
  if (props.autoSync && lodBuildingInfo.value) {
    // LOD模式：从LOD管理器获取楼层
    console.log("🔍 FloorPanel计算楼层列表:", {
      autoSync: props.autoSync,
      lodBuildingInfo: lodBuildingInfo.value,
      hasLODManager: !!lodManager,
      lodManagerType: typeof lodManager,
      lodManagerValue: lodManager?.value,
      hasBuildingFloorsMethod: lodManager?.value && typeof lodManager.value.getBuildingFloors === "function",
    });

    // 修复：lodManager是一个ref，需要访问.value
    if (lodManager?.value && typeof lodManager.value.getBuildingFloors === "function") {
      const floors = lodManager.value.getBuildingFloors(lodBuildingInfo.value.id);
      console.log("🏢 获取建筑楼层:", lodBuildingInfo.value.id, floors);

      const result = floors
        .map((floor: number) => ({
          text: `${floor}楼`,
          value: floor,
        }))
        .sort((a: FloorItem, b: FloorItem) => b.value - a.value);

      console.log("📋 楼层列表计算结果:", result);
      return result;
    } else {
      console.warn("⚠️ LOD管理器或getBuildingFloors方法不可用", {
        hasLODManager: !!lodManager,
        hasLODManagerValue: !!lodManager?.value,
        lodManagerValueType: typeof lodManager?.value,
        hasBuildingFloorsMethod: lodManager?.value && typeof lodManager.value.getBuildingFloors === "function",
      });
      return [];
    }
  } else {
    // 手动模式：使用props中的楼层
    console.log("🔧 手动模式楼层列表:", props.floors);
    return props.floors
      .map((floor) => ({
        text: `${floor}楼`,
        value: floor,
      }))
      .sort((a, b) => a.value - b.value);
  }
});

// 组件显示状态 - 通过eventBus控制
const shouldShow = ref(false);

// 显示组件
const showFloorPanel = () => {
  shouldShow.value = true;
  console.log("🎯 FloorPanel显示");
};

// 隐藏组件
const hideFloorPanel = () => {
  shouldShow.value = false;
  console.log("🙈 FloorPanel隐藏");
};

// 初始化当前楼层索引
const initActiveIndex = () => {
  let targetFloor = props.currentFloor;

  if (props.autoSync && lodBuildingInfo.value) {
    // LOD模式：优先使用LOD建筑信息中的楼层
    targetFloor = lodBuildingInfo.value.floor;

    // 如果有buildingCurrentFloors状态，也考虑进去
    if (props.buildingCurrentFloors && props.buildingCurrentFloors[lodBuildingInfo.value.id] && !isExplodedMode.value) {
      targetFloor = props.buildingCurrentFloors[lodBuildingInfo.value.id]
    }
  }

  const index = floorList.value.findIndex((item) => item.value === targetFloor);
  activeIndex.value = index >= 0 ? index : 0;
  console.log("🎯 初始化楼层索引:", {
    targetFloor,
    index: activeIndex.value,
    buildingId: lodBuildingInfo.value?.id,
    buildingCurrentFloors: props.buildingCurrentFloors,
  });
};

// 滚动到指定索引
const scrollToIndex = (index: number, smooth = true) => {
  if (!scrollContent.value) return;

  touchState.value.currentHeight = -index * itemHeight;

  if (smooth) {
    scrollContent.value.style.transition = "transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)";
    // scrollContent.value.style.transform = `translateY(${targetTranslateY}px)`;

    setTimeout(() => {
      if (scrollContent.value) {
        scrollContent.value.style.transition = "";
      }
    }, 300);
  } else {
    scrollContent.value.style.transition = "";
    // scrollContent.value.style.transform = `translateY(${targetTranslateY}px)`;
  }
};

// 选择楼层
const selectFloor = (index: number) => {
  if (props.disabled) return;
  if (index < 0 || index >= floorList.value.length) return;
  activeIndex.value = index;
  scrollToIndex(index, true);
  emitChange();
};

// 楼层项点击处理
const onItemClick =
  throttle((index: number) => {
    if (props.disabled || isScrolling.value) return;

    if (!touchState.value.hasMoved) {
      selectFloor(index);
    }
  }, 100)


eventBus.on("upFloor", () => {
  onItemClick(activeIndex.value - 1)
})
eventBus.on("downFloor", () => {
  onItemClick(activeIndex.value + 1)
})

// 触发变更事件
const emitChange = () => {
  const selectedFloor = floorList.value[activeIndex.value];
  if (!selectedFloor) return;

  console.log("🔄 楼层变更:", selectedFloor.value);

  if (props.autoSync && lodBuildingInfo.value) {
    // LOD模式：发射多种事件
    const eventData = {
      buildingId: lodBuildingInfo.value.id,
      floor: selectedFloor.value,
    };

    // 1. 发射LOD楼层变更事件（用于LOD管理器处理）
    emit("lod-floor-change", eventData);

    // 2. 发射楼层切换事件（用于页面状态更新）
    emit("floor-switch", eventData);

    // 3. 更新本地状态
    lodBuildingInfo.value.floor = selectedFloor.value;

    console.log("✅ LOD楼层变更事件已发射:", eventData);
  } else {
    // 手动模式：触发普通事件
    emit("change", selectedFloor.value);
    emit("update:currentFloor", selectedFloor.value);
  }
};

// 触摸事件处理（简化版本）
const onTouchStart = (e: TouchEvent) => {
  if (props.disabled) return
  touchState.value.isDragging = true
  touchState.value.startY = e.touches[0].clientY
  touchState.value.scrollY = 0
  touchState.value.hasMoved = false
  isScrolling.value = true
}

const onTouchMove = (e: TouchEvent) => {
  if (!touchState.value.isDragging || props.disabled) return

  const deltaY = e.touches[0].clientY - touchState.value.startY
  if (Math.abs(deltaY) > touchState.value.clickThreshold) {
    touchState.value.hasMoved = true
    e.preventDefault()
    touchState.value.scrollY = -activeIndex.value * itemHeight + deltaY
    scrollContent.value.style.transform = `translateY(${touchState.value.scrollY}px)`
  }
}

const onTouchEnd = () => {
  if (!touchState.value.isDragging) return
  touchState.value.isDragging = false
  isScrolling.value = false
  if (touchState.value.hasMoved) {
    // 计算当前滑动操作 - 处于哪个index
    const newIndex = getLoadedFloorIndex()
    const __y = -newIndex * itemHeight
    scrollContent.value.style.transform = `translateY(${__y}px)`
    if (newIndex !== activeIndex.value) {
      selectFloor(newIndex)
    }
  }
}

const getLoadedFloorIndex = () => {
  const _y = touchState.value.scrollY
  const _floors = floorList.value.map((eop, _index) => ({
    ...eop,
    y: -_index * itemHeight
  }))
  if (_y >= 0) return 0
  if (_y <= _floors[_floors.length - 1].y) return _floors.length - 1
  for (let i = _floors.length - 2; i > 0; i--) {
    if (_y <= _floors[i].y) {
      return i
    }
  }
  // 如果没有切换，返回当前楼层模型
  return activeIndex.value;
};

const onWheel = (e: WheelEvent) => {
  if (props.disabled) return
  e.preventDefault()
  const delta = e.deltaY > 0 ? 1 : -1
  const newIndex = Math.max(0, Math.min(activeIndex.value + delta, floorList.value.length - 1))
  if (newIndex !== activeIndex.value) {
    selectFloor(newIndex);
  }
};

// 拆楼模式切换
const toggleExplodedMode = () => {
  if (!lodBuildingInfo.value) {
    console.warn("⚠️ 没有当前LOD建筑，无法切换拆楼模式");
    return;
  }

  isExplodedMode.value = !isExplodedMode.value;

  console.log(`🏗️ ${isExplodedMode.value ? "进入" : "退出"}拆楼模式:`, lodBuildingInfo.value.id);

  // 发射拆楼模式切换事件
  emit("exploded-mode-toggle", {
    buildingId: lodBuildingInfo.value.id,
    enabled: isExplodedMode.value,
  });
};

// LOD事件监听
const setupLODListeners = () => {
  if (!props.autoSync) return;

  console.log("🔧 设置LOD事件监听器");

  // 监听LOD建筑进入事件
  const onLODBuildingEntered = (data: any) => {
    console.log("🏢 LOD建筑进入事件:", data);

    lodBuildingInfo.value = {
      id: data.buildingId,
      name: data.buildingName || data.name || "未知建筑",
      floor: data.floor || 1,
      distance: data.distance || 0,
    };

    console.log("📍 设置LOD建筑信息:", lodBuildingInfo.value);

    // 显示FloorPanel
    showFloorPanel();

    nextTick(() => {
      console.log("🔄 初始化楼层索引...");

      // 调试：检查LOD管理器状态
      if (lodManager?.value) {
        console.log("🔍 LOD管理器调试信息:", {
          hasGetBuildingFloors: typeof lodManager.value.getBuildingFloors === "function",
          hasGetAllBuildingIds: typeof lodManager.value.getAllBuildingIds === "function",
          allBuildingIds: lodManager.value.getAllBuildingIds ? lodManager.value.getAllBuildingIds() : "N/A",
        });
      }

      initActiveIndex();
      scrollToIndex(activeIndex.value, false);
      console.log("✅ 楼层选择器初始化完成");
    });
  };

  // 监听LOD建筑退出事件
  const onLODBuildingExited = () => {
    console.log("🚪 LOD建筑退出事件");

    // 隐藏FloorPanel
    hideFloorPanel();

    // 清空LOD建筑信息
    lodBuildingInfo.value = null;
  };

  // 监听LOD更新事件（更新距离）- 使用节流
  const throttledLODUpdate = throttle((data: any) => {
    if (lodBuildingInfo.value && data.currentLODBuilding) {
      lodBuildingInfo.value.distance = data.currentLODBuilding.distance || 0;
      console.log("🔄 LOD距离更新 (节流):", data.currentLODBuilding.distance);
    }
  }, THROTTLE_DELAY);

  // 监听楼层变更事件
  const onLODBuildingFloorChanged = (data: any) => {
    if (lodBuildingInfo.value && data.buildingId === lodBuildingInfo.value.id) {
      console.log("🏢 LOD楼层变更事件:", data);
      lodBuildingInfo.value.floor = data.floor;
      initActiveIndex();
      scrollToIndex(activeIndex.value, true);
    }
  };

  // 监听FloorPanel显示/隐藏事件
  const onFloorPanelShow = () => {
    console.log("📡 接收到显示FloorPanel事件");
    showFloorPanel();
  };

  const onFloorPanelHide = () => {
    console.log("📡 接收到隐藏FloorPanel事件");
    hideFloorPanel();
  };

  const onNavigationEnabled = (isEnabled) => {
    console.log("📡 接收到禁止FloorPanel事件");
    isNaving.value = isEnabled;
  };

  // 通过eventBus监听事件
  eventBus.on("lod-building-entered", onLODBuildingEntered);
  eventBus.on("lod-building-exited", onLODBuildingExited);
  eventBus.on("lod-updated", throttledLODUpdate);
  eventBus.on("lod-building-floor-changed", onLODBuildingFloorChanged);

  // 监听FloorPanel控制事件
  eventBus.on("floor-panel-show", onFloorPanelShow);
  eventBus.on("floor-panel-hide", onFloorPanelHide);
  eventBus.on("isNavigation", onNavigationEnabled);

  eventBus.on("selectFloor", (floor) => {
    selectFloor(floor)
  })

  // 保存事件处理器引用，用于清理
  lodEventHandlers.value = {
    onLODBuildingEntered,
    onLODBuildingExited,
    throttledLODUpdate,
    onLODBuildingFloorChanged,
    onFloorPanelShow,
    onFloorPanelHide,
    onNavigationEnabled,
  };
};

// 清理LOD监听器
const cleanupLODListeners = () => {
  if (!props.autoSync || !lodEventHandlers.value) return;

  console.log("🧹 清理LOD事件监听器");

  // 从eventBus移除事件监听器
  eventBus.off("lod-building-entered", lodEventHandlers.value.onLODBuildingEntered);
  eventBus.off("lod-building-exited", lodEventHandlers.value.onLODBuildingExited);
  eventBus.off("lod-updated", lodEventHandlers.value.throttledLODUpdate);
  eventBus.off("lod-building-floor-changed", lodEventHandlers.value.onLODBuildingFloorChanged);
  eventBus.off("floor-panel-show", lodEventHandlers.value.onFloorPanelShow);
  eventBus.off("floor-panel-hide", lodEventHandlers.value.onFloorPanelHide);
  eventBus.off("LODControlEnabled", lodEventHandlers.value.onFloorPanelHide);

  lodEventHandlers.value = null;
};

// 监听props变化
watch(
  () => props.currentFloor,
  () => {
    if (!props.autoSync) {
      initActiveIndex();
      nextTick(() => {
        scrollToIndex(activeIndex.value, false);
      });
    }
  }
);

watch(
  () => props.floors,
  () => {
    if (!props.autoSync) {
      initActiveIndex();
      nextTick(() => {
        scrollToIndex(activeIndex.value, false);
      });
    }
  },
  { deep: true }
);

// 监听buildingCurrentFloors变化
watch(
  () => props.buildingCurrentFloors,
  () => {
    if (props.autoSync && lodBuildingInfo.value) {
      console.log("🔄 buildingCurrentFloors变化，重新初始化楼层索引");
      initActiveIndex();
      nextTick(() => {
        scrollToIndex(activeIndex.value, true);
      });
    }
  },
  { deep: true }
);

// 监听lodBuildingInfo变化 - 修复楼层列表同步问题
watch(
  () => lodBuildingInfo.value,
  (newBuildingInfo, oldBuildingInfo) => {
    if (!props.autoSync) return;

    // 建筑切换时
    if (newBuildingInfo?.id !== oldBuildingInfo?.id) {
      console.log("🏢 建筑切换，更新楼层列表:", {
        from: oldBuildingInfo?.id,
        to: newBuildingInfo?.id,
        newFloors: newBuildingInfo ? lodManager?.value?.getBuildingFloors?.(newBuildingInfo.id) || [] : [],
      });

      // 重新初始化楼层索引
      nextTick(() => {
        initActiveIndex();
        scrollToIndex(activeIndex.value, false);
        console.log("✅ 楼层列表已同步更新");
      });
    }
  },
  { deep: true }
);

// 组件挂载
onMounted(() => {
  console.log("🚀 FloorPanel组件挂载", {
    autoSync: props.autoSync,
    hasLODManager: !!lodManager,
    hasLODManagerValue: !!lodManager?.value,
  });

  if (props.autoSync) {
    setupLODListeners();

    // 检查是否已有LOD建筑
    if (lodManager?.value && typeof lodManager.value.getCurrentLODBuildingInfo === "function") {
      const currentLODInfo = lodManager.value.getCurrentLODBuildingInfo();
      if (currentLODInfo) {
        console.log("📍 发现已存在的LOD建筑:", currentLODInfo);
        lodBuildingInfo.value = currentLODInfo;
      }
    }
  }

  initActiveIndex();
  nextTick(() => {
    scrollToIndex(activeIndex.value, false);
  });
});

// 组件卸载
onUnmounted(() => {
  console.log("🔚 FloorPanel组件卸载");
  cleanupLODListeners();

  // 清理节流定时器
  if (throttleTimer.value) {
    clearTimeout(throttleTimer.value);
  }
});

// 暴露方法
defineExpose({
  selectFloor: (index: number) => selectFloor(index),
  getCurrentFloor: () => floorList.value[activeIndex.value]?.value,
  getActiveIndex: () => activeIndex.value,
  getLODBuildingInfo: () => lodBuildingInfo.value,
});
</script>

<style scoped lang="scss">
.floor-panel {
  position: relative;
  width: 44px;
  height: 127px;

  // 单楼层显示
  .explode-floor {
    width: 44px;
    height: 44px;
    background: url(@/assets/image/floor.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    z-index: 9;

    .floor-text {
      color: #fff;
      font-size: 14px;
      font-weight: bold;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
  }

  .close {
    background: url(@/assets/image/closeFloor.png);
    background-size: 100% 100%;
  }

  // 多楼层滚动选择器
  .scroll-panel {
    position: relative;
    width: 44px;
    height: 127px;
    background: url(@/assets/image/floor-panel.png) no-repeat;
    background-size: 100% 100%;
    overflow: hidden;

    &.isNaving {
      pointer-events: none;
    }

    // 选中指示器
    .picker-indicator {
      position: absolute;
      top: 60%;
      left: 50%;
      right: 0;
      height: 28px;
      width: 28px;
      transform: translate(-50%, -50%);
      // background: rgba(255, 255, 255, 0.1);
      // border: 1px solid rgba(255, 255, 255, 0.3);
      // border-radius: 4px;
      pointer-events: none;
      z-index: 0;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);
      background: url(@/assets/image/indicator.svg) no-repeat center / 100% 100%;
    }

    // 滚动容器
    .scroll-container {
      width: 100%;
      height: 100%;
      overflow: hidden;
      scroll-behavior: smooth;

      // iOS优化
      -webkit-overflow-scrolling: touch;
      touch-action: none;

      // 滚动内容
      .scroll-content {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        // 上下占位，确保选中项能居中
        .placeholder-top,
        .placeholder-bottom {
          height: 63px;
        }

        // 楼层项
        .floor-item {
          flex: 0;
          width: 28px;
          height: 28px;
          line-height: 28px;
          text-align: center;
          color: rgba(255, 255, 255, 0.6);
          font-size: 10px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          user-select: none;
          position: relative;

          // iOS触摸优化
          -webkit-tap-highlight-color: transparent;
          -webkit-touch-callout: none;
          -webkit-user-select: none;

          // 悬停效果
          &:hover {
            color: rgba(255, 255, 255, 0.8);
            transform: scale(1.05);
          }

          // 激活状态
          &.active {
            color: #fff;
            // font-weight: bold;
            font-size: 10px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
            transform: scale(1.1);

            // 发光效果
            // &::before {
            //   content: "";
            //   position: absolute;
            //   top: 50%;
            //   left: 50%;
            //   width: 36px;
            //   height: 36px;
            //   background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            //   border-radius: 50%;
            //   transform: translate(-50%, -50%);
            //   z-index: -1;
            // }
          }

          // 非激活项的渐变效果
          &:not(.active) {
            opacity: 0.7;
          }
        }
      }
    }

    // 渐变遮罩效果
    &::before,
    &::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      height: 21px;
      pointer-events: none;
      z-index: 1;
    }

    // &::before {
    //   top: 0;
    //   background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
    // }

    // &::after {
    //   bottom: 0;
    //   background: linear-gradient(to top, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
    // }
  }

  // LOD状态指示器
  .lod-status {
    position: absolute;
    top: -80px;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 10px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);

    .building-name {
      font-weight: bold;
      margin-bottom: 2px;
    }

    .distance {
      opacity: 0.7;
      margin-bottom: 8px;
    }

    .exploded-mode-toggle {
      margin-top: 8px;

      .exploded-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        color: white;
        font-size: 11px;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
        justify-content: center;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
        }

        &.active {
          background: #ff6b35;
          border-color: #ff6b35;
          color: white;

          &:hover {
            background: #e55a2b;
            border-color: #e55a2b;
          }
        }

        svg {
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
