/**
 * Web室内惯导定位库 - 主入口
 * 适配Web浏览器环境的室内惯导定位解决方案
 * 
 * 提供简洁易用的PDR室内定位能力
 * 输入：初始化坐标
 * 输出：实时位置坐标
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

import WebSensorManager from './WebSensorManager.js';
import PositionCalculator from './PositionCalculator.js';
import QualityController from './QualityController.js';
import { TrajectorySmoothing } from './TrajectorySmoothing.js';

/**
 * Web室内惯导定位核心类
 */
export default class InertialNavigationWeb {
  
  /**
   * 构造函数
   * @param {Object} config - 配置参数
   * @param {Object} config.initialPosition - 初始位置 {x, y, z}
   * @param {number} config.sampleRate - 传感器采样率 (Hz)，默认50
   * @param {string} config.mode - 运行模式：'standard'(标准) | 'lite'(轻量) | 'precise'(精确)
   * @param {boolean} config.enableMLA - 是否启用MLA校正，默认true
   * @param {Object} config.calibration - 校准参数
   */
  constructor(config = {}) {
    // 验证配置参数
    this.validateConfig(config);
    
    // 核心配置
    this.config = {
      // 初始位置（必需）
      initialPosition: config.initialPosition,
      
      // 传感器配置
      sampleRate: config.sampleRate || 50,
      
      // 运行模式
      mode: config.mode || 'standard',
      
      // 功能开关
      enableMLA: config.enableMLA !== false,
      enableStepDetection: config.enableStepDetection !== false,
      enableHeadingCorrection: config.enableHeadingCorrection !== false,
      
      // 校准参数
      calibration: {
        stepLength: config.calibration?.stepLength || 0.75,
        magneticDeclination: config.calibration?.magneticDeclination || 0,
        accelerometerBias: config.calibration?.accelerometerBias || [0, 0, 0],
        gyroscopeBias: config.calibration?.gyroscopeBias || [0, 0, 0],
        ...config.calibration
      },
      
      // 融合参数
      fusion: {
        pdrWeight: 0.7,
        mlaWeight: 0.3,
        adaptiveWeighting: true,
        confidenceThreshold: 0.4,
        smoothingFactor: 0.4,
        ...config.fusion
      },
      
      // 质量控制
      quality: {
        outlierThreshold: 3.0,
        minConfidence: 0.2,
        maxDeviation: 5.0,
        ...config.quality
      }
    };

    // 根据模式调整参数
    this.adjustConfigByMode();
    
    // 初始化核心组件
    this.sensorManager = new WebSensorManager(this.config);
    this.positionCalculator = new PositionCalculator(this.config);
    this.qualityController = new QualityController(this.config);

    // 初始化轨迹平滑器
    this.trajectorySmoothing = new TrajectorySmoothing({
      positionSmoothing: 0.2,
      headingSmoothing: 0.15,
      velocitySmoothing: 0.3,
      interpolationSteps: 3,
      adaptiveSmoothing: true
    });
    
    // 系统状态
    this.state = {
      isRunning: false,
      isPaused: false,
      isCalibrated: false,
      isWarming: false,        // 预热状态
      warmupStartTime: 0,      // 预热开始时间
      initialPositionSet: false, // 初始位置是否已设置
      lastUpdate: 0,
      operatingMode: this.config.mode
    };
    
    // 当前定位结果
    this.currentLocation = {
      position: { ...this.config.initialPosition },
      heading: 0,
      velocity: 0,
      stepCount: 0,
      confidence: 0,
      timestamp: Date.now(),
      quality: 'unknown'
    };

    // 方向校准
    this.directionCalibration = {
      offset: 0,           // 方向偏移角度
      isCalibrated: false, // 是否已校准
      sceneNorth: 0        // 场景北向角度
    };
    
    // 历史轨迹
    this.trajectory = [];
    this.maxTrajectoryLength = 1000;
    
    // 性能统计
    this.statistics = {
      totalSteps: 0,
      totalDistance: 0,
      averageConfidence: 0,
      processingTime: [],
      correctionCount: 0,
      startTime: null
    };
    
    // 事件回调函数
    this.onPositionUpdate = null;
    this.onStepDetected = null;
    this.onError = null;
    this.onCalibrationRequired = null;
    
    console.log('🧭 Web惯导定位库初始化完成', {
      mode: this.config.mode,
      initialPosition: this.config.initialPosition,
      enableMLA: this.config.enableMLA
    });
  }
  
  /**
   * 验证配置参数
   * @param {Object} config 
   */
  validateConfig(config) {
    // 检查配置对象是否存在
    if (!config || typeof config !== 'object') {
      throw new Error('配置参数必须是一个对象');
    }
    
    // 检查初始位置
    if (!config.initialPosition) {
      throw new Error('必须提供初始位置坐标 (initialPosition)');
    }
    
    const { x, y, z } = config.initialPosition;
    
    // 检查坐标是否为数字类型
    if (typeof x !== 'number' || typeof y !== 'number' || typeof z !== 'number') {
      throw new Error('初始位置坐标必须为数字类型');
    }
    
    // 检查坐标是否为有效数字（非NaN和有限数）
    if (isNaN(x) || isNaN(y) || isNaN(z) || 
        !isFinite(x) || !isFinite(y) || !isFinite(z)) {
      throw new Error('初始位置坐标必须是有效的数字');
    }
    
    // 检查采样率范围
    if (config.sampleRate && (config.sampleRate < 10 || config.sampleRate > 100)) {
      console.warn('⚠️ 建议采样率设置在10-100Hz之间');
    }
    
    // 检查运行模式是否有效
    if (config.mode && !['lite', 'standard', 'precise'].includes(config.mode)) {
      throw new Error('运行模式必须是 lite、standard 或 precise');
    }
  }
  
  /**
   * 根据运行模式调整配置参数（优化版）
   */
  adjustConfigByMode() {
    const mode = this.config.mode;
    
    // 通用优化参数
    const optimizedDefaults = {
      // 步态检测参数（降低阈值，提高敏感度）
      stepDetection: {
        accPeakMagnitudeLB: 1.2,          // 降低步态检测阈值，提高敏感度
        slideWindowLen: 6,                // 减小滑动窗口，更快响应
        accPeakPeriodLB: 200,             // 减小最小步态间隔
        accPeakPeriodUB: 1500,            // 最大步态间隔保持不变
        confirmationThreshold: 0.4         // 降低确认阈值
      },
      
      // 步长估计参数（更保守）
      stepLength: {
        kValue: 0.42,                     // 降低Weinberg K值
        personalFactor: 1.0,
        defaultLength: 0.65,              // 更保守的默认步长
        adaptiveEnabled: true
      },
      
      // 航向估计参数（增强平滑）
      headingEstimation: {
        headingSmoothing: 0.7,            // 增强航向平滑
        compassWeight: 0.25,              // 降低罗盘权重
        useCompass: true,
        useGyroscope: true
      },
      
      // 滤波参数（增强滤波）
      filterWindow: 8,                    // 增大滤波窗口
      deadZone: 0.08,                    // 死区滤波阈值
      noiseThreshold: 0.12,              // 噪声抑制阈值
      
      // 位置校正参数
      correction: {
        enabled: true,
        minMovementThreshold: 0.2,        // 最小移动阈值
        staticPositionLock: true,
        staticThreshold: 0.08,
        driftCorrection: true,
        driftThreshold: 1.0,
        maxDriftCorrection: 0.4
      }
    };
    
    // 将优化参数合并到配置中
    this.config = { ...this.config, ...optimizedDefaults };
    
    switch (mode) {
      case 'lite':
        // 轻量模式：低功耗，进一步减少计算量
        this.config.sampleRate = Math.min(this.config.sampleRate, 25);
        this.config.enableMLA = false;
        this.config.fusion.smoothingFactor = 0.8;
        this.config.quality.minConfidence = 0.15;
        
        // 轻量模式特殊设置
        this.config.stepDetection.slideWindowLen = 6;
        this.config.filterWindow = 6;
        this.config.headingEstimation.headingSmoothing = 0.8;
        this.config.correction.minMovementThreshold = 0.25;
        break;
        
      case 'precise':
        // 精确模式：最高精度设置
        this.config.sampleRate = Math.max(this.config.sampleRate, 60);
        this.config.enableMLA = true;
        this.config.fusion.smoothingFactor = 0.3;
        this.config.quality.minConfidence = 0.4;
        this.config.fusion.adaptiveWeighting = true;
        
        // 精确模式特殊设置
        this.config.stepDetection.accPeakMagnitudeLB = 2.0;    // 更严格的步态检测
        this.config.stepDetection.confirmationThreshold = 0.7;
        this.config.stepLength.kValue = 0.38;                 // 更保守的步长估计
        this.config.correction.minMovementThreshold = 0.15;   // 更敏感的移动检测
        this.config.filterWindow = 10;                        // 更强的滤波
        break;
        
      case 'standard':
      default:
        // 标准模式：使用优化后的默认参数
        this.config.quality.minConfidence = 0.25;
        break;
    }
    
    console.log(`⚙️ 配置已调整为${mode}模式，优化参数已应用`);
  }
  
  /**
   * 启动惯导定位
   * @param {Object} options - 启动选项
   * @returns {Promise<boolean>} 启动是否成功
   */
  async start(options = {}) {
    if (this.state.isRunning) {
      console.warn('⚠️ 惯导定位已在运行中');
      return true;
    }
    
    try {
      console.log('🚀 启动Web惯导定位系统...');
      
      // 合并启动选项
      const startConfig = { ...this.config, ...options };
      
      // 启动传感器管理器
      await this.sensorManager.start(startConfig);

      // 初始化位置计算器
      this.positionCalculator.initialize(startConfig.initialPosition);
      
      // 初始化质量控制器
      this.qualityController.initialize();
      
      // 设置数据处理回调
      this.sensorManager.setDataCallback((sensorData) => {
        this.processSensorData(sensorData);
      });
      
      // 设置错误回调
      this.sensorManager.setErrorCallback((error) => {
        this.handleError('sensor_error', error);
      });
      
      // 更新状态
      this.state.isRunning = true;
      this.state.isPaused = false;
      this.state.isWarming = true;
      this.state.warmupStartTime = Date.now();
      this.statistics.startTime = Date.now();

      // 立即设置初始位置，解决初始化延迟问题
      this.setInitialPosition();

      // 启动预热过程
      this.startWarmupProcess();

      console.log('✅ Web惯导定位系统启动成功');
      return true;
      
    } catch (error) {
      console.error('❌ 惯导定位启动失败:', error);
      this.handleError('start_failed', error);
      return false;
    }
  }

  /**
   * 设置初始位置 - 解决初始化延迟问题
   */
  setInitialPosition() {
    if (this.state.initialPositionSet) {
      return;
    }

    // 立即提供初始位置
    const initialPosition = {
      position: { ...this.config.initialPosition },
      heading: 0, // 初始航向角，后续会更新
      velocity: 0,
      stepCount: 0,
      confidence: 0.5, // 给予中等置信度
      timestamp: Date.now(),
      quality: 'initializing',
      source: 'initial'
    };

    this.updateCurrentLocation(initialPosition);
    this.state.initialPositionSet = true;

    // 立即触发位置更新回调
    if (this.onPositionUpdate) {
      console.log('🔍 触发初始位置更新回调');
      this.onPositionUpdate(this.getCurrentLocation());
    }

    console.log('📍 初始位置已设置:', initialPosition.position);
  }

  /**
   * 启动预热过程 - 等待获取真实的初始航向角
   */
  startWarmupProcess() {
    console.log('🔥 开始传感器预热，等待获取真实初始航向角...');

    // 等待获取稳定的初始航向角
    this.waitForInitialHeading();
  }

  /**
   * 等待获取稳定的初始航向角
   */
  async waitForInitialHeading() {
    const maxWaitTime = 3000; // 最大等待3秒
    const checkInterval = 100; // 每100ms检查一次
    let waitTime = 0;

    const checkHeading = () => {
      // 获取当前传感器数据
      const magnetometerData = this.sensorManager.sensorStatus.magnetometer.lastData;

      if (magnetometerData && magnetometerData.direction !== undefined) {
        // 获取到有效的航向角数据
        const initialHeading = magnetometerData.direction;
        console.log('✅ 获取到初始航向角:', initialHeading.toFixed(1) + '°');

        // 设置初始航向角到惯导系统
        this.setInitialHeading(initialHeading);

        // 完成预热
        this.state.isWarming = false;
        console.log('✅ 传感器预热完成，初始航向角已设置');

        // 适度降低置信度阈值
        this.config.quality.minConfidence = Math.min(0.25, this.config.quality.minConfidence);

        return;
      }

      waitTime += checkInterval;
      if (waitTime < maxWaitTime) {
        // 继续等待
        setTimeout(checkHeading, checkInterval);
      } else {
        // 超时，使用默认值
        console.warn('⚠️ 等待初始航向角超时，使用默认值0°');
        this.setInitialHeading(0);
        this.state.isWarming = false;
        this.config.quality.minConfidence = Math.min(0.25, this.config.quality.minConfidence);
      }
    };

    // 开始检查
    setTimeout(checkHeading, checkInterval);
  }

  /**
   * 设置初始航向角
   */
  setInitialHeading(heading) {
    console.log('🧭 设置初始航向角:', heading.toFixed(1) + '°');

    // 设置到MotionAnalyzer的航向估计器
    if (this.positionCalculator && this.positionCalculator.motionAnalyzer) {
      const headingEstimator = this.positionCalculator.motionAnalyzer.headingEstimator;
      if (headingEstimator) {
        // 强制设置初始航向角
        headingEstimator.state.heading = heading;
        headingEstimator.state.initialized = true;
        headingEstimator.state.lastUpdate = Date.now();

        console.log('✅ 初始航向角已设置到航向估计器');
      }
    }
  }



  /**
   * 停止惯导定位
   */
  stop() {
    if (!this.state.isRunning) {
      return;
    }
    
    console.log('⏹️ 停止Web惯导定位系统');
    
    // 停止传感器采集
    this.sensorManager.stop();
    
    // 停止质量控制器
    this.qualityController.destroy();
    
    // 重置状态
    this.state.isRunning = false;
    this.state.isPaused = false;
    
    // 输出最终统计
    this.logFinalStatistics();
  }

  /**
   * 校准方向 - 解决与场景罗盘方向的角度误差
   * @param {number} sceneNorthDirection - 场景中的北向角度（度）
   */
  calibrateDirection(sceneNorthDirection) {
    const currentHeading = this.getCurrentLocation().heading;
    this.directionCalibration.offset = sceneNorthDirection - currentHeading;
    this.directionCalibration.isCalibrated = true;
    this.directionCalibration.sceneNorth = sceneNorthDirection;

    console.log(`🧭 方向校准完成: 偏移角度 ${this.directionCalibration.offset.toFixed(1)}°`);
    console.log(`   当前航向: ${currentHeading.toFixed(1)}°`);
    console.log(`   场景北向: ${sceneNorthDirection.toFixed(1)}°`);
  }

  /**
   * 获取场景坐标系下的航向角
   * @returns {number} 校准后的航向角
   */
  getSceneHeading() {
    const rawHeading = this.getCurrentLocation().heading;
    if (this.directionCalibration.isCalibrated) {
      const calibratedHeading = (rawHeading + this.directionCalibration.offset + 360) % 360;
      return calibratedHeading;
    }
    return rawHeading;
  }

  /**
   * 重置方向校准
   */
  resetDirectionCalibration() {
    this.directionCalibration = {
      offset: 0,
      isCalibrated: false,
      sceneNorth: 0
    };
    console.log('🔄 方向校准已重置');
  }

  /**
   * 暂停/恢复定位
   * @param {boolean} pause - true暂停，false恢复
   */
  pause(pause = true) {
    if (!this.state.isRunning) {
      console.warn('⚠️ 系统未运行，无法暂停');
      return;
    }
    
    this.state.isPaused = pause;
    
    if (pause) {
      console.log('⏸️ 暂停Web惯导定位');
      this.sensorManager.pause();
    } else {
      console.log('▶️ 恢复Web惯导定位');
      this.sensorManager.resume();
    }
  }
  
  /**
   * 重置到指定位置
   * @param {Object} position - 新的位置坐标 {x, y, z}
   */
  reset(position = null) {
    console.log('🔄 重置Web惯导定位系统');
    
    // 重置位置
    const newPosition = position || this.config.initialPosition;
    this.currentLocation.position = { ...newPosition };
    this.currentLocation.heading = 0;
    this.currentLocation.velocity = 0;
    this.currentLocation.stepCount = 0;
    this.currentLocation.timestamp = Date.now();
    
    // 清空轨迹
    this.trajectory = [];
    
    // 重置统计
    this.statistics.totalSteps = 0;
    this.statistics.totalDistance = 0;
    this.statistics.correctionCount = 0;
    this.statistics.processingTime = [];
    
    // 重置计算器
    this.positionCalculator.reset(newPosition);
    
    console.log('✅ 重置完成，新位置:', newPosition);
  }
  
  /**
   * 处理传感器数据的核心方法
   * @param {Object} sensorData - 传感器数据
   */
  processSensorData(sensorData) {
    if (this.state.isPaused || !this.state.isRunning) {
      return;
    }
    
    const startTime = Date.now();
    
    try {
      // 数据质量检查 - 预热期间使用更宽松的条件
      const isWarmingUp = this.state.isWarming;
      const qualityResult = this.qualityController.checkDataQuality(sensorData, {
        relaxedMode: isWarmingUp // 预热期间放宽质量要求
      });

      if (!qualityResult.isValid && !isWarmingUp) {
        console.warn('⚠️ 传感器数据质量不佳:', qualityResult.reason);
        return;
      }
      
      // 位置计算
      const positionResult = this.positionCalculator.update(sensorData);
      
      if (positionResult && positionResult.success) {
        console.log('🔍 位置计算成功，原始数据:', positionResult.position);

        // 临时禁用轨迹平滑，直接使用原始数据
        this.updateCurrentLocation(positionResult);

        // 记录轨迹
        this.recordTrajectory();

        // 更新统计信息
        this.updateStatistics(positionResult, Date.now() - startTime);

        // 触发位置更新回调
        if (this.onPositionUpdate) {
          console.log('🔍 触发位置更新回调');
          this.onPositionUpdate(this.getCurrentLocation());
        }
      } else {
        console.log('🔍 位置计算失败或无结果:', positionResult);
      }
      
    } catch (error) {
      console.error('❌ 传感器数据处理失败:', error);
      this.handleError('processing_failed', error);
    }
  }
  
  /**
   * 更新当前位置信息
   * @param {Object} positionResult - 位置计算结果
   */
  updateCurrentLocation(positionResult) {
    this.currentLocation = {
      position: positionResult.position,
      heading: positionResult.position.heading || 0,
      velocity: positionResult.velocity || 0,
      stepCount: this.statistics.totalSteps,
      confidence: positionResult.confidence,
      timestamp: Date.now(),
      quality: this.qualityController.getQualityLevel()
    };
    
    this.state.lastUpdate = Date.now();
  }
  
  /**
   * 记录轨迹点
   */
  recordTrajectory() {
    const trajectoryPoint = {
      ...this.currentLocation.position,
      heading: this.currentLocation.heading,
      timestamp: this.currentLocation.timestamp,
      confidence: this.currentLocation.confidence
    };
    
    this.trajectory.push(trajectoryPoint);
    
    // 限制轨迹长度
    if (this.trajectory.length > this.maxTrajectoryLength) {
      this.trajectory.shift();
    }
  }
  
  /**
   * 更新性能统计
   * @param {Object} positionResult - 位置结果
   * @param {number} processingTime - 处理时间(ms)
   */
  updateStatistics(positionResult, processingTime) {
    this.statistics.processingTime.push(processingTime);
    
    // 保持处理时间数组大小
    if (this.statistics.processingTime.length > 100) {
      this.statistics.processingTime.shift();
    }
    
    // 更新步数
    if (positionResult.stepDetected) {
      this.statistics.totalSteps++;
      
      // 步数检测回调
      if (this.onStepDetected) {
        this.onStepDetected({
          stepCount: this.statistics.totalSteps,
          stepLength: positionResult.stepLength,
          timestamp: Date.now()
        });
      }
    }
    
    // 更新距离
    this.statistics.totalDistance = this.calculateTotalDistance();
    
    // 更新平均置信度
    this.statistics.averageConfidence = this.calculateAverageConfidence();
    
    // 更新校正次数
    if (positionResult.corrected) {
      this.statistics.correctionCount++;
    }
  }
  
  /**
   * 计算总距离
   * @returns {number} 总距离(米)
   */
  calculateTotalDistance() {
    if (this.trajectory.length < 2) return 0;
    
    let totalDistance = 0;
    for (let i = 1; i < this.trajectory.length; i++) {
      const prev = this.trajectory[i - 1];
      const curr = this.trajectory[i];
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + 
        Math.pow(curr.y - prev.y, 2) + 
        Math.pow(curr.z - prev.z, 2)
      );
      totalDistance += distance;
    }
    return totalDistance;
  }
  
  /**
   * 计算平均置信度
   * @returns {number} 平均置信度
   */
  calculateAverageConfidence() {
    if (this.trajectory.length === 0) return 0;
    
    const recentTrajectory = this.trajectory.slice(-50); // 最近50个点
    const totalConfidence = recentTrajectory.reduce((sum, point) => sum + (point.confidence || 0), 0);
    return totalConfidence / recentTrajectory.length;
  }
  
  /**
   * 错误处理
   * @param {string} type - 错误类型
   * @param {Error} error - 错误对象
   */
  handleError(type, error) {
    const errorInfo = {
      type,
      message: error.message || error.toString(),
      timestamp: Date.now(),
      state: { ...this.state }
    };
    
    if (this.onError) {
      this.onError(errorInfo);
    }
    
    // 根据错误类型处理
    switch (type) {
      case 'sensor_error':
        // 传感器错误，尝试恢复
        this.recoverFromSensorError();
        break;
      case 'calibration_required':
        // 需要校准
        if (this.onCalibrationRequired) {
          this.onCalibrationRequired();
        }
        break;
      default:
        console.error('未处理的错误类型:', type, error);
    }
  }
  
  /**
   * 从传感器错误中恢复
   */
  recoverFromSensorError() {
    console.log('🔧 尝试从传感器错误中恢复...');
    
    // 重启传感器管理器
    setTimeout(async () => {
      if (this.state.isRunning) {
        try {
          await this.sensorManager.start();
        } catch (error) {
          console.error('❌ 传感器恢复失败:', error);
        }
      }
    }, 1000);
  }
  
  /**
   * 输出最终统计信息
   */
  logFinalStatistics() {
    const runtime = this.statistics.startTime ? Date.now() - this.statistics.startTime : 0;
    const avgProcessingTime = this.statistics.processingTime.length > 0 
      ? this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length 
      : 0;
    
    console.log('📊 Web惯导定位会话统计:', {
      运行时间: `${(runtime / 1000).toFixed(1)}秒`,
      总步数: this.statistics.totalSteps,
      总距离: `${this.statistics.totalDistance.toFixed(2)}米`,
      平均置信度: `${(this.statistics.averageConfidence * 100).toFixed(1)}%`,
      校正次数: this.statistics.correctionCount,
      平均处理时间: `${avgProcessingTime.toFixed(1)}ms`
    });
  }
  
  // ==================== 公共API方法 ====================
  
  /**
   * 获取当前位置
   * @returns {Object} 当前位置信息
   */
  getCurrentLocation() {
    return {
      ...this.currentLocation,
      // 添加额外的状态信息
      isMoving: this.currentLocation.velocity > 0.1,
      qualityLevel: this.qualityController.getQualityLevel(),
      systemStatus: this.getSystemStatus()
    };
  }
  
  /**
   * 获取历史轨迹
   * @param {number} limit - 返回的轨迹点数量限制
   * @returns {Array} 轨迹点数组
   */
  getTrajectory(limit = null) {
    if (limit && limit > 0) {
      return this.trajectory.slice(-limit);
    }
    return [...this.trajectory];
  }
  
  /**
   * 获取性能统计
   * @returns {Object} 性能统计信息
   */
  getStatistics() {
    const runtime = this.statistics.startTime ? Date.now() - this.statistics.startTime : 0;
    const avgProcessingTime = this.statistics.processingTime.length > 0 
      ? this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length 
      : 0;
    
    return {
      runtime: runtime,
      totalSteps: this.statistics.totalSteps,
      totalDistance: this.statistics.totalDistance,
      averageConfidence: this.statistics.averageConfidence,
      correctionCount: this.statistics.correctionCount,
      averageProcessingTime: avgProcessingTime,
      trajectoryLength: this.trajectory.length,
      isRealtime: this.state.isRunning && !this.state.isPaused
    };
  }
  
  /**
   * 获取系统状态
   * @returns {Object} 系统状态信息
   */
  getSystemStatus() {
    return {
      isRunning: this.state.isRunning,
      isPaused: this.state.isPaused,
      isCalibrated: this.state.isCalibrated,
      operatingMode: this.state.operatingMode,
      lastUpdate: this.state.lastUpdate,
      sensorStatus: this.sensorManager.getStatus(),
      qualityLevel: this.qualityController.getQualityLevel()
    };
  }
  
  /**
   * 更新配置参数
   * @param {Object} config - 新的配置参数
   */
  updateConfig(config) {
    // 合并配置
    this.config = { ...this.config, ...config };
    
    // 根据模式重新调整
    if (config.mode) {
      this.adjustConfigByMode();
    }
    
    // 更新子模块配置
    this.sensorManager.updateConfig(this.config);
    this.positionCalculator.updateConfig(this.config);
    this.qualityController.updateConfig(this.config);
    
    console.log('⚙️ 配置已更新:', config);
  }
  
  /**
   * 校准传感器
   * @param {Object} calibrationData - 校准数据
   * @returns {Promise<boolean>} 校准是否成功
   */
  async calibrate(calibrationData = {}) {
    console.log('🔧 开始传感器校准...');

    try {
      // 更新校准配置
      if (calibrationData.stepLength) {
        this.config.calibration.stepLength = calibrationData.stepLength;
      }

      if (calibrationData.magneticDeclination !== undefined) {
        this.config.calibration.magneticDeclination = calibrationData.magneticDeclination;
      }



      // 更新传感器管理器校准参数
      this.sensorManager.updateConfig({ calibration: this.config.calibration });

      // 更新位置计算器校准参数
      this.positionCalculator.updateConfig({ calibration: this.config.calibration });

      this.state.isCalibrated = true;
      console.log('✅ 传感器校准成功');
      return true;
      
    } catch (error) {
      console.error('❌ 传感器校准异常:', error);
      this.handleError('calibration_failed', error);
      return false;
    }
  }
  
  /**
   * 设置MLA校正节点
   * @param {Array} nodes - MLA节点数组
   */
  setMlaNodes(nodes) {
    this.positionCalculator.setMlaNodes(nodes);
    console.log('📍 已设置MLA节点:', nodes.length, '个');
  }

  /**
   * 直接应用MLA位置校正 - 立即更新位置，不依赖传感器更新频率
   * @param {Object} mlaData - MLA校正数据
   */
  applyImmediateMlaCorrection(mlaData) {
    if (!this.state.isRunning) {
      console.warn('⚠️ 惯导系统未运行，无法应用MLA校正');
      return false;
    }

    try {
      // 构建MLA校正位置
      const correctedPosition = {
        x: mlaData.position?.x || mlaData.x,
        y: mlaData.position?.y || mlaData.y,
        z: mlaData.position?.z || mlaData.z || this.currentLocation.position?.z || 0,
        heading: this.currentLocation.heading || 0, // 保持当前航向
        velocity: 0, // MLA校正时设为静止
        confidence: Math.max(0.8, mlaData.confidence || 0.8),
        timestamp: Date.now(),
        stepDetected: false,
        corrected: true,
        correctionDistance: this.calculateCorrectionDistance(mlaData),
        source: 'mla_immediate',
        mlaNodeId: mlaData.id || mlaData.beaconId,
        buildingId: mlaData.buildingId,
        floor: mlaData.floor
      };

      // 直接更新PositionCalculator的当前位置
      this.positionCalculator.position = { ...correctedPosition };

      // 添加到位置历史
      this.positionCalculator.positionHistory.push({ ...correctedPosition });
      if (this.positionCalculator.positionHistory.length > this.positionCalculator.maxHistoryLength) {
        this.positionCalculator.positionHistory.shift();
      }

      // 更新当前位置信息
      this.currentLocation = {
        position: correctedPosition,
        heading: correctedPosition.heading,
        velocity: correctedPosition.velocity,
        stepCount: this.statistics.totalSteps,
        confidence: correctedPosition.confidence,
        timestamp: correctedPosition.timestamp,
        quality: this.qualityController.getQualityLevel(),
        buildingId: correctedPosition.buildingId,
        floor: correctedPosition.floor
      };

      // 记录轨迹
      this.recordTrajectory();

      // 立即触发位置更新回调
      if (this.onPositionUpdate) {
        console.log('🎯 MLA立即校正 - 触发位置更新回调');
        window.writeLog && window.writeLog(`<span style='color:green;'>🎯 MLA立即校正触发回调</span>: x=${correctedPosition.x.toFixed(4)}, y=${correctedPosition.y.toFixed(4)}`);
        this.onPositionUpdate(this.getCurrentLocation());
      }

      console.log(`✅ MLA立即校正成功: x=${correctedPosition.x.toFixed(4)}, y=${correctedPosition.y.toFixed(4)}`);
      window.writeLog && window.writeLog(`<span style='color:green;'>✅ MLA立即校正成功</span>: x=${correctedPosition.x.toFixed(4)}, y=${correctedPosition.y.toFixed(4)}`);
      return true;

    } catch (error) {
      console.error('❌ MLA立即校正失败:', error);
      return false;
    }
  }

  /**
   * 计算校正距离
   * @param {Object} mlaData - MLA数据
   * @returns {number} 校正距离
   */
  calculateCorrectionDistance(mlaData) {
    if (!this.currentLocation.position) return 0;

    const currentPos = this.currentLocation.position;
    const mlaPos = mlaData.position || mlaData;

    const deltaX = (mlaPos.x || mlaData.x) - currentPos.x;
    const deltaY = (mlaPos.y || mlaData.y) - currentPos.y;

    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }
  
  /**
   * 导出数据
   * @param {string} format - 导出格式: 'json' | 'csv'
   * @returns {string} 导出的数据
   */
  exportData(format = 'json') {
    const exportData = {
      metadata: {
        version: '1.0.0',
        exportTime: new Date().toISOString(),
        config: this.config,
        statistics: this.getStatistics()
      },
      trajectory: this.trajectory,
      currentLocation: this.currentLocation
    };
    
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(exportData, null, 2);
      
      case 'csv':
        return this.convertToCSV(this.trajectory);
      
      default:
        throw new Error('不支持的导出格式: ' + format);
    }
  }
  
  /**
   * 将轨迹转换为CSV格式
   * @param {Array} trajectory - 轨迹数据
   * @returns {string} CSV格式字符串
   */
  convertToCSV(trajectory) {
    const headers = ['timestamp', 'x', 'y', 'z', 'heading', 'confidence'];
    const csvLines = [headers.join(',')];
    
    trajectory.forEach(point => {
      const row = [
        point.timestamp,
        point.x.toFixed(6),
        point.y.toFixed(6),
        point.z.toFixed(6),
        (point.heading || 0).toFixed(2),
        (point.confidence || 0).toFixed(3)
      ];
      csvLines.push(row.join(','));
    });
    
    return csvLines.join('\n');
  }
}

// 导出库版本信息
export const version = '1.0.0';
export const description = 'Web室内惯导定位库';