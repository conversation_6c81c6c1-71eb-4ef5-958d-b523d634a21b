/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> 
 * @des: 拆楼 效果 / 动画
 * @Date: 2025-08-25 16:26:18 
 * @Last Modified by: z<PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2025-08-25 16:48:52
 */
// import mapboxgl from "mapbox-gl"
import * as THREE from 'three'
import gsap from 'gsap'
import { eventBus } from '@/refactored/EventBus'
// 模型基础配置
import { SceneConfig } from '@/refactored/SceneConfig'
import { AccurateCoordinateConverter } from '@/refactored/MapboxCoordinateConverter'

/**
 * 建筑LOD模型数据
 */
interface BuildingLODModel {
	id: string
	name: string
	position: THREE.Vector3
	object: THREE.Object3D
	isVisible: boolean
	distanceToCamera: number
	// 建筑相关属性
	isExterior?: boolean
	isInterior?: boolean
	buildingId?: string
	floor?: number
	isDefault?: boolean
	hasInterior?: boolean
	switchDistance?: number
	fadeDistance?: number
  labelEl?: HTMLDivElement
}

// 东\西\南\北 管 对应的 pitch - 默认值 40
const basePitch = 50
// export const pitchMap: any = {
//   'EastMuseum-exterior': 45,
//   'WestMuseum-exterior': 45,
//   'SouthMuseum-exterior': 45,
//   'NorthMuseum-exterior': 45
// }
const win = (window as any)
let isDragging = false; // 拖拽状态
let isPinching = false; // 双指缩放状态
let dragStart = { x: 0, y: 0 }; // 拖拽初始值
let startBearing = 0; // 当前相机的 bearing
// 均在展示的室内模型
let models: BuildingLODModel[] = []
// 当前激活的 展示楼栋
let currentFloor = -1;
const _obj = {
  bearing: startBearing
}
// 记录开始滑动之前的相机y
let cameraY = 0;
// 即将要到达的值
let nextAnimationFlag = false;
let nextCameraY = 0;
// 定义判断轻扫的阈值
const FLICK_DISTANCE_THRESHOLD = 50; // 鼠标移动的最小距离 // 只算y轴挪动的距离
const FLICK_TIME_THRESHOLD = 200; // 鼠标松开的最大时间，单位毫秒
let dragStartTime = 0; // 记录拖动开始时间
let totalDeltaX = 0;
let totalDeltaY = 0;
// 相机基础 - 移动值
const _baseHeight = 100
// 基础楼层底部高度
let _baseY = 0;
// 双指缩放变量
let initialPinchDistance = 0;
// 双指触摸的初始y坐标
let initialTouchY0 = 0;
let initialTouchY1 = 0;
// requestAnimationFrame
let requestAnimationFrameTime: number | null = null;
// 当前模式下面的最大切换zoom
const currentMinZoom = 15
let lastMinZoom = 0


// 将模型相对坐标转换为 地理坐标实现方式
// 将模型相对坐标转换为屏幕坐标
// 将屏幕坐标借用mapbox中的函数转换为 地理坐标
// fbx的模型自带经纬度 - 使用Mesh的坐标去计算 Group的坐标是原点坐标
export const threePositionToLatLon = (model: THREE.Object3D) => {
  const _map = win.mapApp.map;
  const _camera = win.mapApp.camera;
  // 获取模型相对坐标 转换为屏幕像素坐标
  const modelWorldPosition = new THREE.Vector3();
  model.getWorldPosition(modelWorldPosition);
  // 将 3D 坐标转换到地图平面上的 2D 像素
  // 这是一个简化版本，真正的转换需要更复杂的数学运算，但Mapbox本身提供了更易用的方法
  const projectedPoint = new THREE.Vector3();
  projectedPoint.setFromMatrixPosition(model.matrixWorld);
  projectedPoint.project(_camera);
  const screenX = (projectedPoint.x * 0.5 + 0.5) * _map.getCanvas().clientWidth;
  const screenY = (-projectedPoint.y * 0.5 + 0.5) * _map.getCanvas().clientHeight;
  // 将屏幕像素坐标转换回经纬度
  const lngLat = _map.unproject([screenX, screenY]);
  console.log('手动计算的模型经纬度坐标（近似值）:', lngLat);
  return lngLat
}

/**
 * @description: 直接将 Three.js 模型的位置（本地坐标）转换为经纬度，不依赖于屏幕坐标。
 * @param {THREE.Vector3} modelPosition - 模型的 THREE.Vector3 位置。
 * @param {mapboxgl.LngLat} originLngLat - Three.js 世界原点 (0, 0, 0) 所对应的经纬度。
 * @param {Object} mapboxgl - 传入 mapboxgl 对象以解决未定义问题。
 * @returns {mapboxgl.LngLat} 返回一个包含经纬度和高度的 LngLat 对象。
 */
export const threePositionToLngLat = (modelPosition: THREE.Vector3, originLngLat = SceneConfig.getMapCenter()) => {
  if (!modelPosition || !originLngLat) {
    console.error('模型位置或原点经纬度数据缺失。');
    return null;
  }
  // 将度数转换为弧度
  const toRadians = (deg: number) => deg * Math.PI / 180;
  // 地球平均半径，单位米
  const earthRadius = 6371000;
  // 计算每米对应的经度变化量
  // 纬度变化量相对固定，经度变化量随纬度变化
  const latConversion = 1 / (earthRadius * (Math.PI / 180));
  const lonConversion = 1 / (earthRadius * (Math.PI / 180) * Math.cos(toRadians(originLngLat[0])));
  // 将 x 和 z 坐标转换为经度和纬度变化量
  const deltaLat = -modelPosition.z * latConversion;
  const deltaLon = modelPosition.x * lonConversion;
  const newLat = originLngLat[0] + deltaLat;
  const newLng = originLngLat[1] + deltaLon;
  const newAltitude = modelPosition.y; // Y轴直接代表高度
  return {
    lat: newLat,
    lng: newLng,
    height: newAltitude
  }
};

// 拆楼 - 开启
// models - 当前正常显示的室内模型数组
// 当前默认展示的 currentFloor 激活展示的室内模型
export const splitModelStart = (_shellModel: BuildingLODModel, _models: BuildingLODModel[], _currentFloor: number) => {
  const _map = win.mapApp.map;
  // 设置最大缩放比例
  lastMinZoom = _map.getMinZoom()
  _map.setMinZoom(currentMinZoom)
  const convert = new AccurateCoordinateConverter(
		SceneConfig.getMapCenter(),
		SceneConfig.getModelRotation(),
		SceneConfig.getBaseAltitude()
	)
  const coord = convert.worldToGeo(_shellModel.object.children[0].position)
  const _pitch = basePitch
  let _zoom = _map.getZoom();
  if (_zoom < 18) _zoom = 18
  _map.easeTo({
    center: [coord.longitude, coord.latitude],
    pitch: _pitch,
    zoom: _zoom,
    speed: 1, // 动画速度
    curve: 1.5 // 动画曲线
  });
  models = _models;
  // 给model 按照楼层排序
  models.sort((a,b) => a.floor! - b.floor!)
  currentFloor = _currentFloor;
  // 往 模型 中 塞入文字对象
  addLayerToThree()
  // 开始监听鼠标 \ 手指 事件
  addListenerSplit();
}

// 往three.js中添加 新对象 - mapbox 本身不支持高层 - 只能在three.js中加入数据并入高层操作
const addLayerToThree = () => {
  models = models.map(model => {
    // 创建 HTML 标签元素
    const labelEl = document.createElement('div');
    labelEl.id = model.id;
    labelEl.className = 'label-element';
    labelEl.innerHTML = `<p class="label-content">${model.floor}楼</p>`;
    document.body.appendChild(labelEl);
    return {
      ...model,
      labelEl
    }
  })
  resetHtml()
}

// 监听 - mapbox - canvas的手指 / 鼠标操作 - 手动实现
// 旋转 / 放大 / 缩小功能 / 上下平移切换
const addListenerSplit = () => {
  const _map = win.mapApp.map;
  const _canvasDom = _map.getCanvas();
  // web鼠标事件
  _canvasDom.addEventListener('mousedown', mousedown);
  // 监听鼠标移动事件
  _canvasDom.addEventListener('mousemove', mousemove);
  // 监听鼠标松开事件
  _canvasDom.addEventListener('mouseup', mouseup);
  // 监听鼠标滚轮事件
  _canvasDom.addEventListener('wheel', mouseWheel);

  // 额外的触摸事件支持，用于移动设备
  _canvasDom.addEventListener('touchstart', touchstart);
  _canvasDom.addEventListener('touchmove', touchmove);
  _canvasDom.addEventListener('touchend', touchend);

  // 楼层点击监听
  // exploded-camera-height-change
  eventBus.on('exploded-camera-height-change', synchronizationSelectFloor)
}

// 鼠标按下事件
const mousedown = (e: { button: number; clientX: any; clientY: any; }) => {
  const _map = win.mapApp.map;
  const _camera = win.mapApp.camera;
  if (e.button === 0) { // 鼠标左键
    isDragging = true;
    // 终止所有正在进行的 GSAP 动画，防止抖动
    gsap.killTweensOf(_obj);
    gsap.killTweensOf(_camera.position); // 停止相机 Y 轴的任何动画
    if (nextAnimationFlag) {
      _camera.position.y = nextCameraY
    }
    dragStart = { x: e.clientX, y: e.clientY };
    startBearing = _map.getBearing();
    cameraY = _camera.position.y
    const _index = models.findIndex(model => model.floor === currentFloor)
    _baseY = cameraY - _index * _baseHeight // 记录拖拽开始时base基础值
    dragStartTime = Date.now(); // 记录拖动开始时间
    totalDeltaX = 0; // 重置总移动距离
    totalDeltaY = 0;
    _obj.bearing = startBearing
  }
}

// 鼠标移动
const mousemove = (e: { clientX: number; clientY: number; }) => {
  const _map = win.mapApp.map;
  const _camera = win.mapApp.camera;
  if (!isDragging) return;
  // 更新总移动距离
  const deltaX = e.clientX - dragStart.x;
  const deltaY = e.clientY - dragStart.y;
  totalDeltaX = deltaX;
  totalDeltaY = deltaY;

  // 水平拖动（旋转）
  const newBearing = startBearing + deltaX * 0.1; // 0.1是旋转速度的系数
  // 使用 GSAP 平滑地更新相机方向和倾斜
  gsap.to(_obj, {
    bearing: newBearing,
    duration: 0.3,
    onUpdate: () => {
      _map.setBearing(_obj.bearing);
    },
    ease: 'power2.out'
    // 修改为 back.out 缓动函数，提供更强的动感
    // ease: 'back.out'
  });
  // 垂直拖动（Y轴移动）
  const currentZoom = _map.getZoom();
  const moveScale = 0.1; // 控制 Y 轴移动速度的系数
  const newCameraY = cameraY + deltaY * moveScale;
  // 使用 GSAP 平滑地更新相机 Y 轴位置，获得丝滑效果
  gsap.to(_camera.position, {
    y: newCameraY,
    duration: 0.3, // 动画时长
    ease: 'power2.out' // 缓动函数
  });
  const _currentModel = getLoadedFloorIndex()
  if (_currentModel != -1) {
    currentFloor = _currentModel.floor!
  }
  changeBuildSelect()
  // // 垂直拖动判断
  // const dragThreshold = 50 / currentZoom; // 拖动阈值，单位：像素
  // // 向下拖动，切换到下一个垂直层级
  // if (deltaY > dragThreshold) {
  // }
  // // 向上拖动，切换到上一个垂直层级
  // else if (deltaY < -dragThreshold) {
  // }
}

// 鼠标抬起
const mouseup = () => {
  if (isDragging) {
    isDragging = false;
    const _camera = win.mapApp.camera;
    const _index = models.findIndex(model => model.floor === currentFloor)
    const _y = _camera.position.y
    const dragDuration = (Date.now() - dragStartTime);
    const totalDistance = Math.sqrt(totalDeltaY * totalDeltaY);
    // 如果移动距离和时间都小于阈值，则判断为轻扫
    if (totalDistance > FLICK_DISTANCE_THRESHOLD && dragDuration < FLICK_TIME_THRESHOLD) {
    // 不用判断距离 - 只需要判断一下时间 间隔
    // if (dragDuration < FLICK_TIME_THRESHOLD) {
      // 判断垂直轻扫的方向，并应用不同的动画
      if (totalDeltaY > 0) {
        // 向下轻扫
        if (_index === models.length - 1) {
          // 恢复初始值
          nextCameraY = cameraY
        } else {
          // 切换上一个
          nextCameraY = cameraY + _baseHeight
          currentFloor = models[_index + 1].floor!;
        }
      } else if (totalDeltaY < 0) {
        // 向下轻扫
        if (_index === 0) {
          // 恢复初始值
          nextCameraY = cameraY
        } else {
          // 切换下一个
          nextCameraY = cameraY - _baseHeight
          currentFloor = models[_index - 1].floor!;
        }
      }
    } else {
      // if ((_index === 0 && _y < cameraY) || (_index === models.length - 1 && _y > cameraY)) {
      //   // 恢复初始值
      //   nextCameraY = cameraY
      // } else {
      //   // 判断当前拖动之后相机视角 是否已经达到其它楼层的水平
      //   const _currentModel = getLoadedFloorIndex()
      //   if (_currentModel != -1) {
      //     currentFloor = _currentModel.floor!
      //     nextCameraY = _currentModel.y
      //   }
      // }
      // 判断当前拖动之后相机视角 是否已经达到其它楼层的水平
      const _currentModel = getLoadedFloorIndex()
      if (_currentModel != -1) {
        currentFloor = _currentModel.floor!
        nextCameraY = _currentModel.y
      }
    }
    gsap.to(_camera.position, {
      y: nextCameraY,
      duration: 1, // 动画时长
      ease: 'power2.out', // 缓动函数
      onStart: () => {
        nextAnimationFlag = true
      },
      onComplete: () => {
        nextAnimationFlag = false
      }
    });
    changeBuildSelect()
  }
}

// 鼠标滚轮事件
const mouseWheel = (e: { preventDefault: () => void; deltaY: number; }) => {
  const _map = win.mapApp.map;
  // 阻止默认的滚轮行为
  e.preventDefault();
  // 停止当前任何正在进行的动画，然后立即开始新的动画
  _map.stop();
  // 获取当前缩放级别
  const currentZoom = _map.getZoom();
  // 根据滚轮方向计算新的缩放级别
  const newZoom = e.deltaY > 0 ? currentZoom - 1 : currentZoom + 1;
  // 获取鼠标位置并转换为经纬度，作为缩放的中心点
  // const center = map.unproject([e.clientX, e.clientY]);
  // 当前视角底图的中心点
  const center = _map.getCenter();
  // 使用 flyTo 实现平滑缩放
  _map.flyTo({
    center: center,
    zoom: newZoom,
    speed: 1.5, // 动画速度
    curve: 1.5 // 动画曲线
  });
}

// 触摸事件
const getTouchDistance = (touches: TouchList) => {
  const dx = touches[0].clientX - touches[1].clientX;
  const dy = touches[0].clientY - touches[1].clientY;
  return Math.sqrt(dx * dx + dy * dy);
}

// 手指按下事件
const touchstart = (e: TouchEvent) => {
  const _map = win.mapApp.map;
  const _camera = win.mapApp.camera;
  // 终止所有正在进行的 GSAP 动画，防止抖动
  gsap.killTweensOf(_obj);
  gsap.killTweensOf(_camera.position); // 停止相机 Y 轴的任何动画
  if (nextAnimationFlag) {
    _camera.position.y = nextCameraY
  }
  dragStartTime = Date.now(); // 记录拖动开始时间
  totalDeltaX = 0; // 重置总移动距离
  totalDeltaY = 0;

  if (e.touches.length === 1) { // 单指拖动
    isPinching = false;
    isDragging = true;
    const touch = e.touches[0];
    dragStart = { x: touch.clientX, y: touch.clientY };
    startBearing = _map.getBearing();
    cameraY = _camera.position.y;
    const _index = models.findIndex(model => model.floor === currentFloor);
    _baseY = cameraY - _index * _baseHeight;
    _obj.bearing = startBearing;
  } else if (e.touches.length === 2) { // 双指缩放
    isPinching = true;
    isDragging = false; // 禁用单指拖动
    initialPinchDistance = getTouchDistance(e.touches);
    // 记录初始触摸点的Y坐标，用于区分缩放和倾斜
    initialTouchY0 = e.touches[0].clientY;
    initialTouchY1 = e.touches[1].clientY;
  } else {
    isPinching = false;
    isDragging = false;
  }
}

// 手指移动
const touchmove = (e: TouchEvent) => {
  // e.preventDefault(); // 阻止浏览器默认的缩放行为
  const _map = win.mapApp.map;
  const _camera = win.mapApp.camera;
  if (e.touches.length === 1 && isDragging) { // 单指拖动
    const touch = e.touches[0];
    const deltaX = touch.clientX - dragStart.x;
    const deltaY = touch.clientY - dragStart.y;
    totalDeltaX = deltaX;
    totalDeltaY = deltaY;

    // 水平拖动（旋转）
    const newBearing = startBearing + deltaX * 0.5;
    gsap.to(_obj, {
      bearing: newBearing,
      duration: 0.3,
      onUpdate: () => {
        _map.setBearing(_obj.bearing);
      },
      ease: 'power2.out'
    });

    // 垂直拖动（Y轴移动）
    const moveScale = 0.1;
    const newCameraY = cameraY + deltaY * moveScale;
    gsap.to(_camera.position, {
      y: newCameraY,
      duration: 0.3,
      ease: 'power2.out'
    });
    const _currentModel = getLoadedFloorIndex()
    if (_currentModel != -1) {
      currentFloor = _currentModel.floor!
    }
    changeBuildSelect()
  } else if (e.touches.length === 2) { // 双指缩放
    const currentPinchDistance = getTouchDistance(e.touches);
    const currentY0 = e.touches[0].clientY;
    const currentY1 = e.touches[1].clientY;
    // 计算缩放变化量（手指距离变化）
    const pinchDelta = Math.abs(currentPinchDistance - initialPinchDistance);
    // 计算垂直滑动变化量（两个手指的Y轴平均移动量）
    const verticalSwipeDelta = Math.abs(((currentY0 + currentY1) / 2) - ((initialTouchY0 + initialTouchY1) / 2));
    // 定义一个阈值，用于判断手势的主导方向
    const GESTURE_THRESHOLD = 5;

    // 如果缩放变化量大于垂直移动量，且超过阈值，则执行自定义缩放
    if (pinchDelta > verticalSwipeDelta && pinchDelta > GESTURE_THRESHOLD) {
      e.preventDefault(); // 阻止浏览器默认的缩放行为，由我们自己处理
      const zoomDelta = (currentPinchDistance - initialPinchDistance) * 0.005;
      const currentZoom = _map.getZoom();
      const newZoom = currentZoom + zoomDelta;
      // 直接设置缩放级别，不需要动画，更跟手
      _map.setZoom(newZoom);
      initialPinchDistance = currentPinchDistance; // 更新初始距离
    }
    // 如果不是以缩放为主导，则不调用 preventDefault，让 Mapbox 处理倾斜
  }
}

// 手指松开
const touchend = () => {
  if (isPinching) {
    // 结束双指手势
    isPinching = false;
    return; // 结束逻辑，不需要执行下面的轻扫判断
  }
  if (isDragging) {
    isDragging = false;
    const _camera = win.mapApp.camera;
    const _index = models.findIndex(model => model.floor === currentFloor)
    const _y = _camera.position.y
    const dragDuration = (Date.now() - dragStartTime);
    // const totalDistance = Math.sqrt(totalDeltaX * totalDeltaX + totalDeltaY * totalDeltaY);
    const totalDistance = Math.sqrt(totalDeltaY * totalDeltaY);
    // 如果移动距离和时间都小于阈值，则判断为轻扫
    if (totalDistance > FLICK_DISTANCE_THRESHOLD && dragDuration < FLICK_TIME_THRESHOLD) {
      if (totalDeltaY > 0) {
        if (_index === models.length - 1) {
          nextCameraY = cameraY
        } else {
          nextCameraY = cameraY + _baseHeight
          currentFloor = models[_index + 1].floor!;
        }
      } else if (totalDeltaY < 0) {
        if (_index === 0) {
          nextCameraY = cameraY
        } else {
          nextCameraY = cameraY - _baseHeight
          currentFloor = models[_index - 1].floor!;
        }
      }
    } else {
      const _currentModel = getLoadedFloorIndex()
      if (_currentModel != -1) {
        currentFloor = _currentModel.floor!
        nextCameraY = _currentModel.y
      }
    }
    gsap.to(_camera.position, {
      y: nextCameraY,
      duration: 1,
      ease: 'power2.out',
      onStart: () => {
        nextAnimationFlag = true
      },
      onComplete: () => {
        nextAnimationFlag = false
      }
    });
    changeBuildSelect()
  }
}

// 判断当前是否已经进入到其它楼层
/**
 * @description: 根据相机Y轴位置，判断当前相机位于哪个楼层之间。
 * @param {number} _y - 相机的Y轴位置。
 * @param {Array<Object>} _models - 包含每个楼层模型信息的数组，按楼层顺序排列。
 * @returns {number} 返回当前相机所在的楼层索引。
 */
const getLoadedFloorIndex = () => {
  const _camera = win.mapApp.camera;
  const _y = _camera.position.y
  const _models = models.map((eop, _index) => ({
    ...eop,
    y: _baseY + _index * _baseHeight
  }))
  if (!_models || _models.length === 0) {
    return -1; // 或返回其他错误码
  }
  const currentIndex = _models.findIndex(model => model.floor === currentFloor);
  if (currentIndex === -1) {
    // 如果当前楼层未找到，返回默认第一层
    return _models[0];
  }
  const currentModel = _models[currentIndex];
  if (currentIndex === 0) {
    const nextModel = _models[currentIndex + 1];
    // 检查是否切换到上一层
    if (_y >= nextModel.y) return nextModel
  } else if (currentIndex === _models.length - 1) {
    const lastModel = _models[currentIndex - 1];
    // 检查是否切换到下一层
    if (_y <= lastModel.y) return lastModel
  } else {
    // 检查在上 ， 还是下
    const nextModel = _models[currentIndex + 1];
    const lastModel = _models[currentIndex - 1];
    if (_y >= nextModel.y) return nextModel
    if (_y <= lastModel.y) return lastModel
  }
  // 如果没有切换，返回当前楼层模型
  return currentModel;
};

// 同步 当前选中楼层
const synchronizationSelectFloor = (data: { targetFloor: number }) => {
  currentFloor = data.targetFloor
}

// 改变楼层的选中状态
const changeBuildSelect = () => {
  const _model = models.find(model => model.floor === currentFloor)
  if (_model) {
    eventBus.emit('lod-building-floor-changed', {
      buildingId: _model.buildingId,
      floor: currentFloor,
      isSplit: true
    })
  } 
}

// 重新渲染 html 元素
const resetHtml = () => {
  const _map = win.mapApp.map;
  const _camera = win.mapApp.camera;
  models.forEach(model => {
    const labelObject = model.object.children[0]
    const labelEl = model.labelEl
    // 获取模型相对坐标 转换为屏幕像素坐标
    const modelWorldPosition = new THREE.Vector3();
    labelObject!.getWorldPosition(modelWorldPosition);
    // 将 3D 坐标转换到地图平面上的 2D 像素
    // 这是一个简化版本，真正的转换需要更复杂的数学运算，但Mapbox本身提供了更易用的方法
    const projectedPoint = new THREE.Vector3();
    projectedPoint.setFromMatrixPosition(labelObject!.matrixWorld);
    projectedPoint.project(_camera);
    const screenX = (projectedPoint.x * 0.5 + 0.5) * _map.getCanvas().clientWidth;
    const screenY = (-projectedPoint.y * 0.5 + 0.5) * _map.getCanvas().clientHeight;
    // 更新 HTML 元素的位置，使其与 3D 对象同步
    labelEl!.style.left = `${screenX}px`;
    labelEl!.style.top = `${screenY}px`;
  })
  requestAnimationFrameTime = requestAnimationFrame(resetHtml)
}

// 移除监听 - 并且还原
const removeListenerSplit = () => {
  const _map = win.mapApp.map;
  const _canvasDom = win.mapApp.map.getCanvas();
  // web鼠标事件
  _canvasDom.removeEventListener('mousedown', mousedown)
  // 监听鼠标移动事件
  _canvasDom.removeEventListener('mousemove', mousemove);
  // 监听鼠标松开事件
  _canvasDom.removeEventListener('mouseup', mouseup);
  // 监听鼠标滚轮事件
  _canvasDom.removeEventListener('wheel', mouseWheel);

  // 移除触摸事件
  _canvasDom.removeEventListener('touchstart', touchstart);
  _canvasDom.removeEventListener('touchmove', touchmove);
  _canvasDom.removeEventListener('touchend', touchend);

  eventBus.off('exploded-camera-height-change', synchronizationSelectFloor)
}

// 拆楼 - 结束
export const splitModelEnd = () => {
  const _map = win.mapApp.map;
  // 还原
  _map.setMinZoom(lastMinZoom)
  cancelAnimationFrame(requestAnimationFrameTime!)
  // 移除 - label 上图
  models.forEach(model => {
    if (model.labelEl) document.body.removeChild(model.labelEl)
  })
  models = [];
  currentFloor = -1;
  // 结束监听
  removeListenerSplit();
}