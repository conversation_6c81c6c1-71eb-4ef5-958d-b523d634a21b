/**
 * Web传感器管理器
 * 适配Web浏览器环境的传感器数据采集和预处理模块
 * 将DeviceMotionEvent和DeviceOrientationEvent适配为统一格式
 */

export default class WebSensorManager {
  constructor(config = {}) {
    this.config = {
      sampleRate: config.sampleRate || 50,
      enableAccelerometer: true,
      enableGyroscope: true,
      enableMagnetometer: true,
      enableFiltering: config.enableFiltering !== false,
      filterWindow: config.filterWindow || 5,
      enableCalibration: config.enableCalibration !== false,
      ...config
    };
    
    // 传感器状态
    this.sensorStatus = {
      accelerometer: { available: false, active: false, lastData: null },
      gyroscope: { available: false, active: false, lastData: null },
      magnetometer: { available: false, active: false, lastData: null }
    };
    
    // 数据缓存
    this.dataBuffer = {
      accelerometer: [],
      gyroscope: [],
      magnetometer: []
    };
    
    // 校准参数
    this.calibration = {
      accelerometerBias: config.calibration?.accelerometerBias || [0, 0, 0],
      gyroscopeBias: config.calibration?.gyroscopeBias || [0, 0, 0],
      magnetometerBias: config.calibration?.magnetometerBias || [0, 0, 0],
      accelerometerScale: config.calibration?.accelerometerScale || [1, 1, 1],
      magneticDeclination: config.calibration?.magneticDeclination || 0
    };
    
    // 增强滤波器，使用更大的窗口和死区滤波
    this.filters = {
      accelerometer: new MovingAverageFilter(
        this.config.filterWindow || 8,        // 滤波窗口增大到8
        this.config.deadZone || 0.08,         // 加速计死区阈值
        this.config.noiseThreshold || 0.12    // 加速计噪声阈值
      ),
      gyroscope: new MovingAverageFilter(
        this.config.filterWindow || 6,        // 陀螺仪滤波窗口
        this.config.deadZone || 0.05,         // 陀螺仪死区阈值 
        this.config.noiseThreshold || 0.08    // 陀螺仪噪声阈值
      ),
      magnetometer: new MovingAverageFilter(
        this.config.filterWindow || 10,       // 磁力计需要更强的滤波
        this.config.deadZone || 0.1,          // 磁力计死区阈值
        this.config.noiseThreshold || 0.15    // 磁力计噪声阈值
      )
    };
    
    // 采样控制
    this.samplingInterval = null;
    this.lastSampleTime = 0;
    this.sampleInterval = 1000 / this.config.sampleRate;
    
    // 事件监听器引用
    this.motionListener = null;
    this.orientationListener = null;
    
    // 数据回调
    this.dataCallback = null;
    this.errorCallback = null;
    
    // 运行状态
    this.isRunning = false;
    this.isPaused = false;
    
    console.log('📡 Web传感器管理器初始化完成', {
      sampleRate: this.config.sampleRate,
      enableFiltering: this.config.enableFiltering
    });
  }
  
  /**
   * 检查传感器权限和可用性
   */
  async requestPermissions() {
    try {
      // 检查是否在HTTPS环境或localhost
      if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
        throw new Error('传感器访问需要HTTPS环境或localhost');
      }
      
      // 请求设备运动权限（iOS 13+需要）
      if (typeof DeviceMotionEvent !== 'undefined' && typeof DeviceMotionEvent.requestPermission === 'function') {
        const motionPermission = await DeviceMotionEvent.requestPermission();
        if (motionPermission !== 'granted') {
          throw new Error('设备运动权限被拒绝');
        }
      }
      
      // 请求设备方向权限（iOS 13+需要）
      if (typeof DeviceOrientationEvent !== 'undefined' && typeof DeviceOrientationEvent.requestPermission === 'function') {
        console.log('🔍 检测到iOS设备，请求设备方向权限...');
        const orientationPermission = await DeviceOrientationEvent.requestPermission();
        console.log('🔍 权限请求结果:', orientationPermission);
        if (orientationPermission !== 'granted') {
          console.error('❌ 设备方向权限被拒绝！这会导致航向角始终为0');
          console.log('💡 解决方案: Safari设置 → 网站设置 → 开启"运动与方向访问"');
        } else {
          console.log('✅ 设备方向权限已获取');
        }
      } else {
        console.log('🔍 非iOS设备或旧版本，无需请求权限');
      }
      
      return true;
    } catch (error) {
      console.error('❌ 传感器权限请求失败:', error);
      throw error;
    }
  }
  
  /**
   * 启动传感器采集
   */
  async start(config = {}) {
    if (this.isRunning) {
      console.warn('⚠️ 传感器管理器已在运行');
      return;
    }
    
    try {
      console.log('🚀 启动Web传感器采集...');
      
      // 请求权限
      await this.requestPermissions();
      
      // 更新配置
      this.updateConfig(config);
      
      // 检查传感器可用性
      await this.checkSensorAvailability();
      
      // 启动传感器监听
      this.startDeviceMotionListener();
      this.startDeviceOrientationListener();
      
      // 启动数据采样
      this.startSampling();
      
      this.isRunning = true;
      console.log('✅ Web传感器采集启动成功');
      
    } catch (error) {
      console.error('❌ 传感器启动失败:', error);
      this.handleError('sensor_start_failed', error);
      throw error;
    }
  }
  
  /**
   * 停止传感器采集
   */
  stop() {
    if (!this.isRunning) {
      return;
    }
    
    console.log('⏹️ 停止Web传感器采集');
    
    // 停止采样
    this.stopSampling();
    
    // 移除事件监听器
    this.stopDeviceMotionListener();
    this.stopDeviceOrientationListener();
    
    // 清空缓存
    this.clearBuffers();
    
    this.isRunning = false;
    this.isPaused = false;
  }
  
  /**
   * 检查传感器可用性
   */
  async checkSensorAvailability() {
    // 检查DeviceMotionEvent支持
    if (typeof DeviceMotionEvent !== 'undefined') {
      this.sensorStatus.accelerometer.available = true;
      this.sensorStatus.gyroscope.available = true;
    } else {
      console.warn('⚠️ 浏览器不支持DeviceMotionEvent');
    }

    // 检查DeviceOrientationEvent支持
    if (typeof DeviceOrientationEvent !== 'undefined') {
      this.sensorStatus.magnetometer.available = true;
    } else {
      console.warn('⚠️ 浏览器不支持DeviceOrientationEvent');
    }

    console.log('📊 传感器可用性检查完成:', this.sensorStatus);
  }
  
  /**
   * 启动设备运动监听器（加速计+陀螺仪）
   */
  startDeviceMotionListener() {
    if (!this.sensorStatus.accelerometer.available && !this.sensorStatus.gyroscope.available) {
      console.warn('⚠️ 设备运动传感器不可用');
      return;
    }
    
    this.motionListener = (event) => {
      if (this.isPaused) return;
      
      const timestamp = Date.now();
      
      // 处理加速计数据（包含重力）
      if (event.accelerationIncludingGravity) {
        const acc = event.accelerationIncludingGravity;
        const calibratedAcc = this.calibrateAccelerometer({
          x: acc.x || 0,
          y: acc.y || 0,
          z: acc.z || 0
        });
        
        this.dataBuffer.accelerometer.push({
          ...calibratedAcc,
          timestamp
        });
        
        this.sensorStatus.accelerometer.lastData = calibratedAcc;
        this.sensorStatus.accelerometer.active = true;
      }
      
      // 处理陀螺仪数据
      if (event.rotationRate) {
        const gyro = event.rotationRate;
        const calibratedGyro = this.calibrateGyroscope({
          x: gyro.alpha || 0,  // 绕Z轴旋转
          y: gyro.beta || 0,   // 绕X轴旋转
          z: gyro.gamma || 0   // 绕Y轴旋转
        });
        
        this.dataBuffer.gyroscope.push({
          ...calibratedGyro,
          timestamp
        });
        
        this.sensorStatus.gyroscope.lastData = calibratedGyro;
        this.sensorStatus.gyroscope.active = true;
      }
      
      // 限制缓存大小
      ['accelerometer', 'gyroscope'].forEach(sensor => {
        if (this.dataBuffer[sensor].length > 100) {
          this.dataBuffer[sensor].shift();
        }
      });
    };
    
    window.addEventListener('devicemotion', this.motionListener, { passive: true });
    console.log('✅ 设备运动监听器启动成功');
  }
  
  /**
   * 启动设备方向监听器（磁力计/罗盘）
   */
  startDeviceOrientationListener() {
    if (!this.sensorStatus.magnetometer.available) {
      console.warn('⚠️ 设备方向传感器不可用');
      return;
    }

    // 检测设备平台和浏览器（基于参考文件的最佳实践）
    const userAgent = navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    const isAndroid = /Android/.test(userAgent);
    const isChrome = /Chrome/.test(userAgent) && !/Edge/.test(userAgent);

    // 提取Chrome版本号
    const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
    const chromeVersion = chromeMatch ? parseInt(chromeMatch[1]) : null;

    // 惯导系统职责：只输出标准的地理航向角
    // 0°=北，90°=东，180°=南，270°=西
    // 场景校准由NavigationManager负责

    console.log('🔍 设备平台检测:', {
      isIOS,
      isAndroid,
      isChrome,
      chromeVersion,
      说明: '惯导系统输出标准地理航向角，无场景校准'
    });

    if (isIOS) {
      // iOS设备：参考ios-webkit-compass-demo.html的实现
      console.log('📱 iOS设备：使用webkitCompassHeading获取绝对地理方向');

      this.orientationListener = (event) => {
        if (this.isPaused) return;
        const timestamp = Date.now();

        // 确定最佳的方向数据源和值（参考demo文件的逻辑）
        let heading = null;
        let dataSource = 'unknown';
        let isAbsolute = false;
        let accuracy = null;

        // iOS 设备：直接使用 webkitCompassHeading (已经是绝对地理方向)
        if (typeof event.webkitCompassHeading === 'number') {
          heading = event.webkitCompassHeading;
          dataSource = 'webkit_compass';
          isAbsolute = true;
          accuracy = event.webkitCompassAccuracy;

          console.log('🧭 iOS使用webkitCompassHeading:', heading.toFixed(2) + '° (精度: ' + accuracy + ')');

        // 标准设备：检查 absolute 属性
        } else if (event.absolute === true && event.alpha !== null) {
          heading = event.alpha;
          dataSource = 'native_absolute';
          isAbsolute = true;

          console.log('🧭 iOS使用原生绝对方向:', heading.toFixed(2) + '°');

        // 降级：相对方向（需要校准）
        } else if (event.alpha !== null) {
          heading = event.alpha;
          dataSource = 'relative';
          isAbsolute = false;

          console.log('🧭 iOS使用相对方向:', heading.toFixed(2) + '° (需要校准)');
        }

        if (heading !== null) {
          console.log('🧭 iOS标准地理航向:', {
            地理航向: heading.toFixed(2) + '°',
            数据源: dataSource,
            是否绝对: isAbsolute,
            说明: '输出标准地理坐标系航向角'
          });

          const magnetometerData = {
            x: Math.cos((heading - 90) * Math.PI / 180),
            y: Math.sin((heading - 90) * Math.PI / 180),
            z: 0,
            direction: heading, // 标准地理航向角，无场景补偿
            beta: event.beta || 0,
            gamma: event.gamma || 0,
            accuracy: accuracy || dataSource,
            dataSource: dataSource,
            isAbsolute: isAbsolute
          };

          this.dataBuffer.magnetometer.push({
            ...magnetometerData,
            timestamp
          });

          this.sensorStatus.magnetometer.lastData = magnetometerData;
          this.sensorStatus.magnetometer.active = true;

          // 限制缓存大小
          if (this.dataBuffer.magnetometer.length > 100) {
            this.dataBuffer.magnetometer.shift();
          }
        }
      };

      window.addEventListener('deviceorientation', this.orientationListener, { passive: true });

    } else if (isAndroid && isChrome && chromeVersion >= 50) {
      // Android Chrome 50+：参考android-absolute-orientation-test.html的实现
      console.log('🤖 Android Chrome ' + chromeVersion + '：优先使用deviceorientationabsolute事件');

      // 检测API支持
      const hasAbsolute = 'ondeviceorientationabsolute' in window;
      const hasStandard = 'ondeviceorientation' in window;

      console.log('🔧 API支持检测:', { hasStandard, hasAbsolute });

      this.orientationListener = (event) => {
        if (this.isPaused) return;
        const timestamp = Date.now();

        // 验证事件类型和数据有效性（参考demo文件的验证逻辑）
        if (event.type === 'deviceorientationabsolute' && event.alpha !== null) {
          console.log('🧭 Android标准地理航向:', {
            地理航向: event.alpha.toFixed(2) + '°',
            absolute: event.absolute,
            eventType: event.type,
            说明: '输出标准地理坐标系航向角'
          });

          const magnetometerData = {
            x: Math.cos((-event.alpha - 90) * Math.PI / 180),
            y: Math.sin((-event.alpha - 90) * Math.PI / 180),
            z: 0,
            direction: - event.alpha, // 标准地理航向角，无场景补偿
            beta: event.beta || 0,
            gamma: event.gamma || 0,
            accuracy: 'absolute',
            dataSource: 'deviceorientationabsolute',
            isAbsolute: true
          };

          this.dataBuffer.magnetometer.push({
            ...magnetometerData,
            timestamp
          });

          this.sensorStatus.magnetometer.lastData = magnetometerData;
          this.sensorStatus.magnetometer.active = true;

          // 限制缓存大小
          if (this.dataBuffer.magnetometer.length > 100) {
            this.dataBuffer.magnetometer.shift();
          }
        } else {
          console.warn('🤖 Android absolute事件数据无效:', {
            type: event.type,
            alpha: event.alpha,
            absolute: event.absolute
          });
        }
      };

      if (hasAbsolute) {
        window.addEventListener('deviceorientationabsolute', this.orientationListener, { passive: true });
        console.log('✅ Android deviceorientationabsolute监听器启动成功');
      } else {
        console.warn('⚠️ 设备不支持deviceorientationabsolute，降级使用deviceorientation');
        window.addEventListener('deviceorientation', this.orientationListener, { passive: true });
      }

    } else {
      // 其他设备：使用标准deviceorientation事件
      console.log('💻 其他设备：使用标准deviceorientation事件');

      this.orientationListener = (event) => {
        if (this.isPaused) return;
        const timestamp = Date.now();

        if (event.alpha !== null && event.alpha !== undefined) {
          console.log('🧭 其他设备标准地理航向:', {
            地理航向: event.alpha.toFixed(2) + '°',
            absolute: event.absolute,
            说明: '输出标准地理坐标系航向角'
          });

          const magnetometerData = {
            x: Math.cos((-event.alpha - 90) * Math.PI / 180),
            y: Math.sin((-event.alpha - 90) * Math.PI / 180),
            z: 0,
            direction: -event.alpha, // 标准地理航向角，无场景补偿
            beta: event.beta || 0,
            gamma: event.gamma || 0,
            accuracy: event.absolute ? 'absolute' : 'relative',
            dataSource: 'deviceorientation',
            isAbsolute: event.absolute || false
          };

          this.dataBuffer.magnetometer.push({
            ...magnetometerData,
            timestamp
          });

          this.sensorStatus.magnetometer.lastData = magnetometerData;
          this.sensorStatus.magnetometer.active = true;

          // 限制缓存大小
          if (this.dataBuffer.magnetometer.length > 100) {
            this.dataBuffer.magnetometer.shift();
          }
        }
      };

      window.addEventListener('deviceorientation', this.orientationListener, { passive: true });
    }

    console.log('✅ 设备方向监听器启动成功');
  }
  
  /**
   * 停止设备运动监听器
   */
  stopDeviceMotionListener() {
    if (this.motionListener) {
      window.removeEventListener('devicemotion', this.motionListener);
      this.motionListener = null;
      this.sensorStatus.accelerometer.active = false;
      this.sensorStatus.gyroscope.active = false;
    }
  }
  
  /**
   * 停止设备方向监听器
   */
  stopDeviceOrientationListener() {
    if (this.orientationListener) {
      window.removeEventListener('deviceorientation', this.orientationListener);
      this.orientationListener = null;
      this.sensorStatus.magnetometer.active = false;
    }
  }
  
  /**
   * 启动数据采样
   */
  startSampling() {
    this.samplingInterval = setInterval(() => {
      if (this.isPaused || !this.isRunning) return;
      
      const now = Date.now();
      if (now - this.lastSampleTime < this.sampleInterval) {
        return;
      }
      
      this.lastSampleTime = now;
      
      // 采集当前传感器数据
      const sensorData = this.collectCurrentData();
      
      // 触发数据回调
      if (this.dataCallback && sensorData) {
        this.dataCallback(sensorData);
      }
      
    }, this.sampleInterval);
  }
  
  /**
   * 停止数据采样
   */
  stopSampling() {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
      this.samplingInterval = null;
    }
  }
  
  /**
   * 收集当前传感器数据
   */
  collectCurrentData() {
    const timestamp = Date.now();

    // 检查是否有有效数据
    if (!this.sensorStatus.accelerometer.lastData &&
        !this.sensorStatus.gyroscope.lastData) {
      return null;
    }

    const rawData = {
      timestamp,
      accelerometer: this.sensorStatus.accelerometer.lastData,
      gyroscope: this.sensorStatus.gyroscope.lastData,
      magnetometer: this.sensorStatus.magnetometer.lastData
    };

    // 应用滤波
    let processedData = rawData;
    if (this.config.enableFiltering) {
      processedData = this.applyFiltering(rawData);
    }

    return processedData;
  }
  
  /**
   * 应用数据滤波
   */
  applyFiltering(data) {
    const filtered = { ...data };
    
    if (data.accelerometer) {
      filtered.accelerometer = this.filters.accelerometer.filter(data.accelerometer);
    }
    
    if (data.gyroscope) {
      filtered.gyroscope = this.filters.gyroscope.filter(data.gyroscope);
    }
    
    if (data.magnetometer) {
      filtered.magnetometer = this.filters.magnetometer.filter(data.magnetometer);
    }
    
    return filtered;
  }
  
  /**
   * 加速计校准
   */
  calibrateAccelerometer(data) {
    const bias = this.calibration.accelerometerBias;
    const scale = this.calibration.accelerometerScale;
    
    return {
      x: (data.x - bias[0]) * scale[0],
      y: (data.y - bias[1]) * scale[1],
      z: (data.z - bias[2]) * scale[2]
    };
  }
  
  /**
   * 陀螺仪校准
   */
  calibrateGyroscope(data) {
    const bias = this.calibration.gyroscopeBias;
    
    return {
      x: data.x - bias[0],
      y: data.y - bias[1],
      z: data.z - bias[2]
    };
  }
  
  /**
   * 磁力计校准
   */
  calibrateMagnetometer(data) {
    // 应用磁偏角校正
    const declination = this.calibration.magneticDeclination;
    const correctedDirection = (data.direction + declination + 360) % 360;
    
    return {
      ...data,
      direction: correctedDirection,
      x: Math.cos((correctedDirection - 90) * Math.PI / 180),
      y: Math.sin((correctedDirection - 90) * Math.PI / 180)
    };
  }
  


  /**
   * 暂停/恢复
   */
  pause() {
    this.isPaused = true;
  }
  
  resume() {
    this.isPaused = false;
  }
  
  /**
   * 清空数据缓存
   */
  clearBuffers() {
    this.dataBuffer.accelerometer = [];
    this.dataBuffer.gyroscope = [];
    this.dataBuffer.magnetometer = [];
  }
  
  /**
   * 设置数据回调
   */
  setDataCallback(callback) {
    this.dataCallback = callback;
  }
  
  /**
   * 设置错误回调
   */
  setErrorCallback(callback) {
    this.errorCallback = callback;
  }
  
  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    
    // 更新采样间隔
    if (config.sampleRate) {
      this.sampleInterval = 1000 / config.sampleRate;
    }
    
    // 更新校准参数
    if (config.calibration) {
      this.calibration = { ...this.calibration, ...config.calibration };
    }
    
    // 更新滤波窗口
    if (config.filterWindow) {
      this.filters.accelerometer.setWindowSize(config.filterWindow);
      this.filters.gyroscope.setWindowSize(config.filterWindow);
      this.filters.magnetometer.setWindowSize(config.filterWindow);
    }
  }
  
  /**
   * 获取传感器状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      sensors: { ...this.sensorStatus },
      calibration: { ...this.calibration },
      bufferSizes: {
        accelerometer: this.dataBuffer.accelerometer.length,
        gyroscope: this.dataBuffer.gyroscope.length,
        magnetometer: this.dataBuffer.magnetometer.length
      }
    };
  }
  
  /**
   * 错误处理
   */
  handleError(type, error) {
    const errorInfo = {
      type,
      message: error.message || error,
      timestamp: Date.now(),
      sensorStatus: this.sensorStatus
    };
    
    if (this.errorCallback) {
      this.errorCallback(errorInfo);
    }
    
    console.error('Web传感器管理器错误:', errorInfo);
  }
}

/**
 * 增强移动平均滤波器
 * 包含死区滤波和噪声抑制功能
 */
class MovingAverageFilter {
  constructor(windowSize = 8, deadZone = 0.05, noiseThreshold = 0.1) {
    this.windowSize = windowSize;
    this.deadZone = deadZone;           // 死区阈值，小变化将被忽略
    this.noiseThreshold = noiseThreshold; // 噪声阈值
    
    this.buffer = {
      x: [],
      y: [],
      z: []
    };
    
    this.lastOutput = { x: 0, y: 0, z: 0 };
    this.staticCount = 0; // 静止计数器
    this.outlierBuffer = {
      x: [],
      y: [],
      z: []
    };
  }
  
  filter(data) {
    if (!data) return data;
    
    // 先应用离群值检测和处理
    const cleanedData = this.removeOutliers(data);
    
    // 应用死区滤波
    const deadZoneFiltered = this.applyDeadZone(cleanedData);
    
    // 添加到滑动窗口
    this.buffer.x.push(deadZoneFiltered.x);
    this.buffer.y.push(deadZoneFiltered.y);
    this.buffer.z.push(deadZoneFiltered.z);
    
    // 限制缓存大小
    if (this.buffer.x.length > this.windowSize) {
      this.buffer.x.shift();
      this.buffer.y.shift();
      this.buffer.z.shift();
    }
    
    // 计算加权平均值（最近的数据权重更高）
    const weightedAvg = this.calculateWeightedAverage();
    
    // 应用噪声抑制
    const noiseFiltered = this.suppressNoise(weightedAvg);
    
    // 更新最后输出
    this.lastOutput = noiseFiltered;
    
    // 保留其他属性
    return { ...data, ...noiseFiltered };
  }
  
  /**
   * 移除离群值
   */
  removeOutliers(data) {
    const { x, y, z } = data;
    
    // 将数据添加到离群值缓冲区
    this.outlierBuffer.x.push(x);
    this.outlierBuffer.y.push(y);
    this.outlierBuffer.z.push(z);
    
    // 保持缓冲区大小为5
    const outlierWindowSize = 5;
    if (this.outlierBuffer.x.length > outlierWindowSize) {
      this.outlierBuffer.x.shift();
      this.outlierBuffer.y.shift();
      this.outlierBuffer.z.shift();
    }
    
    // 如果缓冲区数据不足，直接返回
    if (this.outlierBuffer.x.length < 3) {
      return data;
    }
    
    // 检测并修正离群值
    const result = {
      x: this.correctOutlier(this.outlierBuffer.x, x),
      y: this.correctOutlier(this.outlierBuffer.y, y),
      z: this.correctOutlier(this.outlierBuffer.z, z)
    };
    
    return result;
  }
  
  /**
   * 修正单轴离群值
   */
  correctOutlier(buffer, currentValue) {
    const sorted = [...buffer].sort((a, b) => a - b);
    const median = sorted[Math.floor(sorted.length / 2)];
    const mad = this.calculateMAD(buffer, median); // 绝对中位偏差
    
    // 如果当前值偏离中位数超过3倍MAD，认为是离群值
    const threshold = 3 * mad;
    if (Math.abs(currentValue - median) > threshold && threshold > 0.1) {
      // 用中位数替换离群值
      return median;
    }
    
    return currentValue;
  }
  
  /**
   * 计算绝对中位偏差 (Median Absolute Deviation)
   */
  calculateMAD(values, median) {
    const deviations = values.map(v => Math.abs(v - median));
    deviations.sort((a, b) => a - b);
    return deviations[Math.floor(deviations.length / 2)];
  }
  
  /**
   * 应用死区滤波
   */
  applyDeadZone(data) {
    const result = { ...data };
    
    // 计算与上次输出的变化
    const deltaX = Math.abs(data.x - this.lastOutput.x);
    const deltaY = Math.abs(data.y - this.lastOutput.y);
    const deltaZ = Math.abs(data.z - this.lastOutput.z);
    
    // 应用死区：小变化保持上次值
    if (deltaX < this.deadZone) result.x = this.lastOutput.x;
    if (deltaY < this.deadZone) result.y = this.lastOutput.y;
    if (deltaZ < this.deadZone) result.z = this.lastOutput.z;
    
    // 检查是否处于静止状态
    const totalChange = deltaX + deltaY + deltaZ;
    if (totalChange < this.deadZone * 2) {
      this.staticCount++;
      
      // 如果连续多次变化很小，进一步减少输出变化
      if (this.staticCount > 5) {
        const staticFactor = 0.1; // 静止时的变化系数
        result.x = this.lastOutput.x + (result.x - this.lastOutput.x) * staticFactor;
        result.y = this.lastOutput.y + (result.y - this.lastOutput.y) * staticFactor;
        result.z = this.lastOutput.z + (result.z - this.lastOutput.z) * staticFactor;
      }
    } else {
      this.staticCount = Math.max(0, this.staticCount - 1);
    }
    
    return result;
  }
  
  /**
   * 计算加权平均值
   */
  calculateWeightedAverage() {
    const length = this.buffer.x.length;
    if (length === 0) return { x: 0, y: 0, z: 0 };
    
    let weightedX = 0, weightedY = 0, weightedZ = 0;
    let totalWeight = 0;
    
    // 线性权重：最新数据权重最高
    for (let i = 0; i < length; i++) {
      const weight = (i + 1) / length; // 权重从1/n到1
      weightedX += this.buffer.x[i] * weight;
      weightedY += this.buffer.y[i] * weight;
      weightedZ += this.buffer.z[i] * weight;
      totalWeight += weight;
    }
    
    return {
      x: weightedX / totalWeight,
      y: weightedY / totalWeight,
      z: weightedZ / totalWeight
    };
  }
  
  /**
   * 噪声抑制
   */
  suppressNoise(data) {
    const result = { ...data };
    
    // 对于非常小的值，进一步压制
    ['x', 'y', 'z'].forEach(axis => {
      if (Math.abs(result[axis]) < this.noiseThreshold) {
        result[axis] *= 0.3; // 将小噪声降到30%
      }
    });
    
    return result;
  }
  
  setWindowSize(size) {
    this.windowSize = Math.max(3, Math.min(20, size)); // 限制窗口大小在3-20之间
    
    // 截断缓存
    if (this.buffer.x.length > this.windowSize) {
      this.buffer.x = this.buffer.x.slice(-this.windowSize);
      this.buffer.y = this.buffer.y.slice(-this.windowSize);
      this.buffer.z = this.buffer.z.slice(-this.windowSize);
    }
  }
  
  /**
   * 设置死区阈值
   */
  setDeadZone(deadZone) {
    this.deadZone = Math.max(0, Math.min(0.5, deadZone)); // 限制死区在0-0.5之间
  }
  
  /**
   * 设置噪声阈值
   */
  setNoiseThreshold(threshold) {
    this.noiseThreshold = Math.max(0, Math.min(1.0, threshold)); // 限制阈值在0-1之间
  }
  
  reset() {
    this.buffer.x = [];
    this.buffer.y = [];
    this.buffer.z = [];
    this.outlierBuffer.x = [];
    this.outlierBuffer.y = [];
    this.outlierBuffer.z = [];
    this.lastOutput = { x: 0, y: 0, z: 0 };
    this.staticCount = 0;
  }
}