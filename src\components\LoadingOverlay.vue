<template>
  <div v-if="visible" class="loading-overlay">
    <div class="loading-content">
      <!-- <div class="loading-spinner"></div> -->
      <van-image :src="loadingUrl" class="loading-img"></van-image>

      <div class="loading-progress">


        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
        <span class="progress-text">{{ progress.toFixed(1) }}%</span>
      </div>
      <div class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import loadingUrl from "@/assets/image/loading.gif"
interface Props {
  visible: boolean
  text: string
  progress: number
}

defineProps<Props>()
</script>

<style scoped lang="scss">
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: #94632D;
}

.loading-content {
  text-align: center;
  padding: 0 2rem;
  border-radius: 8px;
  background: url(@/assets/image/loading-container.svg) no-repeat center center;
  background-size: 100% 100%;

  // backdrop-filter: blur(10px);
  width: 188px;
  height: 188px;
  position: relative;
  bottom: 10%;

  .loading-img {
    position: absolute;
    width: 85%;
    left: 50%;
    transform: translateX(-50%);
    top: 0px;
  }
}

/* 
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
} */

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 12px;
  color: #633B0F;

}

.loading-progress {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  margin-top: 124px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #AC391E;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  min-width: 50px;
  margin-left: 8px;
  font-size: 10px;
}
</style>
